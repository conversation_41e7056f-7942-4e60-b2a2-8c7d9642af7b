# UX设计师

## 岗位职责

1. 进行用户研究与可用性测试，挖掘用户需求和痛点，为产品优化提供数据支持，提升用户满意度和产品可用性。  
2. 设计用户流程和交互原型，产出高质量的交互方案，确保产品界面直观易用且符合用户行为习惯。  
3. 与产品经理和开发团队协作推进设计落地，确保设计方案在开发过程中的高质量实现。  
4. 制定和维护设计规范与组件库，保障产品体验一致性，提升设计与开发效率。

## 任职要求

### 必备技能

1. 熟练掌握用户体验设计原则与方法，具备扎实的用户研究和信息架构能力。  
2. 精通Figma/Sketch等主流UX设计工具，能够高效产出高保真原型与交互方案。  
3. 熟悉设计交付流程与开发协作规范，能够清晰表达设计意图并与技术团队顺畅沟通。  
4. 良好的信息架构与交互设计能力，能够独立完成从需求分析到设计落地的全流程工作。

### 优先技能

1. 掌握用户行为分析工具（如Mixpanel、Hotjar），能通过数据驱动设计优化。  
2. 具备A/B测试与数据驱动设计的能力，能够基于实验结果持续优化用户体验。  
3. 能够撰写清晰的设计文档与用户指南，确保设计意图被准确理解和传达。

### 加分项

1. 拥有知名互联网公司（如BAT、TikTok、Meta）实习或工作背景，具备成熟项目经验。  
2. 拥有完整上线项目的案例作品集，能够展示从研究到落地的完整设计成果。  
3. 有设计相关领域的学术论文发表或竞赛获奖，体现专业深度与研究能力。

## 我们提供

1. 有竞争力的薪资待遇：15-30K·15薪，根据能力面议。  
2. 良好的发展空间和学习成长机会，参与高影响力项目。  
3. 弹性工作制度与开放的团队文化，支持远程办公与个人成长。  
4. 丰富的设计资源支持与跨部门协作机会，助力职业能力全面提升。

**基本信息**

- 学历要求：本科及以上学历
- 工作经验：3-5年相关经验
- 薪资范围：15-30K·15薪
- 工作地点：北京
- 工作方式：现场办公（支持部分远程）