```json
{
  "个人信息": {
    "姓名": "林先生",
    "年龄": "31岁",
    "性别": "男",
    "学历": "本科",
    "联系方式": "18602059478"
  },
  "求职意向": {
    "职位": "产品经理",
    "行业方向": "医疗行业",
    "期望薪资": "面议"
  },
  "工作经历": [
    {
      "公司名称": "珠海金利时代科技股份有限公司",
      "职位": "高级产品经理",
      "时间": "2019.01-2020.08",
      "项目描述": [
        "负责某药品的后端系统开发、API程序、流程管理等。",
        "产品线: 电商产品，岗位职责: 用户研究"
      ]
    },
    {
      "公司名称": "平安健康社区",
      "职位": "产品经理",
      "时间": "2021.01-2022.06",
      "项目描述": [
        "1. 负责社区相关业务模块设计、功能实现及优化",
        "2. 敏捷迭代",
        "3. 流程管理",
        "产品线: 医疗产品，岗位职责: 用户研究"
      ]
    },
    {
      "公司名称": "weidai稳健医疗钉钉平台",
      "职位": "产品经理",
      "时间": "2020.01-2020.06",
      "项目描述": [
        "项目背景：针对企业客户的需求，打造一款以小程序为载体、满足分级医院需求的产品展示与销售工具，提升企业生活品质与用户体验。",
        "岗位职责: 产品设计、内容运营、流程管理、项目管理"
      ]
    },
    {
      "公司名称": "平安健康养老社区",
      "职位": "产品助理",
      "时间": "2021.04-2021.06",
      "项目描述": [
        "1. 参与社区相关业务模块设计、功能实现及优化",
        "2. 敏捷迭代",
        "产品线: 医疗产品，岗位职责: 用户研究"
      ]
    },
    {
      "公司名称": "H+医疗系统",
      "职位": "产品经理",
      "时间": "2021.04-2021.06",
      "项目描述": [
        "项目背景：医院现有HIS系统版本低于1.0版本，结合当前收费软件集成人工操作、收费数据录入等问题，任务",
        "1. 关键问题分析，收集并整理系统的现状和需求，完善了将门诊收费系统与内部报销机制、符合医保协议约定内容对接、与财务费用管理系统对接",
        "2. 同台区化后的管理统计",
        "3. 社区医疗业务系统建设，医生使用电子处方进行认证开药资格、权限校验的功能实现",
        "4. 社区医疗业务系统建设，医生使用电子处方进行认证开药资格、权限校验的功能实现",
        "5. 定期对产品的用户反馈进行跟踪、改进和优化"
      ]
    },
    {
      "公司名称": "平安健康医养系统",
      "职位": "产品经理",
      "时间": "2020.08-2020.12",
      "项目描述": [
        "大数据驱动的医养产品创新与医养资产产业化",
        "1. 主导策划、设计、开发、产品落地推广的B端营销平台/平台功能配置、基础功能参数、各类营销策略等，并配合各省市分公司开展市场调研与竞品分析",
        "2. 深入理解医养业务场景，积极参与产品功能的设计，确保产品统一的数据交互方式，并实现数据的闭环应用，打通医养业务之间的壁垒，提高医养服务的可实现性",
        "3. 通过引入第三方SaaS公司的解决方案，对现有医养服务平台进行改造，驱动业务流程再造、权限设置优化与安全管控"
      ]
    },
    {
      "公司名称": "对码健康险",
      "职位": "产品经理",
      "时间": "2022.06-2023.01",
      "项目描述": [
        "1. 需求分析与文档编写处理",
        "2. 进行需求评审与风险评估",
        "3. 建立健康险产品体系（三档）, 推动上线机制，落实敏捷化，关注中文动态的撰写与人工干预的发生率",
        "4. 负责团队知识能力的培养，支持全省人员在具体实施过程中遇到的问题，如技术问题、关键决策经验等",
        "5. 监督健康险产品上线前质量的审核，作为从方案撰写、原型制作到测试的需求方，确保需求合法合规"
      ]
    },
    {
      "公司名称": "对码健康险",
      "职位": "产品经理",
      "时间": "2023.08-2023.12",
      "项目描述": [
        "1. 中后台系统建设规划的拟订与调整，根据实际运行情况，反向调整等环节，综合AI技术与传统业务的衔接整合，日均自动化出单量突破1万次",
        "2. 实现长尾市场的突破，通过结构化→非结构化→拼接匹配→形成资金流，日均处理量提升至高峰时环比增长53%",
        "3. 联合腾讯云搭建CDN加速，构建私有云架构，实现APP+桌面端双模式，全链路加密保障，公众号功能优化，日均访问量达10万次",
        "4. 成功推动入驻蚂蚁集团生态体系，接入（蓝盾 Med EHR + Faxis 国家版）、携手西马与疾控院合作22%，扩大业务工",
        "5. 活跃用户的持续稳定发展，实现月活跃用户数、新增用户、留存率及复投率显著，确保核心内容的可持续发展",
        "6. 电信增值服务金融产品的孵化，引入了移动中保、企信宝等渠道，Alvin中保客流量激增，先于计划目标提前完成"
      ]
    }
  ],
  "教育背景": {
    "学校": "中央财经大学",
    "专业": "工商管理",
    "学历": "本科",
    "毕业时间": "2016.06"
  },
  "技能": [
    "熟练掌握Python、Java、C++、SQL等编程语言",
    "熟悉MySQL、Oracle、MongoDB等数据库",
    "具有良好的沟通协调能力和团队协作精神",
    "具备较强的学习能力和适应能力",
    "熟悉敏捷开发流程，能够独立完成产品需求分析、设计、开发、测试等工作",
    "具有较强的项目管理能力，能够有效地组织和协调项目团队完成项目目标",
    "熟悉数据分析和挖掘，能够运用数据分析工具进行数据清洗、分析和可视化",
    "熟悉UI/UX设计，能够进行界面设计和用户体验优化",
    "熟悉产品生命周期管理，包括产品规划、设计、开发、测试、上线和维护等各个环节",
    "熟悉产品需求分析和产品设计，能够进行产品需求分析、产品设计和产品迭代",
    "熟悉产品运营和市场推广，能够进行产品推广、市场调研和用户反馈分析",
    "熟悉产品管理和项目管理，能够进行产品管理和项目管理",
    "熟悉产品测试和质量保证，能够进行产品测试和质量保证",
    "熟悉产品发布和部署，能够进行产品发布和部署",
    "熟悉产品监控和性能优化，能够进行产品监控和性能优化",
    "熟悉产品迭代和版本控制，能够进行产品迭代和版本控制",
    "熟悉产品培训和用户支持，能够进行产品培训和用户支持",
    "熟悉产品文档和报告编写，能够进行产品文档和报告编写",
    "熟悉产品审计和合规管理，能够进行产品审计和合规管理"
  ],
  "证书": [
    "项目管理师",
    "计算机二级",
    "英语四级",
    "英语三级"
  ]
}
```