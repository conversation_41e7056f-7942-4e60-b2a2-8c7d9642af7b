```plaintext
丁嘉亮
24岁 | 2023应届毕业生 | 硕士 | 在校-月内到岗

编程技能：熟练掌握Java/python编程语言，熟练应用开发框架Jongo, SpringBoot, SpringCloud, 微服务框架Mybatis。
算法技能：熟练使用线性回归、决策树、SVM支持向量机、随机森林等机器学习算法。熟悉transformer, pytorch深度学习框架。
大型模型训练：了解主流大模型，熟练使用提示词工程，Langchain,RAG,PET微调，Lora微调，熟悉stable diffusion，clip多态模型。
办公软件：能够熟练运用Word, Excel, PowerPoint办公软件，以及墨刀原型设计软件。

期望职位：瑞银 | 算法工程师 | 行业不限 | 面议

工作经历
深圳城市交通规划设计师中心 | 大模型算法 科研员 | 实习 | 2025.06 - 2025.08
- 深度学习模型开发与优化：设计并优化基于深度学习的交通场景模型（如桥梁加速度与阻尼比、回波反射率预测、高架道路舒适度预测），将建模误差率分别降至3.42%和46.9%。
- Agent系统研发：参与城市交通场景下Agent系统的设计与原型开发，探索多智能体协作作用力的应用研究和开发。
- AI技术应用场景：参与地铁交通沿线车司机协同仿真项目，利用关键技术研发解决实际交通调度问题。
- AI前沿技术探索：阅读近期发表的AI相关论文，进行代码复现，探索新技术在现有项目中的应用。

珠海金山办公软件有限公司 | ai产品实习生 | 实习 | 2024.11 - 2025.02
工作任务：
- 根据需求和反馈的bugfix，进行Prompt设计和迭代，日常监测应用，进行提示分发策略调整，对于不同大型模型生成的base版进行评价。
- 测试公司内Prompt，用户对Prompt的反馈为90%，近一周采纳的采纳率30.4%。
工作任务：
- 在已有的场景包括面向问答、党的人类文字场景下的Prompt，文字发送率采用从4.5%提升到57.2%，以0-1构建十三种标准文本格式Prompt；
- 研究改进：
  - 基于大语言模型的在线质检检测系统（IEEESEICS）出版中第一作者，主导完成实验设计、代码实现、模型优化与论文撰写。
  - 基于大语言模型的在线质检检测系统（IEEESEICS）出版中
  - 创新点：基于LoRA技术的ChatGLM-6B进行微调，设计混合精度策略，结合语境检索技术降低显存开销；基于AdamW优化器动态权重衰减策略；采用稀疏学习率预热融合并行学习蒸馏策略，同时利用BERT、Qwen等大模型进行预训练。
  - 成果：精度提高24%，召回率提高14%，准确率提高12%，F1-score提升13.

项目经验
基于大语言模型的虚假新闻检测系统 | 第一作者 | 2025.02 - 2025.06
- 大型模型微调与优化：作为第一作者，主导基于LoRA技术微调ChatGLM-6B模型，设计混合精度训练+梯度检查策略确保稳定性和效率；采用AdamW优化器改善+4性学习速率策略，提升模型收敛速度。
- 多模型对比实验：通过bert、Qwen 等模型对照实验验证，优化检索算法，最终宏观准确率提升24%，召回率提升14%，F1-scor 提升13，伪造信息识别提供解决方案。
Chappit Agent智能助手 | 开发者 | 2024.11 - 2024.12
项目背景与目标：ChatGPT是一个基于多模态AI技术的强大助手，旨在提升企业办公自动化流程效率。它能够处理语音、图像和文本等多种输入模式，通过简单的提问和读取的自然语言处理能力，为用户生成高质量的PPT。
项目实现：
- 多模态输入支持：支持语音集成Qipai Whisper自动语音识别技术、图像集成MiniCPM-V2模型、文本等多种输入方式自动生成演示文稿，基于输入内容，通过GPT-4生成的内容，启动生成模块的PPT演示文稿，支持多种输出模板。
- PPT智能配置：利用GPT-3理解PPT内容，结合布局配置相关的关键字通过运行Chrome API获取图片。
- 文件转换：上传doc文档后，通过docx解析MD文件再利用SPIT-4将格式正规范化可视化界面利用Smatio的多模态网页界面展现。
- 基于RAG系统的企业合规审计系统 | 开发者 | 2024.09 - 2024.11
项目背景与目标：企业每年都会公布可持续发展报告，该系统旨在利用GR标准来检查企业可持续发展报告中相关内容，再利用AI模型对其进行判断是否符合GR标准。
项目实现：
- 文档导入与分析：利用Unstructured工具获取PDF并获取Metadata，利用段落分块和滑动窗口的句子切分策略，Embedding模型利用BiLM-BiGRU模型和ME-GEM模型分别编码与Metadata数据的静态+动态和动态化，并分别存入MySQL数据库中；查询结果匹配利用DeepSeq-3模型和BERT-FM模型量化Query，利用相似度计算在MySQL向量库中进行召回。召回率约98%，重排和修正利用TF-IDF模型排序、词袋模型 错误率降为2:1，通过ChatGPT-3.5模型推理基于RAG模型迭代优化，有可能进行重新检索,Watbsearch，
- 应对反应：通过Promerg工程实现基于ChatGPT-4的输出。
- 系统评估：利用RAGQA对模型输出的内容进行忠实度和相关性的判定，忠实度≈91，相关性≈92。
基于Django的轻量级BLOG追踪平台 | 开发者 | 2024.04 - 2024.08
项目介绍：基于Django开发Bug管理系统，为用户提供理想的工作云平台，涵盖了众多行业级功能，便捷的团队协作、详尽的项目管理、兼容的权限系统、大量的文件存储等，大大提升了工作效率。
技术特点：gRPC框架、ModelForm扩展、使用自定义widget插件LOOP多维表实现Bootstrap应用、腾讯对象存储COS用于多个目录存储、集成多个datamappickerjs / select2 /daterangepicker.js / highcharts /bootstrap-select.js、支付网支付和微信扫码。
基于微信小程序的面试宝典 | 开发者 | 2023.01 - 2023.08
本项目是基于微信小程序的程序员访谈手册，旨在为用户提高高质量的访谈材料和与访谈相关的实用信息，它可以按照程序员的岗位知识，从前端到后端开发者工具进行设计，而后部署用了Java开发框架SpringBoot 和MyBatis框架。数据层通过本地数据库和云存储来实现。
项目链接：https://pan.baidu.com/s/1loaVwjaqjhdA7AEhNPEw?pwd=prcy

教育经历
西北师范大学 | 人工智能与数码媒体 | 硕士 | 2024 - 2025
主修课程：
人工智能理论与应用（A）、数据科学编程基础（A）、大数据管理与分析（A）、数码媒体中的人工智能（A）、人工智能与数码媒体工作（A）
湖北工业大学 | 工程技术学院 | 软件工程 | 本科 | 2019 - 2023
在校经历：
2019.09-2020.02 校级三等奖学金；2020.05-2021.02 校级三等奖学金；
2020.11任班级宣传委员；2021.10获得“学创杯”演讲一等奖；2021.12 ICAN互联网大赛湖北区三等奖；2021年国家优秀学生；2022.09-2021.01 校级一等奖学金；
2021.腾讯微信应用开发（Python）Java/Key开发的SQLOracle数据库开发与实战（3天）算法与数据结构(59)；
毕设论文：基于微信小程序的面试宝典

所获荣誉
校级三等奖学金 | 学潮网推北疆二等奖 | ICAN斩获校赛北疆三等奖 | 年度优秀学子 | 推北疆三等奖
校级二等奖学金 | ICAN互联网大赛湖北省三等奖 | 校级二等奖学金 | 年度优秀奖二 | “学潮网推北疆”一等奖

培训证书
编程课程：热爱掌握Java/python编程语言，熟练应用开发框架Jongo, SpringBoot, SpringCloud, 服务框架Mybatis、Mybatis-Plus，去复杂化；熟练使用线性回归、决策树、SVM支持向量机、随机森林等机器学习算法。熟悉Transformer深度学习框架。、大型模型技能：熟练利用RAG、Langchain、Lora微调、Prompt Engineer、Agent开发、BERT、Tirare、Qwen、Deiseli、Clip模型原理与应用。| 办公软件：能够熟练运用Word、Excel、PowerPoint办公软件，以及墨刀原型设计软件。

为保证保护本人在BOSS直聘平台提交、发布、展示的简历（包括但不限于在线浏览、附件简历）中的个人信息（包括但不限于联系方式、学历证明、工作经验、工作职责等），任何单位以上均可出于合理招聘的目的，通过BOSS直聘平台对该简历进行查阅。未经BOSS直聘及本人本人书面授权，在任何情况下不得将本人在BOSS直聘平台提交、发布、展示的简历中的个人信息，在任何第三方平台上进行复制、使用、传播、存储。
```