```json
{
  "个人信息": {
    "姓名": "姚博",
    "邮箱": "<EMAIL>",
    "求职意向": "产品经理，行业不限，期望薪资：15-18K"
  },
  "工作经历": [
    {
      "公司名称": "蚂蚁集团金融科技有限公司",
      "职位": "数据标注工程师",
      "时间": "2023.05 - 至今",
      "职责": [
        "负责智能投顾金融AI Agent模型的标注应用。",
        "AI Agent的算法研发。",
        "AI Agent的优化和升级。",
        "用户画像的运营。",
        "具体工作内容包括：根据产品需求进行标注任务的设计与执行；参与与产品团队的沟通协作，确保标注工作的顺利进行；分析产品需求，设计有效的标注方案并应用；对标注结果进行审核，保证标注质量；搭建智能投顾平台的运行机制，并持续优化迭代系统，确保其在实际可用性。",
        "使用知识图谱技术（KBP）即时理解标签含义，并与团队进行紧密合作，确保问题及时解决。"
      ]
    },
    {
      "公司名称": "蚂蚁集团金融科技有限公司",
      "职位": "产品经理",
      "时间": "2022.11 - 2023.03",
      "职责": [
        "加入智能投顾项目组后，设计并实施了多个产品的原型复盘，以用户反馈体验及用户痛点出发效果，推动积极入口工具上手转化率的提升。",
        "作为强大的AI基础建设，促进客户化，提升用户体验，更好服务用户投资，解决用户问题。",
        "为泛理财用户提供标签体系，包含常见的金融术语、关键投资概念和热点资讯。",
        "与销售和客服部门合作，推动智能标签体系覆盖度的一致性实现。",
        "使用分析工具监控标签使用情况，发现并调整重复或不相关的标签，提高标签的有效性。",
        "加入智能投顾项目组后，设计并实施了多个产品的原型复盘，以用户反馈体验及用户痛点出发效果，推动积极入口工具上手转化率的提升。",
        "作为强大的AI基础建设，促进客户化，提升用户体验，更好服务用户投资，解决用户问题。",
        "与销售和客服部门合作，推动智能标签体系覆盖度的一致性实现。",
        "使用分析工具监控标签使用情况，发现并调整重复或不相关的标签，提高标签的有效性。",
        "负责产品功能组合设计的流程管理。",
        "学习掌握常用的技术工具。",
        "完成上级安排的相关工作任务。"
      ]
    },
    {
      "公司名称": "项目经验",
      "职位": "项目经理",
      "时间": "2022.11 - 至今",
      "职责": [
        "1. 完成项目的需求文档、详细设计文档，以及项目计划书，将项目计划通过腾讯 uop 平台进行发送，自动过滤系统进行接收，接收后由 win 系统进行文件分发。",
        "2. 项目管理：立项、立项会议、阶段评审、writting not、info 验证。",
        "3. 项目交付："
      ]
    },
    {
      "公司名称": "项目经验",
      "职位": "AI小猿 A产品经理",
      "时间": "2020.02 - 2024.12",
      "职责": [
        "可下载设备移动端，使用A小猿，体验效果。",
        "AIP 小猿是小米小爱同学的助手，帮助用户了解小爱同学的问题和困惑，以用户视角提供小爱同学能够输出效果更好的小爱同学语音指令的功能和知识库，成为用户与小爱同学知乎，让用户自动化搜索问题解答，更好地使用用户的设备。",
        "需要深入理解项目的市场调研、客户需求及法律法规的要求，可以按照项目的定位、目标、方向、产品特点及优势、竞品分析等制定出符合项目要求的产品规划书。",
        "产品开发中涉及开发、测试、迭代和维护等多个环节，有较强的协调和管理能力有效传达信息和意见，梳理思路，明确为用户创造价值的方向并且把想法转化为设计方案的基本框架以及详细的测试计划和验证。",
        "作为产品经理，需要关注产品的性能和用户体验，以便于发现问题并提出解决方案，同时要具备良好的沟通能力和团队协作精神。",
        "编写需求、竞品分析、智屏端 AAgent 的产品需求文档，以及相关测试用例，提升用户体验，满足用户需求。",
        "需要熟练运用 SQL 对业务数据进行处理，使用大数据模型，展示预测示例的准确性和发展趋势，通过 AAgent 的增强模块的内容，以私教的方式为客户做精准的引导，再考虑是否利用定制化的 AAgent 复制模板，整合私教的知识内容，推出具有特色的专属内容。"
      ]
    },
    {
      "公司名称": "项目经验",
      "职位": "蚂蚁风控教育 A产品经理",
      "时间": "2024.08 - 2024.11",
      "职责": [
        "发起并孵化一个社会化社区，关键投资决策的辅助系统的底层，涵盖超过100个领域至少一半以上的新领地至少一半以上。",
        "工作目标围绕风险控制标准的规范化建立日程推进，保障标的合规性，降低违规风险，提升各机构的准确率至98%。",
        "筛选风险监控模型，每日筛选超过100个领域或较长的名单，有效地提高了标准化的风险监控。",
        "研究并运用 Python+winform方式构建模型，撰写高质量report，形成完整的报告体系内生成报告达80%。",
        "深度挖掘用户行为特征，针对不同场景和人群做出针对性主题文章，导览地址超200篇文章，保证内容的一致性。",
        "P"
      ]
    },
    {
      "公司名称": "项目经验",
      "职位": "金链AI Agent职位",
      "时间": "2023.11 - 2024.06",
      "职责": [
        "负责跟进金融市场的运作机制，客户需求以及宏观政策的影响，以确保产品的目标和定位。",
        "产品的设计开发：作为产品经理从企业业务的角度来看，确保所有的需求和期望都能够得到很好的表达满足。",
        "数据治理：保障数据的质量可控性，同时也需要掌握数据治理分析的基本技能，以支持溯源的训练和验证。",
        "估值模型设计：确定金链产品的估值目标，实现估值的可视化呈现。",
        "负责跟进金链AI Agent在现有业务中的实际落地情况，不断改进和创新，生产实现客户的目标价值，解决复杂问题，进一步提升解决效率。",
        "智能推荐、智能问答：智能推荐的 AI Agent为金融服务业提供多种功能比如智能推荐、智能问答、智能风控、智能客服等，基于丰富的应用场景和海量的数据，降低成本、增加收入、优化风险管理、提升客户满意度。",
        "需求管理：策划并生成使用大模型调优、前置策略的动态生效和推送规则，通过金链AI Agent的精准定位、整合营销的内容，推出独具特色的专属内容。"
      ]
    }
  ],
  "教育背景": {
    "学校": "北京理工大学珠海学院",
    "专业": "造价工程",
    "学位": "本科",
    "时间": "2016 - 2022"
  },
  "证书": {
    "证书名称": "某证书编号"
  },
  "专业技能": [
    "Java、Python、C++、Django、大型数据库如MySQL、RabbitMQ、Flask等基础知识",
    "需求分析、需求设计与绘制、操作手册、API接口设计",
    "行业知识：金融领域的知识积累，智慧金融、银行技术、客户关系管理",
    "为适应互联网时代快速更新、变化、融合的趋势，我始终坚持以学习为主导，持续进步，个人简历中所列的信息，在任何第三方平台未经许可，不得复制、修改、发布。"
  ]
}
```