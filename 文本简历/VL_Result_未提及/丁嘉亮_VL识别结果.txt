```json
{
  "个人信息": {
    "姓名": "丁嘉亮",
    "年龄": "24岁",
    "毕业院校": "北京邮电大学",
    "专业": "硕士",
    "联系方式": "136****9876"
  },
  "工作经历": [
    {
      "公司名称": "深圳城市交通规划设计研究院中心",
      "职位": "大模型算法 科研员",
      "时间": "2025.06-2025.08",
      "项目描述": [
        "深度学习模型开发与优化：设计并优化基于深度学习的交通场景模型（如桥梁加速度与温度/回波率预测、高架道路舒适度预测），将建模误差分别降至3.42%和46.5%。",
        "Agent 系统研发：参与构建交通领域Agent 系统的设计与原型开发，探索多智能体协作应用的方法与开发。",
        "AI 技术应用场景：参加地铁交通沿线车司机协同仿真项目，运用关键技术模块解决实际交通调度问题。",
        "AI 前沿技术探索：阅读近期发表的AI 相关论文，进行代码复现，探索新技术在现有项目中的应用。"
      ]
    },
    {
      "公司名称": "珠海金山办公软件有限公司",
      "职位": "ai产品实习生",
      "时间": "2024.11-2025.02",
      "项目描述": [
        "工作任务：根据需求和反馈的bugcase，进行Prompt设计和迭代，日常监测应用，进行提示流分类调整器，对于不同大型模型生成的base case进行评价。",
        "法定公众号维护：Promot 用户对内容的反馈为90%，近一周采纳的采纳率为80.4%。",
        "工作任务：已上线的场景包括面向电商、党政机关等文字场景下的Prompt，文字发送率由前4.5%提升到57.2%，以0-1构建十三种标准文本格式Prompt。",
        "研究经验：基于大语言模型的在线质检系统（IEEESEICS）出版中第一作者，主导完成实验设计、代码实现、模型优化与论文撰写。",
        "基于大语言模型的在线质检系统（IEEESEICS）出版中副组长：基于Lora技术微调ChatGLM-6B 进行质量检测，设计混合精度策略，结合语境检索技术降低显存开销；基于AdamW优化策略动态调整学习率；采用梯度学习率衰减融合并行学习率衰减策略，同时利用BERT、Qwen等大模型进行预训练。",
        "成果：精准度提高24%，召回率提高14%，准确率提高12%，F1-score提升13."
      ]
    }
  ],
  "教育背景": [
    {
      "学校": "北师香港浸会大学",
      "专业": "人工智能与数码媒体",
      "学位": "硕士",
      "时间": "2024-2025",
      "主修课程": [
        "人工智能理论与应用(A)、数据科学编程基础(A)、大数据管理与分析(A)、数码媒体中的人工智能(A)、人工智能与数码媒体工作(A)"
      ]
    },
    {
      "学校": "湖北工业大学工程技术学院",
      "专业": "软件工程",
      "学位": "本科",
      "时间": "2019-2023",
      "在校经历": [
        "2019.09-2020.02 校级三等奖学金；2020.05-2021.02 排名三等奖学金；2020.11担任班级宣传委员；2021.10获得“华创杯”演讲一等奖；2021.12 ICAN互联网大赛湖北省三等奖；2021年国家优秀学生；2022.09-2021.01 排名一等奖学金；2021.腾讯微信应用开发(分)Java+Javafx开发(分)Oracle数据库开发与实战(分)算法与数据结构(69)；毕设:论文《基于微信小程序的面试宝典》"
      ],
      "所获荣誉": [
        "校级三等奖学金",
        "ICAN互联网大赛湖北省三等奖",
        "校级二等奖学金",
        "年度优秀学生二",
        "华创杯演讲一等奖"
      ]
    }
  ],
  "技能": [
    "编程语言：熟练掌握Java/python编程语言，熟练应用开发框架Jongo, SpringBoot, SpringCloud, 服务框架、Mybatis，Spring Boot；熟悉使用弹性固件、决策树、SVM支持向量机、随机森林等机器学习算法。熟悉transformer, pytorch深度学习框架。",
    "大型模型技能：熟练利用RAO、Langchain、Lora微调、Prompt Engineer、Agent开发、BertTrain、Qwen、Dense、Clip模型原理与应用。",
    "办公软件：能够熟练运用Word、Excel、PowerPoint办公软件，以及墨刀原型设计软件。",
    "其他技能：热爱编程，热衷于开源社区贡献，善于团队合作，具有良好的沟通能力和解决问题的能力。"
  ],
  "求职意向": [
    "期望职位：珠海 | 算法工程师 | 行业不限 | 面议"
  ]
}
```