#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BOSS直聘自动化程序配置文件
"""

# 浏览器配置
BROWSER_CONFIG = {
    'headless': False,  # 是否使用无头模式
    'window_size': '1920,1080',  # 浏览器窗口大小
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'headless_fallback': True,  # 无头模式失败时是否回退到有头模式
    'max_retries': 2,  # 最大重试次数
    'headless_options': {  # 无头模式专用选项
        'disable_blink_features': 'AutomationControlled',
        'disable_extensions': True,
        'no_first_run': True,
        'disable_default_apps': True,
        'disable_popup_blocking': True,
        'disable_web_security': True,
        'allow_running_insecure_content': True,
        'disable_features': 'VizDisplayCompositor',
        'disable_ipc_flooding_protection': True
    }
}

# URL配置
URLS = {
    'login': 'https://www.zhipin.com/web/user/?ka=header-login',
    'chat': 'https://www.zhipin.com/web/chat/index'
}

# 超时配置（秒）
TIMEOUTS = {
    'login_wait': 300,  # 等待用户登录的超时时间
    'page_load': 100,    # 页面加载超时时间
    'element_wait': 20  # 元素等待超时时间
}

# 个人中心元素的XPath
PERSONAL_CENTER_XPATH = "/html/body/div[2]/div[2]/div/div/div[9]/div[1]/div[1]/div/span[1]"

# 职位管理按钮的选择器
JOB_MANAGEMENT_SELECTOR = "//a[contains(text(), '职位管理')]"

# 职位信息提取的选择器
JOB_INFO_SELECTORS = {
    'warp': "div.job-list-warp",
    'wrappers': "li.job-jobInfo-warp",
    'title': 'div.job-title a',               # 职位名称类名
    'info_labels': 'div.info-labels',       # 岗位信息标签类名
    
    # 新增：岗位信息标签的备用选择器
    'alternative_info_labels': [
        'info-labels',
        'job-info',
        'position-info',
        'recruit-info',
        'job-details',
        'position-details',
        'recruit-details',
        'job-meta',
        'position-meta',
        'recruit-meta',
        'job-tags',
        'position-tags',
        'recruit-tags',
        'job-attributes',
        'position-attributes',
        'recruit-attributes'
    ],
    
    # 新增：岗位信息字段的多种选择器
    'info_field_selectors': {
        '工作地点': [
            ".//span[contains(@class, 'location')]",
            ".//span[contains(@class, 'place')]",
            ".//span[contains(@class, 'city')]",
            ".//span[contains(@class, 'area')]",
            ".//div[contains(@class, 'location')]",
            ".//div[contains(@class, 'place')]",
            ".//div[contains(@class, 'city')]",
            ".//div[contains(@class, 'area')]",
        ],
        '工作经验': [
            ".//span[contains(@class, 'experience')]",
            ".//span[contains(@class, 'exp')]",
            ".//span[contains(@class, 'year')]",
            ".//div[contains(@class, 'experience')]",
            ".//div[contains(@class, 'exp')]",
            ".//div[contains(@class, 'year')]",
            ".//*[contains(text(), '年') and contains(text(), '经验')]",
            ".//*[contains(text(), '应届') or contains(text(), '1年') or contains(text(), '3年') or contains(text(), '5年')]"
        ],
        '学历要求': [
            ".//span[contains(@class, 'education')]",
            ".//span[contains(@class, 'edu')]",
            ".//span[contains(@class, 'degree')]",
            ".//div[contains(@class, 'education')]",
            ".//div[contains(@class, 'edu')]",
            ".//div[contains(@class, 'degree')]",
            ".//*[contains(text(), '大专') or contains(text(), '本科') or contains(text(), '硕士') or contains(text(), '博士')]"
        ],
        '薪资范围': [
            ".//span[contains(@class, 'salary')]",
            ".//span[contains(@class, 'pay')]",
            ".//span[contains(@class, 'money')]",
            ".//div[contains(@class, 'salary')]",
            ".//div[contains(@class, 'pay')]",
            ".//div[contains(@class, 'money')]",
            ".//*[contains(text(), 'K') or contains(text(), '万') or contains(text(), '元')]"
        ],
        '招聘类型': [
            ".//span[contains(@class, 'type')]",
            ".//span[contains(@class, 'category')]",
            ".//span[contains(@class, 'job-type')]",
            ".//div[contains(@class, 'type')]",
            ".//div[contains(@class, 'category')]",
            ".//div[contains(@class, 'job-type')]",
            ".//*[contains(text(), '全职') or contains(text(), '兼职') or contains(text(), '实习')]"
        ]
    },
    'alternative_wrappers': [           # 备用职位包装器选择器
        # 主要选择器
        "//ul[contains(@class, 'job-list-content')]//li",
        "//div[contains(@class, 'job-main-info')]",
        "//li[contains(@class, 'job-jobInfo-warp')]",
        "//div[contains(@class, 'job-main-info-wrapper')]",
        "//div[contains(@class, 'top-wrap')]",
        
        # 新增选择器 - 基于实际页面结构
        "//div[contains(@class, 'job-item')]",
        "//div[contains(@class, 'job-card')]",
        "//div[contains(@class, 'position-item')]",
        "//div[contains(@class, 'recruit-item')]",
        "//div[contains(@class, 'job-info')]",
        "//div[contains(@class, 'position-info')]",
        "//div[contains(@class, 'recruit-info')]",
        "//div[contains(@class, 'job-list-item')]",
        "//div[contains(@class, 'position-list-item')]",
        "//div[contains(@class, 'recruit-list-item')]",
        
        # 更通用的选择器
        "//div[contains(@class, 'item') and contains(@class, 'job')]",
        "//div[contains(@class, 'item') and contains(@class, 'position')]",
        "//div[contains(@class, 'item') and contains(@class, 'recruit')]",
        "//div[contains(@class, 'card') and contains(@class, 'job')]",
        "//div[contains(@class, 'card') and contains(@class, 'position')]",
        
        # 基于文本内容的选择器
        "//div[contains(text(), '职位') or contains(text(), '招聘') or contains(text(), '工作')]",
        "//div[contains(text(), '薪资') or contains(text(), '地点') or contains(text(), '经验')]",
        
        # 基于页面结构的选择器
        "//div[@data-testid]",
        "//div[@data-id]",
        "//div[@id and contains(@id, 'job')]",
        "//div[@id and contains(@id, 'position')]",
        "//div[@id and contains(@id, 'recruit')]",
        
        # 最后的后备选择器
        "//div[contains(@class, 'content')]//div[position()>1]",
        "//div[contains(@class, 'main')]//div[position()>1]",
        "//div[contains(@class, 'list')]//div[position()>1]"
    ],
    
    # 新增：专门针对iframe中ul元素的选择器
    'iframe_ul_selectors': [
        # 直接查找ul元素
        "//ul",
        "//ul[contains(@class, 'list')]",
        "//ul[contains(@class, 'job')]",
        "//ul[contains(@class, 'position')]",
        "//ul[contains(@class, 'recruit')]",
        "//ul[contains(@class, 'content')]",
        "//ul[contains(@class, 'main')]",
        
        # 查找包含特定文本的ul
        "//ul[.//*[contains(text(), '职位')]]",
        "//ul[.//*[contains(text(), '招聘')]]",
        "//ul[.//*[contains(text(), '工作')]]",
        "//ul[.//*[contains(text(), '工程师')]]",
        "//ul[.//*[contains(text(), '经理')]]",
        "//ul[.//*[contains(text(), '专员')]]",
        
        # 查找有多个li子元素的ul
        "//ul[count(li) > 1]",
        "//ul[count(li) > 2]",
        "//ul[count(li) > 3]",
        
        # 查找包含特定结构的ul
        "//ul[.//li[contains(@class, 'item')]]",
        "//ul[.//li[contains(@class, 'job')]]",
        "//ul[.//li[contains(@class, 'position')]]",
        "//ul[.//li[contains(@class, 'recruit')]]"
    ],
    
    'alternative_titles': [             # 备用职位名称选择器
        # 主要选择器
        ".//div[contains(@class, 'title')]",
        ".//span[contains(@class, 'title')]",
        ".//h3",
        ".//h4",
        ".//strong",
        
        # 新增选择器
        ".//div[contains(@class, 'job-title')]",
        ".//div[contains(@class, 'position-title')]",
        ".//div[contains(@class, 'recruit-title')]",
        ".//div[contains(@class, 'name')]",
        ".//span[contains(@class, 'name')]",
        ".//div[contains(@class, 'job-name')]",
        ".//span[contains(@class, 'job-name')]",
        ".//div[contains(@class, 'position-name')]",
        ".//span[contains(@class, 'position-name')]",
        
        # 基于文本内容的选择器
        ".//*[contains(text(), '工程师') or contains(text(), '经理') or contains(text(), '专员') or contains(text(), '主管') or contains(text(), '总监')]",
        ".//*[contains(text(), '开发') or contains(text(), '设计') or contains(text(), '运营') or contains(text(), '销售') or contains(text(), '市场')]",
        
        # 通用选择器
        ".//div[1]",
        ".//span[1]",
        ".//*[1]"
    ]
}

# 职位信息字段映射
JOB_INFO_FIELDS = [
    '工作地点',      # 第一个span
    '工作经验',      # 第二个span
    '学历要求',      # 第三个span
    '薪资范围',      # 第四个span
    '招聘类型'       # 第五个span
]

# 用户信息提取的选择器
USER_INFO_SELECTORS = {
    'name': "//span[contains(@class, 'name')]",
    'avatar': "//div[contains(@class, 'avatar')]//img",
    'company': "//div[contains(@class, 'info-item')]"
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'boss_automation.log',
    'encoding': 'utf-8'
}
# 等待时间配置（秒）
WAIT_TIMES = {
    'page_load_extra': 3,  # 页面加载后的额外等待时间
    'after_click': 3,      # 点击后的等待时间
    'before_extract': 2    # 提取信息前的等待时间
}
