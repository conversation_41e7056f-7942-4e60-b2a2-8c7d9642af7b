



#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BOSS直聘自动化程序 - DrissionPage版本
功能：自动访问BOSS直聘平台，获取用户信息和职位列表
"""
import time
import logging
import json
import os
from datetime import datetime
from DrissionPage import ChromiumPage, ChromiumOptions, Chromium
from DrissionPage.common import Actions
from user_state_manager import user_state
# 配置常量
BROWSER_CONFIG = {
    'headless': False,
    'window_size': '1920,1080',
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
}

URLS = {
    'login': 'https://www.zhipin.com/web/user/?ka=header-login',
    'chat': 'https://www.zhipin.com/web/chat/index'
}

USER_INFO_SELECTORS = {
    'name': 'x://span[contains(@class, "name")]',
    'avatar': 'x://img[contains(@src, "avatar")]'
}

JOB_MANAGEMENT_SELECTOR = 'x://a[contains(text(), "职位管理")]'

JOB_INFO_SELECTORS = {
    'warp': 'x://div[contains(@class, "job-jobInfo-warp")]',
    'wrappers': 'x://li[contains(@class, "job-jobInfo-warp")]',
    'title': 'x://div[contains(@class, "job-title")]//a',
    'info_labels': 'x://div[contains(@class, "info-labels")]'
}

TIMEOUTS = {
    'page_load': 30,
    'element_wait': 10,
    'login_wait': 300
}

class BossDriver:
    """浏览器驱动管理 - DrissionPage版本"""
    
    def __init__(self, headless=False, debug_mode=False):
        self.browser = None
        self.headless = headless
        self.debug_mode = debug_mode
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('boss_automation_drission.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_driver(self):
        """设置DrissionPage浏览器"""
        try:
            self.logger.info("正在初始化DrissionPage浏览器...")
            
            # 创建浏览器选项
            co = ChromiumOptions()
            co.set_argument('--window-size', BROWSER_CONFIG['window_size'])
            co.set_argument('--user-agent', BROWSER_CONFIG['user_agent'])
            
            if self.headless:
                co.set_argument('--headless=new')
                co.set_argument('--disable-blink-features=AutomationControlled')
                co.set_argument('--disable-extensions')
                co.set_argument('--no-first-run')
                co.set_argument('--disable-default-apps')
                co.set_argument('--disable-popup-blocking')
                co.set_argument('--disable-web-security')
                co.set_argument('--allow-running-insecure-content')
                co.set_argument('--disable-features=VizDisplayCompositor')
                co.set_argument('--disable-ipc-flooding-protection')
            
            co.set_argument('--no-sandbox')
            co.set_argument('--disable-dev-shm-usage')
            co.set_argument('--disable-gpu')
            
            # 创建浏览器实例
            from DrissionPage import ChromiumPage
            self.browser = ChromiumPage(co)
            
            # DrissionPage不需要手动设置timeout，这些设置已经在ChromiumOptions中配置
            
            self.logger.info("DrissionPage浏览器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {e}")
            return False
    
    def close(self):
        """关闭浏览器"""
        if self.browser:
            try:
                self.browser.quit()
                self.logger.info("浏览器已关闭")
            except Exception as e:
                self.logger.error(f"关闭浏览器失败: {e}")

class BossScraper:
    """数据提取器 - DrissionPage版本"""
    
    def __init__(self, browser):
        self.browser = browser
        self.logger = logging.getLogger(__name__)
    
    def wait_for_login(self, timeout=300):
        """等待用户登录"""
        timeout = timeout or TIMEOUTS['login_wait']
        try:
            self.logger.info("等待用户登录...")
            
            # 等待URL变化到聊天页面
            start_time = time.time()
            while time.time() - start_time < timeout:
                # 在DrissionPage中，使用page.url获取当前URL
                current_url = self.browser.url
                if "chat/index" in current_url:
                    self.logger.info("用户已登录")
                    return True
                time.sleep(2)
            
            self.logger.error("登录超时")
            return False
            
        except Exception as e:
            self.logger.error(f"等待登录失败: {e}")
            return False
    
    def extract_user_info(self):
        """提取用户信息"""
        try:
            self.logger.info("开始提取用户信息")
            
            # 等待用户名元素出现
            name_element = self.browser.ele(USER_INFO_SELECTORS['name'])
            if not name_element:
                self.logger.error("未找到用户名元素")
                return None
            # self.logger.info(name_element)
            name = name_element.text.strip()
            if name:
                self.logger.info(f"用户名: {name}")
            else:
                self.logger.error("用户名提取失败")
                return None
            
            # 获取头像
            avatar_element = self.browser.ele(USER_INFO_SELECTORS['avatar'])
            if avatar_element:
                avatar_url = avatar_element.attr('src')
                self.logger.info(f"头像URL: {avatar_url}")
            else:
                avatar_url = ''


            # ===================================================================
            # '''
            #     待修改删除
            # '''

            # self.complete_info = True
            # self.user_info = {
            #     '姓名': name or '未知',
            #     '公司名称': '待提取',
            #     '头像': avatar_url or '',
            #     '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # }
            # ===================================================================

            return {
                '姓名': name or '未知',
                '公司名称': '待提取',
                '头像': avatar_url,
                '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            self.logger.error(f"提取用户信息失败: {e}")
            return None
            
    def extract_jobs(self):
        """提取职位列表"""
        try:
            self.logger.info("开始提取职位信息...")
            
            # 点击职位管理
            job_btn = self.browser.ele(JOB_MANAGEMENT_SELECTOR)
            if not job_btn:
                self.logger.error("未找到职位管理按钮")
                return []
            
            job_btn.click()
            self.logger.info("成功进入职位管理页面")
            time.sleep(1.2)
            
            # 获取iframe
            iframe = self.browser.get_frame(1)
            if not iframe:
                self.logger.error("未找到iframe")
                return []
            
            self.logger.info("已切换到iframe")
            
            # 获取职位列表
            job_wrappers = iframe.eles(JOB_INFO_SELECTORS['wrappers'])
            if not job_wrappers:
                self.logger.error("未找到职位列表")
                return []
            
            self.logger.info(f"一共获取到{len(job_wrappers)}个职位")
            jobs = []
            
            for i in range(len(job_wrappers)):
                try:
                    self.logger.info(f"开始提取第{i+1}个职位信息...")
                    # 获取职位标题
                    time.sleep(0.3)
                    iframe = self.browser.get_frame(1)
                    self.logger.info(f"第{i+1}次进入iframe")
                    job_wrapper = iframe.eles(JOB_INFO_SELECTORS['wrappers'])[i]
                    title_element = job_wrapper.ele(JOB_INFO_SELECTORS['title'])
                    title = title_element.text.strip()
                    self.logger.info(f"职位名称: {title}")

                    # 获取职位信息标签
                    info_container = job_wrapper.ele(JOB_INFO_SELECTORS['info_labels'])
                    info_spans = [span.text.strip() for span in info_container.eles('tag:span')]
                
                    # 点击职位获取详情
                    title_element.click()
                    time.sleep(1.2)
                    
                    # 查找textarea iframe
                    try:
                        # 获取textarea内容
                        iframe = self.browser.get_frame(1)
                        textarea = iframe.ele('tag:textarea')
                        jd_content = textarea.value or ""
                        time.sleep(0.2)
            

                    
                    except Exception as e:
                        self.logger.warning(f"第{i+1}个职位获取详情失败: {e}")

                    
                    # 构建职位信息
                    job_info = {
                        '职位索引': i + 1,
                        '职位名称': title,
                        '工作地点': info_spans[0] if len(info_spans) > 0 else '未知',
                        '工作经验': info_spans[1] if len(info_spans) > 1 else '未知',
                        '学历要求': info_spans[2] if len(info_spans) > 2 else '未知',
                        '薪资范围': info_spans[3] if len(info_spans) > 3 else '未知',
                        '招聘类型': info_spans[4] if len(info_spans) > 4 else '未知',
                        '职位详情': jd_content
                    }
                    self.logger.info(f"岗位信息: {job_info}")
                    
                    jobs.append(job_info)
                    self.logger.info(f"第{i+1}个职位信息提取完成")
                    self.browser.back()
                    # self.browser.wait_for_load()
                except Exception as e:
                    self.logger.warning(f"提取第{i+1}个职位失败: {e}")
                    continue
            

            # ===================================================================
            # '''
            #     待修改删除
            # '''
            # self.user_info['职位列表'] = jobs
            # ===================================================================


            return jobs
            
        except Exception as e:
            self.logger.error(f"提取职位列表失败: {e}")
            return []

class BossAutomation:
    """主控制流程 - DrissionPage版本"""
    
    def __init__(self, headless=False):
        self.driver_manager = BossDriver(headless)
        self.scraper = None

    
    def close_boss_PCAD_dialog(self) -> bool:
        """关闭随机客户端弹窗"""
        try:
            # 等待页面加载
            time.sleep(1)
            
            # 判断是否出现弹窗
            dialog_xpath = 'x://img[contains(@class, "bosszp-bg")]'
            dialog_element = self.driver_manager.browser.ele(dialog_xpath)
            
            if dialog_element:
                self.driver_manager.logger.info("检测到弹窗-dont，准备关闭")
                
                # 查找关闭按钮
                close_button_xpath = 'x://i[@class="icon-close"]'
                close_button = self.driver_manager.browser.ele(close_button_xpath)
                
                if close_button:
                    close_button.click()
                    time.sleep(1)
                    self.driver_manager.logger.info("关闭弹窗成功")
                    return True
                else:
                    self.driver_manager.logger.warning("未找到弹窗关闭按钮")
                    return False
            else:
                self.driver_manager.logger.info("未检测到弹窗")
                return True
                
        except Exception as e:
            self.driver_manager.logger.error(f"关闭弹窗失败: {e}")
            return False
    
    def login_and_extract_user_info(self):
        """运行主流程"""
        try:
            # 设置驱动
            if not self.driver_manager.setup_driver():
                return None
            
            # 访问登录页面
            self.driver_manager.browser.get(URLS['login'])
            self.driver_manager.logger.info("已访问登录页面，请手动登录")
            
            # 创建数据提取器
            self.scraper = BossScraper(self.driver_manager.browser)
            
            # 等待登录
            if not self.scraper.wait_for_login():
                return None
            

            
            # 提取用户信息
            user_info = self.scraper.extract_user_info()
            if not user_info:
                return None
            else:
                user_state.set_current_user(user_info)
                return user_info

        except Exception as e:
            self.driver_manager.logger.error(f"获取用户信息失败: {e}")
            return None

    def extract_jobs(self):
        """提取职位信息"""
        try:
            # 关闭弹窗
            self.close_boss_PCAD_dialog()  #待测试，减少时间
            
            # 提取职位信息
            jobs_info = self.scraper.extract_jobs()
            
            return jobs_info
            
        except Exception as e:
            self.driver_manager.logger.error(f"提取职位信息失败: {e}")
            return []
            


    def _save_info(self, user_info, jobs):
        """保存信息到文件"""
        try:
            # 保存用户信息
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            user_name = user_info['姓名']
            
            info_dir = os.path.join('user', user_name, 'info')
            os.makedirs(info_dir, exist_ok=True)
            
            with open(f"{info_dir}/user_info_{timestamp}.json", 'w', encoding='utf-8') as f:
                json.dump(user_info, f, ensure_ascii=False, indent=2)
            
            # 保存职位信息
            if jobs:
                jd_dir = os.path.join('user', user_name, 'jd')
                os.makedirs(jd_dir, exist_ok=True)
                
                for job in jobs:
                    filename = f"{job['职位名称'].replace(' ', '_')}_JD.md"
                    content = f"""# 职位详情

## 基本信息
- **职位名称**：{job['职位名称']}
- **工作地点**：{job['工作地点']}
- **工作经验**：{job['工作经验']}
- **学历要求**：{job['学历要求']}
- **薪资范围**：{job['薪资范围']}
- **招聘类型**：{job['招聘类型']}

## 职位描述
{job.get('职位详情', '暂无详细信息')}

---
*提取时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
                    
                    with open(f"{jd_dir}/{filename}", 'w', encoding='utf-8') as f:
                        f.write(content)
            
            self.driver_manager.logger.info("信息保存完成")
            
        except Exception as e:
            self.driver_manager.logger.error(f"保存信息失败: {e}")

def main():
    """主函数"""
    print("BOSS直聘自动化程序 - DrissionPage版本")
    print("=" * 50)
    
    try:
        automation = BossAutomation(headless=False)
        result = automation.login_and_extract_user_info()
        jobs_info = automation.extract_jobs()
        if result and jobs_info:
            automation._save_info(result, jobs_info)
        elif result:
            automation._save_info(result, [])
        else:
            print("无法保存信息：用户信息获取失败")

        if result:
            print("✅ 用户信息获取完成")
            print(f"用户: {result.get('姓名', '未知')}")
            
            if jobs_info:
                print(f"✅ 职位信息获取完成")
                print(f"职位数量: {len(jobs_info)}")
            else:
                print("⚠️ 职位信息获取失败")
        else:
            print("❌ 用户信息获取失败")
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
    finally:
        input("\n按回车键退出程序...")

if __name__ == "__main__":
    main()