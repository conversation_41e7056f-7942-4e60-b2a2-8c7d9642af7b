#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BOSS直聘自动化程序 V2.0
功能：自动访问BOSS直聘平台，等待用户登录后获取个人信息
改进：使用配置文件、增加错误处理、支持多种信息提取方式
"""

import time
import logging
import json
import asyncio
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import os
import sys
from datetime import datetime
from path_manager import path_manager
# 导入配置文件
try:
    from bosszp_config import *
except ImportError:
    # 如果配置文件不存在，使用默认配置
    BROWSER_CONFIG = {'headless': False, 'window_size': '1920,1080'}
    URLS = {
        'login': 'https://www.zhipin.com/web/user/?ka=header-login',
        'chat': 'https://www.zhipin.com/web/chat/index'
    }
    TIMEOUTS = {'login_wait': 300, 'page_load': 30, 'element_wait': 20}
    PERSONAL_CENTER_XPATH = "/html/body/div[2]/div[2]/div/div/div[9]/div[1]/div[1]/div/span[1]"
    USER_INFO_SELECTORS = {
        'name': ["//span[contains(@class, 'name')]", "//div[contains(@class, 'user-name')]"],
        'company': ["//span[contains(@class, 'company')]", "//div[contains(@class, 'company-name')]"],
        'avatar': ["//img[contains(@class, 'avatar')]", "//div[contains(@class, 'avatar-content')]//img"]
    }
    LOGGING_CONFIG = {'level': 'INFO', 'format': '%(asctime)s - %(levelname)s - %(message)s', 'file': 'boss_automation.log', 'encoding': 'utf-8'}
    WAIT_TIMES = {'page_load_extra': 3, 'after_click': 3, 'before_extract': 2}

class BossAutomationV2:
    def __init__(self, headless=None, debug_mode=False):
        """
        初始化BOSS直聘自动化程序 V2.0
        
        Args:
            headless (bool): 是否使用无头模式运行浏览器，None表示使用配置文件
            debug_mode (bool): 是否启用调试模式
        """
        self.driver = None
        self.headless = headless if headless is not None else BROWSER_CONFIG.get('headless', False)
        self.debug_mode = debug_mode
        self.setup_logging()
        self.user_info = {}
        self.complete_info = False
        
    def setup_logging(self):
        """设置日志配置"""
        try:
            log_file = LOGGING_CONFIG.get('file', 'boss_automation.log')
            log_level = getattr(logging, LOGGING_CONFIG.get('level', 'INFO'))
            log_format = LOGGING_CONFIG.get('format', '%(asctime)s - %(levelname)s - %(message)s')
            log_encoding = LOGGING_CONFIG.get('encoding', 'utf-8')
            
            logging.basicConfig(
                level=log_level,
                format=log_format,
                handlers=[
                    logging.FileHandler(log_file, encoding=log_encoding),
                    logging.StreamHandler()
                ]
            )
            self.logger = logging.getLogger(__name__)
            self.logger.info("日志系统初始化成功")
            
        except Exception as e:
            print(f"日志系统初始化失败: {str(e)}")
            # 使用基本的日志配置
            logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
            self.logger = logging.getLogger(__name__)
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless=new')  # 使用新的无头模式
                # 无头模式下的额外配置
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_argument('--disable-extensions')
                chrome_options.add_argument('--no-first-run')
                chrome_options.add_argument('--disable-default-apps')
                chrome_options.add_argument('--disable-popup-blocking')
                chrome_options.add_argument('--disable-web-security')
                chrome_options.add_argument('--allow-running-insecure-content')
                chrome_options.add_argument('--disable-features=VizDisplayCompositor')
                chrome_options.add_argument('--disable-ipc-flooding-protection')
                # 设置无头模式下的窗口大小
                chrome_options.add_argument('--window-size=1920,1080')
                chrome_options.add_argument('--start-maximized')
            
            # 添加浏览器选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument(f'--window-size={BROWSER_CONFIG.get("window_size", "1920,1080")}')
            chrome_options.add_argument(f'--user-agent={BROWSER_CONFIG.get("user_agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")}')
            
            # 禁用图片加载以提高速度（可选）
            if not self.debug_mode:
                chrome_options.add_argument('--blink-settings=imagesEnabled=false')
            
            # 添加实验性功能支持
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 尝试使用项目根目录的chromedriver
            chromedriver_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'chromedriver.exe')
            
            if os.path.exists(chromedriver_path):
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                self.logger.info(f"使用本地chromedriver: {chromedriver_path}")
            else:
                # 如果没有本地chromedriver，尝试自动下载
                try:
                    from webdriver_manager.chrome import ChromeDriverManager
                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    self.logger.info("使用自动下载的chromedriver")
                except ImportError:
                    self.logger.error("未安装webdriver-manager，请安装后重试")
                    raise
                
            # 设置页面加载策略
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)
            
            if not self.headless:
                self.driver.maximize_window()
                
            self.logger.info("Chrome浏览器驱动设置成功")
            
        except Exception as e:
            self.logger.error(f"设置Chrome驱动失败: {str(e)}")
            raise
            
    def wait_for_user_login(self, timeout=None):
        """
        等待用户登录
        
        Args:
            timeout (int): 等待超时时间（秒），None表示使用配置文件
            
        Returns:
            bool: 是否成功登录
        """
        try:
            timeout = timeout or TIMEOUTS.get('login_wait', 300)
            self.logger.info(f"等待用户登录（超时时间：{timeout}秒）...")
            self.logger.info("请在浏览器中完成登录操作...")
            
            # 等待页面跳转到聊天页面，这表示用户已登录
            WebDriverWait(self.driver, timeout).until(
                lambda driver: "chat/index" in driver.current_url
            )
            
            self.logger.info("检测到用户已登录，页面已跳转到聊天页面")
            return True
            
        except TimeoutException:
            self.logger.error(f"等待用户登录超时（{timeout}秒）")
            return False
        except Exception as e:
            self.logger.error(f"等待用户登录时发生错误: {str(e)}")
            return False
            
            
    def click_personal_center(self):
        """
        点击个人中心
        
        Returns:
            bool: 是否成功点击
        """
        try:
            self.logger.info("尝试点击个人中心...")
            
            # 使用配置文件中的xpath
            xpath = PERSONAL_CENTER_XPATH
            
            # 等待元素出现并可点击
            timeout = TIMEOUTS.get('element_wait', 20)
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((By.XPATH, xpath))
            )
            
            # 点击元素
            element.click()
            self.logger.info("成功点击个人中心")
            
            # 等待页面跳转
            after_click_wait = WAIT_TIMES.get('after_click', 3)
            time.sleep(after_click_wait)
            
            return True
            
        except TimeoutException:
            self.logger.error(f"等待个人中心元素超时（{TIMEOUTS.get('element_wait', 20)}秒）")
            return False
        except NoSuchElementException:
            self.logger.error("未找到个人中心元素")
            return False
        except Exception as e:
            self.logger.error(f"点击个人中心时发生错误: {str(e)}")
            return False
            
    def extract_user_info(self):
        """
        提取用户信息（姓名、公司名称、头像）
        
        Returns:
            dict: 包含用户信息的字典
        """
        try:
            self.logger.info("开始提取用户信息...")
            
            # 初始化用户信息
            self.user_info = {
                '姓名': '',
                '公司名称': '',
                '头像': '',
                '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 提取姓名
            self._extract_name()
            
            # 提取头像
            self._extract_avatar()
            # 提取公司名称
            # self._extract_company()
            

            
            
            return self.user_info
            
        except Exception as e:
            self.logger.error(f"提取用户信息时发生错误: {str(e)}")
            return {
                '姓名': '',
                '公司名称': '',
                '头像': '',
                '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '错误信息': str(e)
            }
    
    def _extract_name(self):
        """提取姓名"""
        try:
            name_selectors = USER_INFO_SELECTORS.get('name', [])
            
            for selector in name_selectors:
                try:
                    name_element = self.driver.find_element(By.XPATH, selector)
                    if name_element.text.strip():
                        self.user_info['姓名'] = name_element.text.strip()
                        self.logger.info(f"找到姓名: {self.user_info['姓名']}")
                        self.logger.info(f"姓名选择器: {selector}")
                        return
                except:
                    continue
            
            self.logger.warning("未找到姓名信息")
            
        except Exception as e:
            self.logger.error(f"提取姓名时发生错误: {str(e)}")
    
    def _extract_company(self):
        """提取公司名称"""
        try:
            self.driver.switch_to.frame(self.driver.find_elements(By.TAG_NAME, "iframe")[0])
            company_selectors = USER_INFO_SELECTORS.get('company', [])
            for selector in company_selectors:
                try:
                    company_element = self.driver.find_elements(By.XPATH, selector)[4]
                    if company_element.text.strip():
                        self.user_info['公司名称'] = company_element.text.strip()
                        self.logger.info(f"找到公司名称: {self.user_info['公司名称']}")
                        self.logger.info(f"公司名称选择器: {selector}")
                        self.driver.switch_to.default_content()

                        return
                except:
                    continue
            self.driver.switch_to.default_content()
            self.logger.warning("未找到公司名称信息")
            
        except Exception as e:
            self.logger.error(f"提取公司名称时发生错误: {str(e)}")
    
    def _extract_avatar(self):
        """提取头像"""
        try:
            avatar_selectors = USER_INFO_SELECTORS.get('avatar', [])
            
            for selector in avatar_selectors:
                try:
                    avatar_element = self.driver.find_element(By.XPATH, selector)
                    avatar_src = avatar_element.get_attribute('src')
                    if avatar_src:
                        self.user_info['头像'] = avatar_src
                        self.logger.info(f"找到头像: {self.user_info['头像']}")
                        self.logger.info(f"头像选择器: {selector}")
                        return
                except:
                    continue
            
            self.logger.warning("未找到头像信息")
            
        except Exception as e:
            self.logger.error(f"提取头像时发生错误: {str(e)}")
    
    def _save_user_info(self):
        """保存用户信息到文件"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            from path_manager import path_manager
            info_dir = path_manager.get_path('user', self.user_info['姓名'], 'info')
            if not os.path.exists(info_dir):
                os.makedirs(info_dir)
                self.logger.info(f"创建目录: {info_dir}")
            filename = f"{info_dir}/user_info_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.user_info, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"用户信息已保存到文件: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存用户信息到文件失败: {str(e)}")
    
    def _save_job_file(self, job_list):
        """保存职位信息到md文件"""
        try:
            # 确保info目录存在
            user_name = self.user_info.get('姓名', '未知用户')
            if not user_name or user_name == '未找到':
                user_name = '未知用户'
                
                
            jd_dir = path_manager.get_data_path('user', user_name, 'jd')
            if not os.path.exists(jd_dir):
                os.makedirs(jd_dir)
                self.logger.info(f"创建目录: {jd_dir}")
            
            saved_files = []
            for job in job_list:
                try:
                    # 生成文件名：JD{职位索引}_{职位名称}
                    job_index = job.get('职位索引', '未知')
                    job_name = job.get('职位名称', '未知职位')
                    
                    # 清理文件名中的非法字符
                    safe_job_name = "".join(c for c in job_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                    safe_job_name = safe_job_name.replace(' ', '_')
                    
                    filename = f"{safe_job_name}_JD.md"
                    file_path = os.path.join(jd_dir, filename)
                    
                    # 构建文件内容
                    content_lines = [
                        f"职位名称：{job.get('职位名称', '未知')}",
                        f"工作地点：{job.get('工作地点', '未知')}",
                        f"工作经验：{job.get('工作经验', '未知')}",
                        f"学历要求：{job.get('学历要求', '未知')}",
                        f"薪资范围：{job.get('薪资范围', '未知')}",
                        f"招聘类型：{job.get('招聘类型', '未知')}",
                        f"{job.get('职位详情', '未知')}"
                    ]
                    
                    # 写入文件
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(content_lines))
                    
                    saved_files.append(file_path)
                    self.logger.info(f"职位信息已保存到文件: {file_path}")
                    
                except Exception as e:
                    self.logger.error(f"保存职位 {job.get('职位名称', '未知')} 失败: {str(e)}")
                    continue
            
            if saved_files:
                self.logger.info(f"成功保存 {len(saved_files)} 个职位文件到 {jd_dir} 目录")
                return saved_files
            else:
                self.logger.warning("没有成功保存任何职位文件")
                return []
                
        except Exception as e:
            self.logger.error(f"保存职位文件时发生错误: {str(e)}")
            return []
          
    def click_job_management(self):
        """
        点击职位管理按钮
        
        Returns:
            bool: 是否成功点击
        """
        try:
            self.logger.info("尝试点击职位管理按钮...")
            
            # 等待页面加载完成
            time.sleep(1.2)
            
            # 尝试多种方式查找职位管理按钮
            try:
                from config import JOB_MANAGEMENT_SELECTORS
                job_management_selector = JOB_MANAGEMENT_SELECTORS
            except ImportError:
                # 如果配置文件不存在，使用默认选择器
                job_management_selector = "//a[contains(text(), '职位管理')]"
            # 查找job_management_selectors元素
                try:
                    element = self.driver.find_element(By.XPATH, job_management_selector)
                except Exception as e:
                    self.logger.warning(f"查找选择器 {job_management_selector} 时发生错误: {str(e)}")



            try:
                element.click()
            except Exception as e:
                self.logger.warning(f"职位管理点击选择器 {job_management_selector} 时发生错误: {str(e)}")
                pass
                
                # 等待页面跳转或加载
            time.sleep(1.2)
                
            return True

            
        except Exception as e:
            self.logger.error(f"点击职位管理按钮时发生错误: {str(e)}")
            return False
            
    def extract_job_list(self):
        """
        提取职位列表信息
        
        Returns:
            list: 包含所有职位信息的列表
        """
        try:
            self.logger.info("开始提取职位列表信息...")
            
            # 等待页面加载完成
            time.sleep(1.5)
            
            
            # 首先尝试在iframe中查找职位列表
            job_wrappers = self._find_jobs_in_iframes()
            

            if not job_wrappers:
                self.logger.error("未找到任何职位信息")
                return []
            

            return job_wrappers
            
        except Exception as e:
            self.logger.error(f"提取职位列表时发生错误: {str(e)}")
            return []
    
    def _find_jobs_in_iframes(self):
        """
        在iframe中查找职位列表
        
        Returns:
            list: 职位包装器元素列表
        """
        try:
            self.logger.info("开始在iframe中查找职位列表...")
            # 查找所有iframe
            iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
            self.logger.info(f"找到 {len(iframes)} 个iframe")
            # 查找第一个iframe下的div.job-list-warp，并打印其内容
            if iframes:
                try:
                    self.driver.switch_to.frame(iframes[0])
                    self.logger.info("已切换到第一个iframe")
                    # 查找div.job-list-warp
                    job_list_warp = self.driver.find_element(By.CSS_SELECTOR, "div.job-list-warp")
                    # 在job_list_warp中查找所有class为job-jobInfo-warp的li标签元素
                    job_wrappers = job_list_warp.find_elements(By.CSS_SELECTOR, "li.job-jobInfo-warp")
                    
                    # 构建职位信息列表
                    job_list = []
                    
                    for i in range(len(job_wrappers)):
                        try:
                            self.logger.info(f"处理第 {i+1} 个职位...")
                            job_wrappers = self.driver.find_elements(By.CSS_SELECTOR, "li.job-jobInfo-warp")
                            wrapper = job_wrappers[i]
                            # 获取职位标题
                            title_element = wrapper.find_element(By.CSS_SELECTOR, "div.job-title a")
                            title_text = title_element.text.strip()
                            self.logger.info(f"职位标题: {title_text}")
                            # 获取职位信息标签
                            info_labels = wrapper.find_element(By.CSS_SELECTOR, "div.info-labels").find_elements(By.TAG_NAME, "span")
                            
                            # 构建职位信息字典
                            job_info = {
                                '职位索引': i + 1,
                                '职位名称': title_text,
                                '工作地点': info_labels[0].text.strip() if len(info_labels) > 0 else '未找到',
                                '工作经验': info_labels[1].text.strip() if len(info_labels) > 1 else '未找到',
                                '学历要求': info_labels[2].text.strip() if len(info_labels) > 2 else '未找到',
                                '薪资范围': info_labels[3].text.strip() if len(info_labels) > 3 else '未找到',
                                '招聘类型': info_labels[4].text.strip() if len(info_labels) > 4 else '未找到'
                            }
                       
                            self.logger.info(f"职位信息: {job_info}")
                            
                            # 点击职位标题，进入职位详情页
                            title_element.click()
                            self.logger.info("已点击职位标题，进入职位详情页")
                            time.sleep(2)
                            self.driver.switch_to.default_content()
                            time.sleep(2)
                            # self.driver.switch_to.frame(iframes[0])

                            # TODO: 在此处完成需要在职位详情页进行的操作
                        
                            textarea = self.driver.find_elements(By.TAG_NAME, "iframe")[0]
                            self.driver.switch_to.frame(textarea)
                            jd_text = self.driver.find_elements(By.TAG_NAME, "textarea")[0]
                            job_info['职位详情'] = jd_text.get_attribute('value')
                            job_list.append(job_info)
                            self.driver.switch_to.default_content()
                            

                            self.driver.back()
                            self.logger.info("已返回职位列表页")
                            time.sleep(1)
                            iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
                            self.driver.switch_to.frame(iframes[0])
                            
                        except Exception as e:
                            self.logger.warning(f"处理第 {i+1} 个职位时发生错误: {str(e)}")
                            continue
                    
                    if job_list:
                        self.logger.info(f"成功提取到 {len(job_list)} 个职位信息")
                        # 切换回主页面
                        self.driver.switch_to.default_content()
                        return job_list
                    
                except Exception as e:
                    self.logger.warning(f"查找第一个iframe下的div.job-list-warp时发生错误: {str(e)}")
                    try:
                        self.driver.switch_to.default_content()
                    except:
                        pass
            return []
            
        except Exception as e:
            self.logger.error(f"在iframe中查找职位列表时发生错误: {str(e)}")
            # 确保切换回主页面
            try:
                self.driver.switch_to.default_content()
            except:
                pass
            return []
    
            
    def run_automation_1(self):
        """
        运行完整的自动化流程
        
        Returns:
            dict: 用户信息字典
        """
        try:
            self.logger.info("开始运行BOSS直聘自动化程序 V2.0...")
            
            # 设置浏览器驱动
            self.setup_driver()
            
            # 访问登录页面
            login_url = URLS.get('login', 'https://www.zhipin.com/web/user/?ka=header-login')
            self.logger.info(f"访问登录页面: {login_url}")
            self.driver.get(login_url)
        except Exception as e:
            self.logger.error(f"自动化程序运行失败: {str(e)}")
            return None
        

    def run_automation_2(self):
        try:
            # 等待用户登录
            if not self.wait_for_user_login():
                self.logger.error("用户登录失败或超时")
                return None
            
            time.sleep(1.2)
            # 检查是否有弹窗，如果有则关闭
            try:
                popup = self.driver.find_element(By.CLASS_NAME, "boss-popup__content")
                self.logger.info(f"检测到弹窗")
                close_btn = self.driver.find_element(By.CLASS_NAME, "boss-popup__close")
                close_btn.click()
                self.logger.info("检测到弹窗并已关闭")
            except Exception as e:
                # 没有弹窗或关闭失败都忽略
                pass
            
            time.sleep(0.7)
            # 点击个人中心
            # if not self.click_personal_center():
            #     self.logger.error("点击个人中心失败")
            #     return None
            
            # time.sleep(1)
            # # 等待个人中心页面加载
            # if not self.wait_for_page_load():
            #     self.logger.error("个人中心页面加载失败")
            #     return None
                        
            # 提取用户信息
            user_info = self.extract_user_info()
            
            # 打印用户信息
            self.logger.info("=" * 50)
            self.logger.info("用户信息提取结果:")
            for key, value in user_info.items():
                if key != '提取时间':
                    self.logger.info(f"{key}: {value}")
            self.logger.info("=" * 50)
            
            # 用户信息获取成功后，点击职位管理按钮
            if user_info and user_info.get('姓名') != '未找到':
                self.logger.info("用户信息获取成功，开始点击职位管理按钮...")
                if self.click_job_management():
                    self.logger.info("成功点击职位管理按钮")
                    
                    # 等待职位列表页面加载
                    time.sleep(1)
                    
                    # 获取职位列表信息
                    job_list = self.extract_job_list()
                    if job_list:
                        self.logger.info(f"成功获取到 {len(job_list)} 个职位信息")
                        # 将职位信息添加到用户信息中
                        user_info['职位列表'] = job_list
                        
                        # 保存职位
                        self._save_job_file(job_list)
                        # 保存包含职位信息的完整用户信息
                        self._save_user_info()
                        self.complete_info = True
                    else:
                        self.logger.warning("未获取到职位列表信息")
                else:
                    self.logger.warning("点击职位管理按钮失败")
            else:
                self.logger.warning("用户信息获取不完整，跳过点击职位管理按钮")
            
            self.close_driver()
            
            return user_info
            
        except Exception as e:
            self.logger.error(f"自动化程序运行失败: {str(e)}")
            return None
            
        finally:
            # 保持浏览器打开，让用户查看结果
            self.logger.info("自动化程序执行完成，浏览器将保持打开状态")
            self.logger.info("您可以手动查看页面内容，完成后请关闭浏览器")
            
    def close_driver(self):
        """关闭浏览器驱动"""
        if self.driver:
            self.driver.quit()
            self.logger.info("浏览器已关闭")

    # 新增：Boss直聘登录相关方法
    def get_login_qr_code(self):
        """获取Boss直聘登录二维码"""
        try:
            self.logger.info("🔐 开始获取Boss直聘登录二维码...")
            time.sleep(1)  # 增加等待时间
            self.logger.info(f"current_url: {self.driver.current_url}")
            
            # 等待页面完全加载
            WebDriverWait(self.driver, 20).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # 尝试多种方式获取二维码
            qr_selectors = [
                "#wrap > div > div.login-entry-page > div.login-register-content > div.btn-sign-switch.ewm-switch > div",
                ".ewm-switch",
                "[class*='ewm-switch']",
                "div[class*='btn-sign-switch']"
            ]
            
            qr_switch_element = None
            for selector in qr_selectors:
                try:
                    qr_switch_element = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue
            
            if not qr_switch_element:
                raise Exception("无法找到二维码切换按钮")
            
            # 点击二维码切换按钮
            self.driver.execute_script("arguments[0].click();", qr_switch_element)
            time.sleep(1)
            
            # 等待二维码区域加载
            qr_box_selectors = [
                "#wrap > div > div.login-entry-page > div.login-register-content > div.scan-app-wrapper > div.qr-code-box",
                ".qr-code-box",
                "[class*='qr-code-box']",
                "div[class*='scan-app-wrapper'] div[class*='qr-code']"
            ]
            
            qr_code_box = None
            for selector in qr_box_selectors:
                try:
                    qr_code_box = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue
            
            if not qr_code_box:
                raise Exception("无法找到二维码区域")
            
            # 截图整个页面作为备用
            self.driver.save_screenshot("boss_login_page.png")
            
            # 尝试获取二维码图片元素
            try:
                qr_img_element = qr_code_box.find_element(By.TAG_NAME, "img")
                self.logger.info("找到img标签的二维码")
            except:
                try:
                    qr_img_element = qr_code_box.find_element(By.TAG_NAME, "canvas")
                    self.logger.info("找到canvas标签的二维码")
                except:
                    # 如果都找不到，直接截图二维码区域
                    qr_code_box.screenshot("qr_code_area.png")
                    self.logger.info("未找到二维码图片元素，已截图二维码区域")
                    return {
                        "success": True,
                        "qr_code_data": "二维码已保存为qr_code_area.png",
                        "message": "二维码已保存，请查看本地文件"
                    }

            # 等待页面加载
            time.sleep(2)

            # 获取二维码图片
            try:
                if qr_img_element.tag_name == 'img':
                    qr_code_url = qr_img_element.get_attribute('src')
                    if qr_code_url and qr_code_url.startswith('http'):
                        self.logger.info("✅ 成功获取二维码URL")
                        return {
                            "success": True,
                            "qr_code_url": qr_code_url,
                            "message": "成功获取登录二维码"
                        }
                    else:
                        # 如果没有src属性，尝试获取base64数据
                        qr_code_data = qr_img_element.get_attribute('data-src') or qr_img_element.get_attribute('data-qr')
                        if qr_code_data:
                            self.logger.info("✅ 成功获取二维码数据")
                            return {
                                "success": True,
                                "qr_code_data": qr_code_data,
                                "message": "成功获取登录二维码数据"
                            }
                        else:
                            # 最后尝试截图二维码区域
                            qr_code_box.screenshot("qr_code.png")
                            self.logger.info("✅ 成功截图二维码区域")
                            return {
                                "success": True,
                                "qr_code_data": "二维码已保存为qr_code.png",
                                "message": "二维码已保存，请查看本地文件"
                            }
                elif qr_img_element.tag_name == 'canvas':
                    # 如果是canvas元素，尝试截图
                    qr_code_box.screenshot("qr_code_canvas.png")
                    self.logger.info("✅ 成功截图canvas二维码")
                    return {
                        "success": True,
                        "qr_code_data": "二维码已保存为qr_code_canvas.png",
                        "message": "二维码已保存，请查看本地文件"
                    }
                    
            except Exception as e:
                self.logger.error(f"❌ 获取二维码失败: {e}")
                # 如果获取失败，尝试截图整个二维码区域
                try:
                    qr_code_box.screenshot("qr_code_fallback.png")
                    return {
                        "success": True,
                        "qr_code_data": "二维码已保存为qr_code_fallback.png",
                        "message": "二维码已保存，请查看本地文件"
                    }
                except:
                    return {
                        "success": False,
                        "message": f"获取二维码失败: {str(e)}"
                    }
                
        except Exception as e:
            self.logger.error(f"❌ 获取登录二维码异常: {e}")
            # 尝试截图整个页面作为调试信息
            try:
                self.driver.save_screenshot("error_page.png")
                self.logger.info("已保存错误页面截图到error_page.png")
            except:
                pass
            
            return {
                "success": False,
                "message": f"获取登录二维码异常: {str(e)}"
            }


def main():
    """主函数"""
    print("=" * 60)
    print("BOSS直聘自动化程序 V2.0")
    print("=" * 60)
    print("此程序将自动访问BOSS直聘平台，等待您登录后获取个人信息")
    print("请按照以下步骤操作：")
    print("1. 程序将打开浏览器并访问登录页面")
    print("2. 请在浏览器中手动完成登录")
    print("3. 登录成功后程序将自动继续执行")
    print("4. 程序将获取您的姓名、公司名称和头像信息")
    print("5. 信息将保存到JSON文件中")
    print("6. 用户信息获取成功后，自动点击职位管理按钮")
    print("7. 自动获取职位列表中的所有职位信息")
    print("=" * 60)
    
    try:
        # 创建自动化实例
        automation = BossAutomationV2(headless=False, debug_mode=True)
        
        # 运行自动化程序
        automation.run_automation_1()
        automation.get_login_qr_code()
        user_info = automation.run_automation_2()

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {str(e)}")
    finally:
        input("\n按回车键退出程序...")


if __name__ == "__main__":
    main()
