#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BOSS直聘自动化程序 - 简化版
功能：自动访问BOSS直聘平台，获取用户信息和职位列表
"""

import time
import logging
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import os
from datetime import datetime
from bosszp_config import *
# # 配置常量
# CONFIG = {
#     'browser': {
#         'headless': False,
#         'window_size': '1920,1080',
#         'timeout': 30
#     },
#     'urls': {
#         'login': 'https://www.zhipin.com/web/user/?ka=header-login',
#         'chat': 'https://www.zhipin.com/web/chat/index'
#     },
#     'selectors': {
#         'personal_center': "//span[contains(@class, 'name')]",
#         'job_management': "//a[contains(text(), '职位管理')]",
#         'job_list': "li.job-jobInfo-warp",
#         'job_title': "div.job-title a",
#         'job_info': "div.info-labels span"
#     },
#     'wait_times': {
#         'page_load': 1.5,
#         'element': 10,
#         'login': 300
#     }
# }

class BossDriver:
    """浏览器驱动管理"""
    
    def __init__(self, headless=False, debug_mode=False):
        self.driver = None
        self.headless = headless
        self.debug_mode = debug_mode
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('boss_automation.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_driver(self):
        """设置Chrome驱动"""
        options = Options()
        if self.headless:
            options.add_argument('--headless=new')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-extensions')
            options.add_argument('--no-first-run')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-popup-blocking')
            options.add_argument('--disable-web-security')
            options.add_argument('--allow-running-insecure-content')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--disable-ipc-flooding-protection')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument(f'--window-size={BROWSER_CONFIG["window_size"]}')
        options.add_argument(f'--user-agent={BROWSER_CONFIG["user_agent"]}')


        # # 禁用图片加载以提高速度（可选）
        # if not self.debug_mode:
        #     options.add_argument('--blink-settings=imagesEnabled=false')

        # 尝试使用本地chromedriver
        chromedriver_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'chromedriver.exe')
        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
        else:
            # 自动下载
            try:
                from webdriver_manager.chrome import ChromeDriverManager
                service = Service(ChromeDriverManager().install())
                self.logger.info("使用自动下载的chromedriver")
            except ImportError:
                raise Exception("请安装webdriver-manager: pip install webdriver-manager")
        
        self.driver = webdriver.Chrome(service=service, options=options)
        self.driver.set_page_load_timeout(TIMEOUTS['page_load'])
        self.driver.implicitly_wait(TIMEOUTS['element_wait'])
        self.logger.info("浏览器驱动设置成功")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

class BossScraper:
    """数据提取器"""
    
    def __init__(self, driver):
        self.driver = driver
        self.logger = logging.getLogger(__name__)
    
    def wait_for_login(self, timeout=300):
        """等待用户登录"""
        timeout = timeout or TIMEOUTS['login_wait']
        try:
            WebDriverWait(self.driver, timeout).until(
                lambda d: "chat/index" in d.current_url
            )
            self.logger.info("用户已登录")
            return True
        except TimeoutException:
            self.logger.error("登录超时")
            return False
    
    def extract_user_info(self):
        """提取用户信息"""
        try:
            self.logger.info("开始提取用户信息")
            # 等待用户名
            # time.sleep(10)
            # name_element = self.driver.find_element(By.XPATH, USER_INFO_SELECTORS['name'])
            name_element = WebDriverWait(self.driver, TIMEOUTS['element_wait']).until(
                EC.presence_of_element_located((By.XPATH, USER_INFO_SELECTORS['name']))
            )
            if name_element.text.strip():
                self.logger.info("用户名提取成功")
            else:
                self.logger.error("用户名提取失败")
            name = name_element.text.strip()
            self.logger.info(f"用户名: {name}")

            avatar_element = self.driver.find_element(By.XPATH, USER_INFO_SELECTORS['avatar'])
            avatar_url = avatar_element.get_attribute('src')
            self.logger.info(f"头像URL: {avatar_url}")
            return {
                '姓名': name or '未知',
                '公司名称': '待提取',
                '头像': avatar_url or '',
                '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            self.logger.error(f"提取用户信息失败: {e}")
            return None
    
    def extract_jobs(self):
        """提取职位列表"""
        try:
            # 点击职位管理
            job_btn = self.driver.find_element(By.XPATH, JOB_MANAGEMENT_SELECTOR)
            job_btn.click()
            self.logger.info("成功进入职位管理页面")
            time.sleep(1.2)
            # 切换到iframe
            iframe = self.driver.find_element(By.TAG_NAME, "iframe")
            self.logger.info("开始获取岗位信息...")
            self.driver.switch_to.frame(iframe)
            
            # 获取职位列表
            job_warp = self.driver.find_element(By.CSS_SELECTOR, JOB_INFO_SELECTORS['warp'])
            job_wrappers = job_warp.find_elements(By.CSS_SELECTOR, JOB_INFO_SELECTORS['wrappers'])
            jobs = []
            
            for i, job_wrapper in enumerate(job_wrappers):  # 限制数量避免过长
                try:
                    self.logger.info(f"一共获取到{len(job_wrappers)}个职位")
                    self.logger.info(f"开始提取第{i+1}个职位信息...")

                    title_element = job_wrapper.find_element(By.CSS_SELECTOR, JOB_INFO_SELECTORS['title'])
                    title = title_element.text.strip()
                    info_spans = job_wrapper.find_element(By.CSS_SELECTOR, JOB_INFO_SELECTORS['info_labels']).find_elements(By.TAG_NAME, 'span')
                    info_spans = [span.text.strip() for span in info_spans]
                    self.logger.info(f"职位名称: {title}")
                    self.logger.info(f"岗位信息: {info_spans}")
                    
                    job_info = {
                        '职位索引': i + 1,
                        '职位名称': title,
                        '工作地点': info_spans[0].text if len(info_spans) > 0 else '未知',
                        '工作经验': info_spans[1].text if len(info_spans) > 1 else '未知',
                        '学历要求': info_spans[2].text if len(info_spans) > 2 else '未知',
                        '薪资范围': info_spans[3].text if len(info_spans) > 3 else '未知',
                        '招聘类型': info_spans[4].text if len(info_spans) > 4 else '未知'
                    }
                    jobs.append(job_info)
                    title_element.click()
                    time.sleep(1.2)#待测试
                    self.driver.switch_to.default_content()
                    time.sleep(1.2)#待测试
                    
                    textarea_iframe = self.driver.find_element(By.TAG_NAME, 'iframe')
                    self.driver.switch_to.frame(textarea_iframe)
                    textarea = self.driver.find_element(By.TAG_NAME, 'textarea')
                    jd_content = textarea.get_attribute('value')
                    job_info['职位详情'] = jd_content
                    self.driver.switch_to.default_content()
                    time.sleep(1.2)#待测试
                    jobs.append(job_info)
                    self.logger.info(f"第{i+1}个职位信息提取完成")

                except Exception as e:
                    self.logger.warning(f"提取第{i+1}个职位失败: {e}")
                    continue
            return jobs

            
        except Exception as e:
            self.logger.error(f"提取职位列表失败: {e}")
            self.driver.switch_to.default_content()
            return []

class BossAutomation:
    """主控制流程"""
    
    def __init__(self, headless=False):
        self.driver_manager = BossDriver(headless)
        self.scraper = None
    
    def close_boss_PCAD_dialog(self) -> bool:
        """关闭随机客户端弹窗"""
        try:
            # 等待页面加载
            time.sleep(1)
            
            # 判断是否出现弹窗
            dialog_xpath = '//img[contains(@class, "bosszp-bg")]'
            dialog_element = self.driver_manager.driver.find_element(By.XPATH, dialog_xpath)
            
            if dialog_element:
                self.driver_manager.logger.info("检测到弹窗-dont，准备关闭")
                
                # 查找关闭按钮
                close_button_xpath = '//i[@class="icon-close"]'
                close_button = self.driver_manager.driver.find_element(By.XPATH, close_button_xpath)
                
                if close_button:
                    close_button.click()
                    time.sleep(1)
                    self.driver_manager.logger.info("关闭弹窗成功")
                    return True
            else:
                self.driver_manager.logger.info("未检测到弹窗")
                return True
                
        except Exception as e:
            self.driver_manager.logger.error(f"关闭弹窗失败: {e}")
            return False



    def login_and_extract(self):
        """运行主流程"""
        try:
            # 设置驱动
            self.driver_manager.setup_driver()
            self.scraper = BossScraper(self.driver_manager.driver)
            
            # 访问登录页面
            self.driver_manager.driver.get(URLS['login'])
            self.driver_manager.logger.info("已访问登录页面，请手动登录")
            
            # 等待登录
            if not self.scraper.wait_for_login():
                return None
            
            self.close_boss_PCAD_dialog()
            # 提取用户信息
            user_info = self.scraper.extract_user_info()
            if not user_info:
                return None
            
            # 提取职位信息
            jobs = self.scraper.extract_jobs()
            user_info['职位列表'] = jobs
            
            # 保存信息
            self._save_info(user_info, jobs)
            return user_info
            
        except Exception as e:
            self.driver_manager.logger.error(f"获取用户信息失败失败: {e}")
            return None
    
    def _save_info(self, user_info, jobs):
        """保存信息到文件"""
        try:
            # 保存用户信息
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            user_name = user_info.get('姓名', '未知用户')
            
            info_dir = os.path.join('user', user_name, 'info')

            os.makedirs(info_dir, exist_ok=True)
            
            with open(f"{info_dir}/user_info_{timestamp}.json", 'w', encoding='utf-8') as f:
                json.dump(user_info, f, ensure_ascii=False, indent=2)
            
            # 保存职位信息
            if jobs:
                jd_dir = os.path.join('user', user_name, 'jd')
                os.makedirs(jd_dir, exist_ok=True)
                
                for job in jobs:
                    filename = f"{job['职位名称'].replace(' ', '_')}_JD.md"
                    content = f"""职位名称：{job['职位名称']}
工作地点：{job['工作地点']}
工作经验：{job['工作经验']}
学历要求：{job['学历要求']}
薪资范围：{job['薪资范围']}
招聘类型：{job['招聘类型']}"""
                    
                    with open(f"{jd_dir}/{filename}", 'w', encoding='utf-8') as f:
                        f.write(content)
            
            self.driver_manager.logger.info("信息保存完成")
            
        except Exception as e:
            self.driver_manager.logger.error(f"保存信息失败: {e}")

def main():
    """主函数"""
    print("BOSS直聘自动化程序 - 简化版")
    print("=" * 50)
    
    try:
        automation = BossAutomation(headless=False)
        result = automation.login_and_extract()
        
        if result:
            print("✅ 自动化流程完成")
            print(f"用户: {result.get('姓名', '未知')}")
            print(f"职位数量: {len(result.get('职位列表', []))}")
        else:
            print("❌ 自动化流程失败")
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
    finally:
        input("\n按回车键退出程序...")

if __name__ == "__main__":
    main()