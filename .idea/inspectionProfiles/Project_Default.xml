<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="LanguageDetectionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="301">
            <item index="0" class="java.lang.String" itemvalue="spacy" />
            <item index="1" class="java.lang.String" itemvalue="datasets" />
            <item index="2" class="java.lang.String" itemvalue="py2neo" />
            <item index="3" class="java.lang.String" itemvalue="partial-json-parser" />
            <item index="4" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="5" class="java.lang.String" itemvalue="jupyterlab_widgets" />
            <item index="6" class="java.lang.String" itemvalue="greenlet" />
            <item index="7" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="8" class="java.lang.String" itemvalue="executing" />
            <item index="9" class="java.lang.String" itemvalue="torchvision" />
            <item index="10" class="java.lang.String" itemvalue="markdown-it-py" />
            <item index="11" class="java.lang.String" itemvalue="qwen-vl-utils" />
            <item index="12" class="java.lang.String" itemvalue="funasr" />
            <item index="13" class="java.lang.String" itemvalue="gradio_client" />
            <item index="14" class="java.lang.String" itemvalue="conformer" />
            <item index="15" class="java.lang.String" itemvalue="cmd2" />
            <item index="16" class="java.lang.String" itemvalue="starlette" />
            <item index="17" class="java.lang.String" itemvalue="bleach" />
            <item index="18" class="java.lang.String" itemvalue="jupyter_server_terminals" />
            <item index="19" class="java.lang.String" itemvalue="fire" />
            <item index="20" class="java.lang.String" itemvalue="lxml" />
            <item index="21" class="java.lang.String" itemvalue="pyarrow-hotfix" />
            <item index="22" class="java.lang.String" itemvalue="pyreadline3" />
            <item index="23" class="java.lang.String" itemvalue="soupsieve" />
            <item index="24" class="java.lang.String" itemvalue="prometheus_client" />
            <item index="25" class="java.lang.String" itemvalue="torchaudio" />
            <item index="26" class="java.lang.String" itemvalue="lightning" />
            <item index="27" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="28" class="java.lang.String" itemvalue="hydra-colorlog" />
            <item index="29" class="java.lang.String" itemvalue="tensorboard-data-server" />
            <item index="30" class="java.lang.String" itemvalue="ray" />
            <item index="31" class="java.lang.String" itemvalue="contourpy" />
            <item index="32" class="java.lang.String" itemvalue="prettytable" />
            <item index="33" class="java.lang.String" itemvalue="pyairports" />
            <item index="34" class="java.lang.String" itemvalue="openai" />
            <item index="35" class="java.lang.String" itemvalue="jupyterlab_pygments" />
            <item index="36" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="37" class="java.lang.String" itemvalue="tensorboard" />
            <item index="38" class="java.lang.String" itemvalue="asttokens" />
            <item index="39" class="java.lang.String" itemvalue="av" />
            <item index="40" class="java.lang.String" itemvalue="vector-quantize-pytorch" />
            <item index="41" class="java.lang.String" itemvalue="narwhals" />
            <item index="42" class="java.lang.String" itemvalue="Mako" />
            <item index="43" class="java.lang.String" itemvalue="httpcore" />
            <item index="44" class="java.lang.String" itemvalue="idna" />
            <item index="45" class="java.lang.String" itemvalue="referencing" />
            <item index="46" class="java.lang.String" itemvalue="modelscope" />
            <item index="47" class="java.lang.String" itemvalue="networkx" />
            <item index="48" class="java.lang.String" itemvalue="cliff" />
            <item index="49" class="java.lang.String" itemvalue="json5" />
            <item index="50" class="java.lang.String" itemvalue="pluggy" />
            <item index="51" class="java.lang.String" itemvalue="numpy" />
            <item index="52" class="java.lang.String" itemvalue="opencv-python-headless" />
            <item index="53" class="java.lang.String" itemvalue="exceptiongroup" />
            <item index="54" class="java.lang.String" itemvalue="clldutils" />
            <item index="55" class="java.lang.String" itemvalue="win32-setctime" />
            <item index="56" class="java.lang.String" itemvalue="gdown" />
            <item index="57" class="java.lang.String" itemvalue="mdurl" />
            <item index="58" class="java.lang.String" itemvalue="langid" />
            <item index="59" class="java.lang.String" itemvalue="lark" />
            <item index="60" class="java.lang.String" itemvalue="websockets" />
            <item index="61" class="java.lang.String" itemvalue="annotated-types" />
            <item index="62" class="java.lang.String" itemvalue="importlib_metadata" />
            <item index="63" class="java.lang.String" itemvalue="aiofiles" />
            <item index="64" class="java.lang.String" itemvalue="jsonpointer" />
            <item index="65" class="java.lang.String" itemvalue="optimum" />
            <item index="66" class="java.lang.String" itemvalue="Send2Trash" />
            <item index="67" class="java.lang.String" itemvalue="openai-whisper" />
            <item index="68" class="java.lang.String" itemvalue="interegular" />
            <item index="69" class="java.lang.String" itemvalue="ruff" />
            <item index="70" class="java.lang.String" itemvalue="babel" />
            <item index="71" class="java.lang.String" itemvalue="debugpy" />
            <item index="72" class="java.lang.String" itemvalue="argon2-cffi" />
            <item index="73" class="java.lang.String" itemvalue="multidict" />
            <item index="74" class="java.lang.String" itemvalue="pytz" />
            <item index="75" class="java.lang.String" itemvalue="einops" />
            <item index="76" class="java.lang.String" itemvalue="pylatexenc" />
            <item index="77" class="java.lang.String" itemvalue="tiktoken" />
            <item index="78" class="java.lang.String" itemvalue="vllm" />
            <item index="79" class="java.lang.String" itemvalue="absl-py" />
            <item index="80" class="java.lang.String" itemvalue="protobuf" />
            <item index="81" class="java.lang.String" itemvalue="pygame" />
            <item index="82" class="java.lang.String" itemvalue="pywinpty" />
            <item index="83" class="java.lang.String" itemvalue="jiter" />
            <item index="84" class="java.lang.String" itemvalue="rootutils" />
            <item index="85" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="86" class="java.lang.String" itemvalue="compressed-tensors" />
            <item index="87" class="java.lang.String" itemvalue="gast" />
            <item index="88" class="java.lang.String" itemvalue="tinycss2" />
            <item index="89" class="java.lang.String" itemvalue="mkl" />
            <item index="90" class="java.lang.String" itemvalue="fsspec" />
            <item index="91" class="java.lang.String" itemvalue="semantic-version" />
            <item index="92" class="java.lang.String" itemvalue="pyzmq" />
            <item index="93" class="java.lang.String" itemvalue="msgspec" />
            <item index="94" class="java.lang.String" itemvalue="pyparsing" />
            <item index="95" class="java.lang.String" itemvalue="pypinyin" />
            <item index="96" class="java.lang.String" itemvalue="optuna" />
            <item index="97" class="java.lang.String" itemvalue="kaldiio" />
            <item index="98" class="java.lang.String" itemvalue="tokenizers" />
            <item index="99" class="java.lang.String" itemvalue="isoduration" />
            <item index="100" class="java.lang.String" itemvalue="xformer" />
            <item index="101" class="java.lang.String" itemvalue="fqdn" />
            <item index="102" class="java.lang.String" itemvalue="iniconfig" />
            <item index="103" class="java.lang.String" itemvalue="langdetect" />
            <item index="104" class="java.lang.String" itemvalue="aiohappyeyeballs" />
            <item index="105" class="java.lang.String" itemvalue="cryptography" />
            <item index="106" class="java.lang.String" itemvalue="orjson" />
            <item index="107" class="java.lang.String" itemvalue="lm-format-enforcer" />
            <item index="108" class="java.lang.String" itemvalue="gekko" />
            <item index="109" class="java.lang.String" itemvalue="altair" />
            <item index="110" class="java.lang.String" itemvalue="bitsandbytes" />
            <item index="111" class="java.lang.String" itemvalue="pytorch-lightning" />
            <item index="112" class="java.lang.String" itemvalue="widgetsnbextension" />
            <item index="113" class="java.lang.String" itemvalue="mmengine-lite" />
            <item index="114" class="java.lang.String" itemvalue="librosa" />
            <item index="115" class="java.lang.String" itemvalue="distlib" />
            <item index="116" class="java.lang.String" itemvalue="cfgv" />
            <item index="117" class="java.lang.String" itemvalue="shellingham" />
            <item index="118" class="java.lang.String" itemvalue="pbr" />
            <item index="119" class="java.lang.String" itemvalue="matplotlib-inline" />
            <item index="120" class="java.lang.String" itemvalue="webcolors" />
            <item index="121" class="java.lang.String" itemvalue="wcwidth" />
            <item index="122" class="java.lang.String" itemvalue="matcha-tts" />
            <item index="123" class="java.lang.String" itemvalue="rouge" />
            <item index="124" class="java.lang.String" itemvalue="edge-tts" />
            <item index="125" class="java.lang.String" itemvalue="Jinja2" />
            <item index="126" class="java.lang.String" itemvalue="uri-template" />
            <item index="127" class="java.lang.String" itemvalue="sounddevice" />
            <item index="128" class="java.lang.String" itemvalue="tomlkit" />
            <item index="129" class="java.lang.String" itemvalue="phonemizer" />
            <item index="130" class="java.lang.String" itemvalue="lightning-utilities" />
            <item index="131" class="java.lang.String" itemvalue="tomli" />
            <item index="132" class="java.lang.String" itemvalue="mistral_common" />
            <item index="133" class="java.lang.String" itemvalue="typer" />
            <item index="134" class="java.lang.String" itemvalue="identify" />
            <item index="135" class="java.lang.String" itemvalue="importlib_resources" />
            <item index="136" class="java.lang.String" itemvalue="prompt_toolkit" />
            <item index="137" class="java.lang.String" itemvalue="stevedore" />
            <item index="138" class="java.lang.String" itemvalue="parso" />
            <item index="139" class="java.lang.String" itemvalue="ipython" />
            <item index="140" class="java.lang.String" itemvalue="rich" />
            <item index="141" class="java.lang.String" itemvalue="sentence-transformers" />
            <item index="142" class="java.lang.String" itemvalue="prometheus-fastapi-instrumentator" />
            <item index="143" class="java.lang.String" itemvalue="fastjsonschema" />
            <item index="144" class="java.lang.String" itemvalue="soundfile" />
            <item index="145" class="java.lang.String" itemvalue="inflect" />
            <item index="146" class="java.lang.String" itemvalue="aiohttp" />
            <item index="147" class="java.lang.String" itemvalue="frozendict" />
            <item index="148" class="java.lang.String" itemvalue="httpx" />
            <item index="149" class="java.lang.String" itemvalue="PyYAML" />
            <item index="150" class="java.lang.String" itemvalue="pycparser" />
            <item index="151" class="java.lang.String" itemvalue="rfc3986" />
            <item index="152" class="java.lang.String" itemvalue="Pygments" />
            <item index="153" class="java.lang.String" itemvalue="umap-learn" />
            <item index="154" class="java.lang.String" itemvalue="multiprocess" />
            <item index="155" class="java.lang.String" itemvalue="shortuuid" />
            <item index="156" class="java.lang.String" itemvalue="comtypes" />
            <item index="157" class="java.lang.String" itemvalue="pycountry" />
            <item index="158" class="java.lang.String" itemvalue="uvicorn" />
            <item index="159" class="java.lang.String" itemvalue="csvw" />
            <item index="160" class="java.lang.String" itemvalue="pywin32" />
            <item index="161" class="java.lang.String" itemvalue="terminado" />
            <item index="162" class="java.lang.String" itemvalue="comm" />
            <item index="163" class="java.lang.String" itemvalue="pydantic" />
            <item index="164" class="java.lang.String" itemvalue="transformers" />
            <item index="165" class="java.lang.String" itemvalue="pyperclip" />
            <item index="166" class="java.lang.String" itemvalue="pydub" />
            <item index="167" class="java.lang.String" itemvalue="loguru" />
            <item index="168" class="java.lang.String" itemvalue="ipykernel" />
            <item index="169" class="java.lang.String" itemvalue="attrs" />
            <item index="170" class="java.lang.String" itemvalue="psutil" />
            <item index="171" class="java.lang.String" itemvalue="simplejson" />
            <item index="172" class="java.lang.String" itemvalue="bibtexparser" />
            <item index="173" class="java.lang.String" itemvalue="HyperPyYAML" />
            <item index="174" class="java.lang.String" itemvalue="flatbuffers" />
            <item index="175" class="java.lang.String" itemvalue="jupyter_server" />
            <item index="176" class="java.lang.String" itemvalue="natsort" />
            <item index="177" class="java.lang.String" itemvalue="platformdirs" />
            <item index="178" class="java.lang.String" itemvalue="propcache" />
            <item index="179" class="java.lang.String" itemvalue="msgpack" />
            <item index="180" class="java.lang.String" itemvalue="einx" />
            <item index="181" class="java.lang.String" itemvalue="gguf" />
            <item index="182" class="java.lang.String" itemvalue="isodate" />
            <item index="183" class="java.lang.String" itemvalue="pandocfilters" />
            <item index="184" class="java.lang.String" itemvalue="wget" />
            <item index="185" class="java.lang.String" itemvalue="auto_gptq" />
            <item index="186" class="java.lang.String" itemvalue="sniffio" />
            <item index="187" class="java.lang.String" itemvalue="segments" />
            <item index="188" class="java.lang.String" itemvalue="stack-data" />
            <item index="189" class="java.lang.String" itemvalue="ruamel.yaml.clib" />
            <item index="190" class="java.lang.String" itemvalue="zipp" />
            <item index="191" class="java.lang.String" itemvalue="ipywidgets" />
            <item index="192" class="java.lang.String" itemvalue="pyarrow" />
            <item index="193" class="java.lang.String" itemvalue="uritemplate" />
            <item index="194" class="java.lang.String" itemvalue="scipy" />
            <item index="195" class="java.lang.String" itemvalue="watchfiles" />
            <item index="196" class="java.lang.String" itemvalue="opencv-python" />
            <item index="197" class="java.lang.String" itemvalue="html5lib-modern" />
            <item index="198" class="java.lang.String" itemvalue="torch" />
            <item index="199" class="java.lang.String" itemvalue="overrides" />
            <item index="200" class="java.lang.String" itemvalue="python-multipart" />
            <item index="201" class="java.lang.String" itemvalue="addict" />
            <item index="202" class="java.lang.String" itemvalue="mistune" />
            <item index="203" class="java.lang.String" itemvalue="pandas" />
            <item index="204" class="java.lang.String" itemvalue="termcolor" />
            <item index="205" class="java.lang.String" itemvalue="autopage" />
            <item index="206" class="java.lang.String" itemvalue="pre_commit" />
            <item index="207" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="208" class="java.lang.String" itemvalue="yarl" />
            <item index="209" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="210" class="java.lang.String" itemvalue="notebook_shim" />
            <item index="211" class="java.lang.String" itemvalue="outlines" />
            <item index="212" class="java.lang.String" itemvalue="cmaes" />
            <item index="213" class="java.lang.String" itemvalue="colorlog" />
            <item index="214" class="java.lang.String" itemvalue="arrow" />
            <item index="215" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="216" class="java.lang.String" itemvalue="xformers" />
            <item index="217" class="java.lang.String" itemvalue="nbclient" />
            <item index="218" class="java.lang.String" itemvalue="cycler" />
            <item index="219" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="220" class="java.lang.String" itemvalue="httptools" />
            <item index="221" class="java.lang.String" itemvalue="webrtcvad" />
            <item index="222" class="java.lang.String" itemvalue="frozenlist" />
            <item index="223" class="java.lang.String" itemvalue="safetensors" />
            <item index="224" class="java.lang.String" itemvalue="certifi" />
            <item index="225" class="java.lang.String" itemvalue="anyio" />
            <item index="226" class="java.lang.String" itemvalue="accelerate" />
            <item index="227" class="java.lang.String" itemvalue="ffmpeg" />
            <item index="228" class="java.lang.String" itemvalue="Markdown" />
            <item index="229" class="java.lang.String" itemvalue="sympy" />
            <item index="230" class="java.lang.String" itemvalue="xxhash" />
            <item index="231" class="java.lang.String" itemvalue="opencv-contrib-python" />
            <item index="232" class="java.lang.String" itemvalue="jupyter-lsp" />
            <item index="233" class="java.lang.String" itemvalue="nodeenv" />
            <item index="234" class="java.lang.String" itemvalue="diffusers" />
            <item index="235" class="java.lang.String" itemvalue="jupyter_client" />
            <item index="236" class="java.lang.String" itemvalue="pure_eval" />
            <item index="237" class="java.lang.String" itemvalue="rdflib" />
            <item index="238" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="239" class="java.lang.String" itemvalue="coloredlogs" />
            <item index="240" class="java.lang.String" itemvalue="fonttools" />
            <item index="241" class="java.lang.String" itemvalue="tbb" />
            <item index="242" class="java.lang.String" itemvalue="peft" />
            <item index="243" class="java.lang.String" itemvalue="virtualenv" />
            <item index="244" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="245" class="java.lang.String" itemvalue="ffmpy" />
            <item index="246" class="java.lang.String" itemvalue="diskcache" />
            <item index="247" class="java.lang.String" itemvalue="language-tags" />
            <item index="248" class="java.lang.String" itemvalue="gradio" />
            <item index="249" class="java.lang.String" itemvalue="async-timeout" />
            <item index="250" class="java.lang.String" itemvalue="more-itertools" />
            <item index="251" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="252" class="java.lang.String" itemvalue="nvidia-ml-py" />
            <item index="253" class="java.lang.String" itemvalue="cloudpickle" />
            <item index="254" class="java.lang.String" itemvalue="types-python-dateutil" />
            <item index="255" class="java.lang.String" itemvalue="jsonschema-specifications" />
            <item index="256" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="257" class="java.lang.String" itemvalue="rpds-py" />
            <item index="258" class="java.lang.String" itemvalue="WeTextProcessing" />
            <item index="259" class="java.lang.String" itemvalue="Cython" />
            <item index="260" class="java.lang.String" itemvalue="hydra-optuna-sweeper" />
            <item index="261" class="java.lang.String" itemvalue="optimum-habana" />
            <item index="262" class="java.lang.String" itemvalue="pytest" />
            <item index="263" class="java.lang.String" itemvalue="intel-openmp" />
            <item index="264" class="java.lang.String" itemvalue="tzdata" />
            <item index="265" class="java.lang.String" itemvalue="humanfriendly" />
            <item index="266" class="java.lang.String" itemvalue="dlinfo" />
            <item index="267" class="java.lang.String" itemvalue="fastapi" />
            <item index="268" class="java.lang.String" itemvalue="whisper" />
            <item index="269" class="java.lang.String" itemvalue="grpcio" />
            <item index="270" class="java.lang.String" itemvalue="aiosignal" />
            <item index="271" class="java.lang.String" itemvalue="paddlespeech" />
            <item index="272" class="java.lang.String" itemvalue="markdown" />
            <item index="273" class="java.lang.String" itemvalue="tqdm" />
            <item index="274" class="java.lang.String" itemvalue="python-docx" />
            <item index="275" class="java.lang.String" itemvalue="pymupdf" />
            <item index="276" class="java.lang.String" itemvalue="requests" />
            <item index="277" class="java.lang.String" itemvalue="pyaudio" />
            <item index="278" class="java.lang.String" itemvalue="python-jose" />
            <item index="279" class="java.lang.String" itemvalue="passlib" />
            <item index="280" class="java.lang.String" itemvalue="wave" />
            <item index="281" class="java.lang.String" itemvalue="bcrypt" />
            <item index="282" class="java.lang.String" itemvalue="PyMuPDF" />
            <item index="283" class="java.lang.String" itemvalue="faiss-cpu" />
            <item index="284" class="java.lang.String" itemvalue="pytesseract" />
            <item index="285" class="java.lang.String" itemvalue="Pillow" />
            <item index="286" class="java.lang.String" itemvalue="chromadb" />
            <item index="287" class="java.lang.String" itemvalue="sqlite3" />
            <item index="288" class="java.lang.String" itemvalue="ollama" />
            <item index="289" class="java.lang.String" itemvalue="duckduckgo-search" />
            <item index="290" class="java.lang.String" itemvalue="asyncio-mqtt" />
            <item index="291" class="java.lang.String" itemvalue="pypdf" />
            <item index="292" class="java.lang.String" itemvalue="pytest-asyncio" />
            <item index="293" class="java.lang.String" itemvalue="python-jwt" />
            <item index="294" class="java.lang.String" itemvalue="langchain-community" />
            <item index="295" class="java.lang.String" itemvalue="langchain" />
            <item index="296" class="java.lang.String" itemvalue="unstructured" />
            <item index="297" class="java.lang.String" itemvalue="langchain-core" />
            <item index="298" class="java.lang.String" itemvalue="gtts" />
            <item index="299" class="java.lang.String" itemvalue="dashscope" />
            <item index="300" class="java.lang.String" itemvalue="nvidia-ml-py3" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>