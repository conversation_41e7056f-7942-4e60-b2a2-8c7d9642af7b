------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 梁朝策

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 广东科技学院 本科（软件工程）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 23-30K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **第一条核心职责匹配情况：**
        - 候选人简历中明确提到：“主导AI Agent工作流的0-1规划设计，确保其满足客户应用场景的需求，提升产品的适用性和灵活性。”
        - 同时提到：“构思ASR+LLM+TTS全流程智能客户/数字人交互方案，提升客户人机交互的智能化水平。”
        - 此外，候选人主导了多个AI相关产品从0到1的设计与落地，如“扶梯AI不安全行为智能监测平台”、“设施设备管理系统”等，体现出完整的AI产品生命周期管理能力。
    - **第二条核心职责匹配情况：**
        - 候选人负责“大语言模型的选型与训练，优化模型性能”，并“负责DeepSeek大模型的训练与调优”，体现出将算法能力转化为产品功能的能力。
        - 在“温氏AI预测性维护项目”中，候选人与算法工程师合作进行模型训练参数调优、数据清洗及精度提升，进一步证明其技术与业务结合能力。
    - **第三条核心职责匹配情况：**
        - 候选人简历中提到“推动设备管理的云端化，优化资源配置，提高客户运营效率”，以及“RAG知识库的应用和解决方案支持”，表明其具备通过数据优化产品表现的经验。
        - 在“省电信 MSS-L3 上云”项目中，候选人主导了国产化及云原生适配工作，并实现系统效率与稳定性的提升，体现出数据驱动优化的能力。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有多个AI Agent相关产品从0到1的设计与落地经验，涵盖大模型选型、Prompt策略、交互流程设计等。
    - 技术与业务结合能力强，具备算法调优、模型部署、用户价值转化的综合能力。
    - 熟悉主流AI产品评估与优化方式，具备数据驱动决策的经验。
- **风险与不足:**
    - 无硬性门槛“潜在风险”或“不匹配”项。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人完全符合JD中所有硬性门槛要求（学历、年龄、薪资均未设置门槛），并在核心职责方面展现出高度匹配的项目经验与能力，包括AI Agent产品全流程设计、技术与业务转化能力、数据驱动优化实践等。