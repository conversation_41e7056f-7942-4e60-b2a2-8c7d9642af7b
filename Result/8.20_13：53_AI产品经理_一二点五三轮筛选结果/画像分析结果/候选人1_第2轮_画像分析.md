# 候选人专业画像分析报告

**基本信息:**

- **姓名:** 梁朝策
- **分析日期:** 2025-04-05

**专业身份与能力层级:**

- **主要专业身份 (80-100分):** 人工智能产品管理（AI Agent、大模型、机器视觉）——**92分**  
  简历中多次出现AI Agent策略、大模型训练与调优、RAG、机器视觉（YOLO）、算法策略设计等关键词，且贯穿多个项目，具备完整的AI产品设计、落地与市场推广能力。

- **重要辅助技能 (60-79分):** 工业互联网与IoT平台产品设计 ——**75分**  
  候选人主导设计了多个设备管理云平台（扶梯AI监测平台、设施设备管理系统、设备管理SaaS平台），具备从边缘计算到云平台的全链路产品设计经验，且有明确的市场转化成果。

- **边缘接触能力 (40-59分):** 低代码平台与企业数字化产品设计 ——**55分**  
  虽然主导ECOP低代码平台核心流程设计，但描述相对简略，缺乏深度技术细节。低代码平台属于其产品线之一，非核心聚焦方向。

**能力边界:**

- **擅长:**  
  - AI Agent产品设计与工作流构建（RAGFlow、Coze、FastGPT等）
  - 大模型选型、训练与调优（DeepSeek）
  - 机器视觉+AI算法集成产品设计（YOLO、边缘计算、IoT）
  - 工业互联网/IoT云平台产品全流程管理

- **适合:**  
  - 企业级SaaS平台设计（设备管理、设施管理）
  - 低代码平台产品设计与工具开发
  - 项目管理（PMP、敏捷认证、CMMI 5流程）

- **不适合:**  
  - 纯技术开发（如算法实现、代码开发）
  - 非AI类传统软件产品深度设计
  - 弱电智能化、安防系统、楼宇自控等（简历中无相关关键词）

**职业发展轨迹:**

- **一致性:** **高**  
  职业路径从项目经理（深圳蓝凌）转向产品经理（中数通、鲁邦通、纤帆渡瀚），聚焦AI+IoT产品设计，具备清晰的技术演进路径和能力积累。

- **专业深度:** **深**  
  在AI Agent、大模型应用、机器视觉集成方面具备从策略设计到工程落地的完整能力，多个项目均涉及AI与IoT融合，且具备明确的市场成果（如转化客户、终端设备部署量等）。

**综合评估与建议:**

- **专业比重总分:** **90分**
- **可信度:** **高**（项目描述具体，有明确客户案例、产品类型、部署规模及成果）
- **最终专业身份:** **AI+IoT融合型产品经理（聚焦AI Agent与大模型应用）**

---