------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 杜嘉琪

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 上海财经大学 计算机科学与技术专业 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 出生于1994年，当前年龄为30岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（月薪上限为20K）
    - **候选人期望:** 简历中未明确提供期望薪资
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - 简历中描述的工作经历集中在**软件工程师**岗位，参与**华为云平台**和**腾讯云平台**的前后端开发工作，包括API部署、代码审查、前端设计与后端服务实现。
    - 但简历中**未提及任何与产品定义、用户需求调研、产品路线图制定、市场策略制定、用户体验设计等产品管理相关职责或项目经验**。
    - 缺乏对AI产品设计、技术可行性评估、产品迭代管理、跨团队协调等核心职责的直接证据。
    - 技能列表中虽然包含技术栈（如Python、Spring Boot、Docker等），但**未体现产品管理工具（如Axure/Figma）、PRD撰写经验、产品规划能力等JD明确要求的硬技能**。

- **匹配度结论:** 低

**3. 综合评估**

- **优势:**
    - 技术背景扎实，具备云平台开发经验，熟悉多种开发框架与部署工具。
    - 拥有华为与腾讯两家大厂的工作经历，显示出较强的技术实现能力。

- **风险与不足:**
    - 候选人简历显示其为**技术开发角色**，而非产品管理方向，**缺乏与AI产品经理岗位匹配的核心职责经验**。
    - 简历中未体现产品需求文档撰写、用户调研、产品路线图制定、市场策略制定等关键能力。
    - 尽管技能列表丰富，但**未体现与产品管理相关的硬技能**，如Axure/Figma原型设计、PRD撰写、产品迭代管理等。

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 简历中未体现与AI产品经理岗位所需核心职责相关的经验与能力，如产品定义、PRD撰写、路线图规划、市场策略制定等，整体匹配度较低，不符合岗位要求。