------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾杰

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上  
    - **候选人情况:** 硕士（非全日制）  
    - **匹配结果:** 匹配  
- **年龄要求:**
    - **JD 要求:** 35岁以下  
    - **候选人情况:** 38岁  
    - **匹配结果:** 潜在风险  
    - **说明:** 38岁 > 35岁，但 ≤ 35 * 1.2 = 42岁，属于“潜在风险”范围。  
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪  
    - **候选人期望:** 简历中未提供明确期望薪资  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1（产品功能与用户体验）**：在“文稿在线项目组”中明确描述了AI技术可行性验证、Prompt工程、UI优化、文本解析与语义理解等AI产品设计内容，体现了对AI产品用户体验的深入理解与实践。
    - **职责2（产品路线图与迭代）**：在多个项目中均有清晰的产品迭代规划，如“配电网无人机智能巡检系统”项目中详细描述了基础功能、重点功能、长期规划的演进路径，体现了路线图规划能力。
    - **职责3（跨职能团队协作）**：曾杰在多个项目中均有与研发、工程、电网系统对接的经验，如“API对接电网管理系统”、“推动项目代建”、“协调多部门评审”等，体现了良好的跨职能协作能力。
    - **职责4（市场定位与策略）**：在“无人机智能操作系统4.0”项目中提到“调整产品策略”，在“文稿在线项目”中也提到“商业化验证”、“SaaS模式+租赁场景灵活划分”等市场策略设计内容，具备一定的市场敏感度。

- **匹配度结论:** 高  

**3. 综合评估**

- **优势:**  
    - 拥有多个AI相关产品设计与落地经验，涵盖文档处理、无人机巡检等场景，具备较强的技术理解与产品设计能力。  
    - 具备PMP和NPDP认证，项目管理与产品管理经验丰富，能独立完成PRD、需求分析、路线图规划等工作。  
    - 在AI技术可行性判断、用户需求调研、产品迭代管理、市场策略设计等方面均有实际项目支撑。  

- **风险与不足:**  
    - **年龄超出JD要求（38岁 > 35岁）**，属于“潜在风险”项。  
    - 未明确提及Axure/Figma等原型工具的使用经验，但其UI优化、UML图绘制等经验可视为间接匹配。  

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 无硬性门槛“不匹配”项  
    2. ✅ 核心职责匹配度为“高”  

- **通过条件：**
    1. ✅ 无硬性门槛“不匹配”项  
    2. ✅ 核心职责匹配度为“高”  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历符合要求，虽年龄略超限但未超过20%阈值，属于“潜在风险”而非淘汰项；在AI产品设计、路线图规划、跨职能协作、市场策略制定等核心职责方面均有充分项目经验支撑，整体匹配度为“高”。