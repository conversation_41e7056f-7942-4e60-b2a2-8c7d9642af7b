------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 陈兴

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 23岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（即最高为20K×13=260K/年）
    - **候选人期望:** 10-15K（即期望年薪上限为15K×12=180K/年）
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **设计和定义AI产品的功能与用户体验：**  
      简历中提到候选人负责多个AI平台的**功能设计与用户体验优化**，如内容安全审核平台、AI智能客服平台等，均涉及**用户需求收集、功能流程优化、Prompt设计与测试**，体现其在产品功能与体验设计方面的能力。
    - **制定产品路线图与优先级：**  
      在文化内容审核平台项目中，候选人“制定推进规划及里程碑计划，推动平台快速上线”，说明其具备**路线图规划与优先级管理经验**。
    - **协调跨职能团队推进AI产品开发与落地：**  
      简历中多次提到“推动LLM模型与审核流程融合”、“协调模型选型-能力强化-数据支撑的完整AI框架”，体现其**与技术团队协作推动产品落地的能力**。
    - **制定AI产品的市场定位与竞争策略：**  
      简历中未明确提及市场定位或竞争策略相关经验，但其主导的多个平台项目**覆盖游戏公告、美术素材、客服系统等多场景应用**，可能间接体现一定的市场理解能力，但证据不足。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**  
  - 一年工作经验即担任AI产品经理岗位，主导多个AI平台项目，涵盖从需求调研、功能设计、技术整合到产品落地的全流程。  
  - 熟悉LLM、RAG、Fewshot等AI技术，具备良好的技术理解与产品落地平衡能力。  
  - 项目经验丰富，涉及审核、客服、对话机器人等多个AI应用场景，具备较强的PRD撰写与原型设计能力。

- **风险与不足:**  
  - 薪资期望略低于JD要求范围，可能在后续沟通中存在调整空间（但未超过20%阈值，属于匹配）。  
  - 缺乏明确的市场定位与竞争策略制定经验的直接证据，可能影响该职责项的深度匹配。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均满足硬性门槛要求，且具备丰富的AI产品策划与落地经验，能够胜任岗位的核心职责。尽管市场定位相关经验证据不足，但整体匹配度仍属“高”，符合通过标准。