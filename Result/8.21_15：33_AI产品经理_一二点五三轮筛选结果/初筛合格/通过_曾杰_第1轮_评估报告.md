------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：曾杰    
**年龄**：38岁 

**工作经验**：10年以上

 **学历**：硕士

**当前职位**：产品经理

**期望薪资**：未提供



**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 硕士
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 38岁
    - **匹配结果:** 潜在风险
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪
    - **候选人期望:** 未提供
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - **职责1（产品功能与用户体验）：** 候选人在“文稿在线项目组”中主导了AI技术可行性验证，并设计了基于Prompt的文档理解模块，具备AI产品功能设计经验。在“配电网无人机巡检系统”项目中，他设计了缺陷识别系统，涉及AI模型的阈值设定与人工复核机制，体现了高可用性与技术可行性的权衡能力。
    - **职责2（产品路线图与迭代）：** 在“无人机智能操作系统4.0”项目中，候选人主导了从需求分析到交付的全流程管理，并缩短了产品周期。在“文稿在线”项目中也明确提到了“下一步迭代”和“长期规划”，体现出路线图制定与迭代管理能力。
    - **职责3（跨职能团队协作）：** 候选人在多个项目中均涉及与研发、工程、市场、合作伙伴的协作。例如在“无人机智能操作系统”项目中，他主导了与API合作伙伴的集成工作，体现了良好的跨职能协作能力。
    - **职责4（市场定位与竞争策略）：** 候选人在“无人机智能操作系统”项目中提到“产品矩阵推广”、“制定产品策略”等内容，并在“文稿在线”项目中进行了商业化验证，涉及SaaS模式探索，具备市场策略制定经验。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    1. 多年AI产品设计与落地经验，涵盖从需求验证、功能设计到商业化落地的全流程。
    2. 具备技术理解能力，掌握Prompt工程、LangChain、RAG/Agent等AI相关技术，能与开发团队深入协作。
    3. 拥有PMP和NPDP认证，具备专业的产品管理与项目推进能力。
    4. 多个大型AI项目成功案例，具备商业化成果与行业影响力（如云南电网标准制定）。

- **风险与不足:**
    1. 年龄超过JD要求上限（38岁 > 35岁），超出6%，属于**潜在风险**。
    2. 期望薪资未提供，无法判断是否匹配，但未超出JD薪资范围上限，因此暂定为“匹配”。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历符合要求，核心职责匹配度高，具备丰富的AI产品设计与落地经验，技术理解能力强，且无“不匹配”项。尽管年龄超出JD要求（38岁），属于**潜在风险**，但未超过20%阈值（35*1.2=42），因此不影响通过资格。