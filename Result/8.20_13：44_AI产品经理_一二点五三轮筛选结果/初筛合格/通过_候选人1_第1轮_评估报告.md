------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 梁朝策

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 广东科技学院 本科（软件工程）2020-2022；广东岭南职业技术学院 大专（软件技术）2014-2017
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 23-30K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - 候选人主导了多个AI Agent相关产品的设计与落地，如“AI Agent工作流的0-1规划设计”、“ASR+LLM+TTS全流程智能客户/数字人交互方案”，具备从0到1的产品规划能力，符合第一条职责。
    - 在“大语言模型的选型与训练”、“DeepSeek大模型的训练与调优”、“RAG知识库的应用和解决方案支持”等项目中，候选人展示了将算法能力转化为产品功能的能力，体现了技术与业务之间的桥梁作用，符合第二条职责。
    - 候选人设计了多个AI产品的评估与优化机制，如“设备管理云平台”通过数千台终端设备的运营数据进行优化，“AI Agent产品流程”通过协调资源与策略确保交付质量，符合第三条职责。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有多个AI Agent、大模型调优、RAG应用等实际项目经验，具备扎实的AI产品设计与落地能力。
    - 具备PMP、敏捷、阿里云ACE认证，项目管理与技术理解能力强。
    - 曾主导多个从0到1的产品设计，具备较强的市场洞察与产品战略能力。

- **风险与不足:**
    - 虽然学历为本科，但非计算机、人工智能等优先专业背景，可能在技术深度上存在短板（但其实际项目经验可部分弥补）。
    - 缺乏明确的“构建AI产品评估体系”的量化指标描述，建议后续深入探讨其评估方法论。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 所有硬性门槛均匹配，且在核心职责方面展现出高度匹配的项目经验与能力。候选人具备主导AI产品全周期开发、技术与业务融合、数据驱动优化等关键能力，符合AI产品经理岗位的核心要求。