------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 李贵滨

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 硕士研究生
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 39岁（1985年出生）
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 未提及
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    2. 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    3. 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    1. **全周期产品规划与落地：**
       - 李贵滨在多个公司担任算法/数据算法总监，主导多个AI产品的设计与落地，如“基于大模型的智能互动问答系统”、“智慧步道人脸识别组合算法”、“智能排线算法”等。
       - 特别是在**2023年至今**的斯波阿斯公司中，作为AI负责人，主导了智慧场馆产品体系的建设，包括从0到1组建团队、产品体系搭建、算法实现与部署，体现了完整的AI产品生命周期管理能力。
    2. **技术与业务结合：**
       - 他在多个项目中将算法能力直接转化为业务价值。例如在**2023年智能互动问答项目**中，使用LLaMA3大模型结合儿童问答数据集，构建实际应用场景，体现了将算法能力转化为用户价值的能力。
       - 在**投篮机项目**中，通过用户画像和推荐算法提升充值率140%，下单量提升40%，展示了将技术成果转化为业务指标的能力。
    3. **数据驱动优化：**
       - 李贵滨在多个项目中使用数据分析优化产品表现。例如在投篮机项目中，通过数据分析优化充值策略、场地选址、奖品吸引力等维度，显著提升业务指标。
       - 在**2021年脉博科技项目**中，通过算法改进使点击率提升35%，购买率提升23%，体现了数据驱动的产品优化能力。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    1. 具备从0到1搭建AI产品体系的丰富经验，涵盖产品规划、技术实现、团队组建等全流程。
    2. 拥有扎实的AI技术背景，熟悉LLM、Prompt工程、Agent应用场景等JD中强调的核心技能。
    3. 多次成功将算法能力转化为用户价值和业务增长，具备技术与业务的双向沟通能力。

- **风险与不足:**
    - 无硬性门槛风险。
    - JD中提到的Agent架构理解与技术挑战识别能力（如幻觉、延迟、可靠性），简历中虽有涉及LLM应用，但未明确提及对Agent架构的具体理解或挑战应对经验。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硕士学历符合岗位背景。
    2. 无年龄、薪资等硬性门槛冲突。
    3. 核心职责匹配度评估为“高”，具备主导AI产品全周期、技术业务桥梁搭建、数据驱动优化等能力。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人具备硕士学历，年龄与薪资均未超出JD要求范围（JD未明确），且在AI产品设计、技术落地、业务转化、数据驱动等方面展现出高度匹配的能力。尽管在Agent架构理解方面证据略显不足，但其整体产品能力与技术背景足以支持进入下一轮面试进行深入评估。