------



**简历初筛评估报告**

**应聘岗位:** 大模型算法工程师  
**候选人:** 谢宝正

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 郑州科技学院 计算机科学与技术（本科）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 未提供年龄信息
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·月结
    - **候选人期望:** 未提供期望薪资信息
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 负责大规模语言模型的训练、微调与优化，提升模型性能和泛化能力，支撑业务场景落地
    - 研究并应用模型压缩、量化、蒸馏等关键技术，降低模型部署成本，提升推理效率
    - 实现模型推理加速与部署，提升服务性能，确保模型在生产环境的稳定运行

- **简历证据分析:**
    - **第一条职责匹配度高：**  
      候选人曾在项目中使用 **Qwen、ChatGLM4** 等大模型，并基于 **LoRA** 进行垂域微调，构建问答系统和客服 Agent，具备大模型训练与调优的直接经验。
    - **第二条职责匹配度中等：**  
      简历中提及使用 **RAG、LoRA、TTS、ASR** 等技术，但未明确提及 **模型压缩、量化、蒸馏** 等技术的应用经验。虽有部署优化经验，但缺少量化压缩类技术的直接证据。
    - **第三条职责匹配度高：**  
      候选人多次提及模型部署优化，如使用 **FastAPI、Docker、Vue** 搭建系统架构，优化 **Wav2Lip 推理流程** 提升高并发性能，具备模型服务化部署与推理加速的经验。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**  
  1. 具备扎实的大模型工程化经验，掌握 **Qwen、ChatGLM4** 等主流模型的部署与微调（LoRA）技术。
  2. 有丰富的模型部署与推理优化经验，熟悉 **FastAPI、Docker、TTS、ASR** 等工程化工具链。

- **风险与不足:**  
  1. 简历中未提供年龄与期望薪资信息，存在信息缺失风险。  
  2. 对于模型压缩与量化技术（如量化、蒸馏等）缺乏直接证据，建议在后续面试中进一步考察。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历满足要求，未提供年龄与薪资信息视为匹配。核心职责匹配度为“高”，具备大模型训练、微调、部署、推理优化等关键能力，仅在模型压缩与量化方面缺乏直接证据，建议后续面试中补充考察。