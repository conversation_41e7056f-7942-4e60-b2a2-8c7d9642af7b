------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：梁朝策  
**年龄**：29岁  

**工作经验**：8年  

**学历**：本科（软件工程）  

**当前职位**：产品经理  

**期望薪资**：23-30K  

---

### **1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科（软件工程）
    - **匹配结果:** 匹配

- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配

- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（即月薪上限为20K）
    - **候选人期望:** 23-30K
    - **计算判断：**
        - JD月薪上限：20K
        - 候选人期望薪资中位数为26.5K，明显高于20K
        - 20K * 1.2 = 24K
        - 候选人期望薪资下限为23K，处于20K ~ 24K之间
    - **匹配结果:** 潜在风险

---

### **2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化
    3. 协调跨职能团队推进AI产品的开发与落地
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    1. **设计AI产品功能与用户体验：**
        - 简历中明确描述“主导AI Agent工作流的0-1规划设计”、“构思ASR+LLM+TTS全流程智能客户/数字人交互方案”，体现出对AI产品功能与用户体验的深度参与。
        - 项目“扶梯AI不安全行为智能监测云平台”展示了其在AI产品设计中对技术可行性、用户体验、硬件选型等环节的把握。
    2. **制定产品路线图与优先级：**
        - 描述“负责制定产品策略，确保项目按时交付”、“主导设备管理云平台的设计与开发，制定核心业务流程”，体现出对产品路线图的规划能力。
        - 在“设施设备管理云平台项目”中提到“负责市场调研、概念生成、运营流程和核心功能设计”，说明具备从0到1的产品路线规划经验。
    3. **协调跨职能团队：**
        - 多次提及“协调团队资源”、“领导产品与UI团队”、“与算法工程师紧密协作”，说明具备良好的跨团队协作经验。
        - 在“温氏AI预测性维护项目”中协调算法工程师进行模型训练与调优，体现了实际操作能力。
    4. **制定市场定位与竞争策略：**
        - 在多个项目中强调“推动产品市场推广”、“服务10个领先客户”、“完成50个终端选型认证”，表明其参与市场策略制定与推广。
        - “设施设备管理系统：支持拓展3个渠道”、“设备管理云平台已有数十个租户”，说明其具备市场拓展与渠道管理经验。

- **匹配度结论:** 高

---

### **3. 综合评估**

- **优势:**
    1. 拥有多个从0到1的AI产品落地项目经验，涵盖AI Agent、大模型调优、机器视觉、智能交互等方向。
    2. 具备完整的AI产品生命周期管理经验，包括需求分析、技术可行性判断、产品规划、市场推广等。
    3. 持有PMP、敏捷认证、阿里云ACE认证等专业资质，技术与产品双重背景。

- **风险与不足:**
    1. **薪资期望超出JD上限**（23K vs JD上限20K），但仍在1.2倍阈值范围内，标记为“潜在风险”。

---

### **4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → 无
    2. 核心职责匹配度 评估结果为 "**低**"。 → 不适用（评估结果为“高”）

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → 满足
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → 满足

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄均满足JD要求，虽期望薪资略高于JD上限，但未超过1.2倍阈值，标记为“潜在风险”。其在AI产品设计、路线图规划、跨团队协作、市场推广等方面均有充分的项目经验和成果支撑，核心职责匹配度高，具备进入下一轮面试的资格。