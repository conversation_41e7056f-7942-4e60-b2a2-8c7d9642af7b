------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：夏思琪  
**年龄**：31岁  
**工作经验**：1年AI产品经理经验 + 10年互联网产品设计经验  
**学历**：计算机学士学位（江西师范大学）  
**当前职位**：AI产品经理  
**期望薪资**：未明确提及  

---

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 计算机学士学位（本科）
    - **匹配结果:** 匹配

- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 31岁
    - **匹配结果:** 匹配

- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（最高值为 260K/年）
    - **候选人期望:** 未在简历中提供期望薪资
    - **匹配结果:** 匹配

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    3. 协调跨职能团队推进AI产品的开发与落地  
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1**：候选人主导过电商平台竞品分析系统（基于扣子平台）、教培行业智能客服系统（对话流设计、意图识别、FAQ机制），并在花卉识别项目中参与需求对接、数据标注质检、模型训练至交付，具备AI产品功能设计与用户体验优化的实际经验。
    - **职责2**：在电商平台竞品分析系统与智能客服系统中，均有持续迭代优化的描述（结合用户反馈与数据分析），并提到“看护与优化各AI项目的线上表现”，体现出产品路线图执行与迭代能力。
    - **职责3**：曾在华为云CodeArts IDE项目中与开发团队共建版本迭代质量防线，在多个项目中推动产品上线及优化，说明其具备跨团队协作与产品落地推进经验。
    - **职责4**：虽然简历中未直接提及市场定位或竞争策略制定内容，但候选人具备从0到1主导AI产品落地的经验（如电商平台竞品分析系统、智能客服系统），具备产品差异化设计与商业化落地能力。

- **匹配度结论:** 高

---

**3. 综合评估**

- **优势:**
    1. 拥有1年AI产品经理经验，并在华为云等头部企业主导AI项目落地，具备扎实的AI产品设计与优化能力。
    2. 10年互联网产品设计背景，跨团队协作与产品推进经验丰富，具备从0到1、持续迭代的完整产品生命周期管理能力。

- **风险与不足:**
    1. 未提供期望薪资信息，后续需确认是否与岗位预算匹配。
    2. 未明确描述市场定位与竞争策略制定经验，但整体能力可覆盖该职责。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄均符合JD要求，虽未提供期望薪资，但无超出薪资上限的风险。核心职责匹配度评估为“高”，具备AI产品设计、迭代优化、跨团队协作等关键能力，符合岗位核心要求。