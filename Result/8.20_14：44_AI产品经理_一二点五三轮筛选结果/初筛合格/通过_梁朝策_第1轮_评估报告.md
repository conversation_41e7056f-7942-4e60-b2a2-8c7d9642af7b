------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 梁朝策

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 广东科技学院 本科（软件工程，2020-2022）；广东岭南职业技术学院 大专（软件技术，2014-2017）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 23-30K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **职责1（AI产品全周期管理）：**
        - 简历中明确提到“主导AI Agent工作流的0-1规划设计”，并“负责大语言模型的选型与训练”，具备从0到1的产品构建经验。
        - 有“构思ASR+LLM+TTS全流程智能客户/数字人交互方案”的经历，显示其具备AI产品设计与落地能力。
    - **职责2（技术与业务桥梁）：**
        - 具体项目中“负责客户的整体需求与产品设计”，“设计AI Agent产品的流程，协调团队资源”，体现其将技术能力转化为业务需求的能力。
        - 在“设施设备管理云平台”项目中，结合“RAG知识库”与“大模型设备绩效及健康策略设计”，显示其对算法能力的业务转化能力。
    - **职责3（数据驱动优化）：**
        - “负责DeepSeek大模型的训练与调优，确保其符合客户内部工作流程”，体现模型优化经验。
        - “优化数据处理效率”、“提升客户人机交互的智能化水平”等描述，显示其具备通过数据优化产品表现的意识和实践。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有多个AI Agent相关项目经验，涵盖从0到1的产品设计与落地。
    - 熟悉LLM、RAG、Prompt工程等关键技术，并具备大模型调优经验。
    - 具备PMP、敏捷、阿里云ACE认证，技术+项目管理能力兼备。
    - 多个项目成果显著，具备市场转化能力与产品推广经验。

- **风险与不足:**
    - 无硬性门槛风险项。
    - 虽有AI产品经验，但未明确提及“构建AI产品评估体系”的具体方法论描述。

**4. 初筛结论**

- **淘汰条件：**
    1. 无硬性门槛“不匹配”项。
    2. 核心职责匹配度为“高”。
- **通过条件：**
    1. 所有硬性门槛均“匹配”。
    2. 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人具备完整的AI产品经理所需技能与项目经验，尤其在AI Agent产品设计、大模型应用、技术与业务衔接方面表现突出，符合岗位核心职责要求。虽未提及学历与薪资门槛，但候选人期望薪资在合理范围内且无冲突，年龄匹配。综合评估为高匹配度人选。