------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：杜嘉琪    
**年龄**：1994年02月（约30岁）

**工作经验**：5年（2段工作经历，均在知名公司担任软件工程师）  

**学历**：本科（上海财经大学，计算机科学与技术专业）  

**当前职位**：软件工程师（华为）  

**期望薪资**：未在简历中明确提供  

---

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 约30岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）
    - **候选人期望:** 简历中未提供
    - **匹配结果:** 匹配

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化
    3. 协调跨职能团队推进AI产品的开发与落地
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - **职责1：产品功能与用户体验设计**
        - 简历中未见候选人参与产品设计、用户体验定义或技术可行性评估的相关描述。
        - 仅描述为“参与需求分析、设计、开发、测试及上线部署”，属于开发角色，非产品经理主导。
    - **职责2：产品路线图与迭代管理**
        - 简历中未体现候选人制定产品路线图、设定优先级或推动产品迭代的经验。
    - **职责3：跨职能团队协作**
        - 简历提到“参与开发环境的搭建”和“使用Jenkins进行持续集成”，但未说明其在跨职能团队中协调推动产品落地的角色。
    - **职责4：市场定位与竞争策略**
        - 简历中无任何与市场分析、竞争策略、商业化策略相关的描述。

- **匹配度结论:** 低

---

**3. 综合评估**

- **优势:**
    - 本科学历匹配岗位要求。
    - 拥有在华为、松下等大型企业从事软件开发的经验，技术背景扎实。
    - 熟悉Docker、Kubernetes、Jenkins等开发与部署工具，具备一定的技术落地能力。

- **风险与不足:**
    - 简历中无任何与“AI产品经理”相关的职责描述，缺乏产品定义、用户调研、PRD撰写、路线图规划等关键经验。
    - 未体现跨职能协作、市场分析、产品策略制定等抽象能力。
    - 期望薪资未提供，存在潜在沟通成本。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 硬性门槛审查中所有项均匹配，但核心职责匹配度评估结果为“低”，候选人简历中未体现与AI产品经理岗位相关的产品定义、路线图规划、跨职能协作及市场策略制定等核心职责经验，不符合岗位基本胜任要求。