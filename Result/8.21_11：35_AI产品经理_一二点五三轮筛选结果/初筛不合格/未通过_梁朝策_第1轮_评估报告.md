------



**简历初筛评估报告**

**应聘岗位:** AI产品经理    
**候选人**：梁朝策    
**年龄**：29岁 

**工作经验**：8年产品经理经验

 **学历**：本科（软件工程）

**当前职位**：产品经理

**期望薪资**：23-30K·13薪



**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科（软件工程）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）
    - **候选人期望:** 23-30K·13薪（ExpectedSalary = 30K）
    - **匹配结果:** 不匹配（30K > 20K * 1.2 = 24K）

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - **职责1（产品功能与用户体验）：** 简历中提到“主导AI Agent工作流的0-1规划设计”、“构思ASR+LLM+TTS全流程交互方案”、“设计AI Agent产品的流程”等，体现出明确的产品功能设计与用户体验构建能力。
    - **职责2（产品路线图与迭代管理）：** 简历中多次提到“主导从0到1的AI产品设计”、“推动产品持续迭代”、“制定产品策略”，符合路线图制定与迭代管理要求。
    - **职责3（跨团队协作）：** 简历中提到“协调团队资源”、“领导产品与UI团队”、“与算法工程师紧密协作”等内容，具备跨职能团队协作能力。
    - **职责4（市场定位与竞争策略）：** 简历中提及“产品市场推广”、“支持售前”、“打造智能化管理支持系统”等描述，表明候选人参与市场策略制定。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 8年产品经理经验，具备从0到1的AI产品落地经验。
    - 拥有PMP、敏捷认证、阿里云ACE认证等硬核资质。
    - 掌握AI核心技能（大模型调优、RAG、Agent流程设计等），与JD中技术要求高度契合。
    - 曾主导多个AI项目（如AI不安全行为监测、设备智能管理平台等），具备完整产品生命周期管理经验。

- **风险与不足:**
    - **薪资要求超出JD上限20%以上（30K vs 20K）**，属于“不匹配”项。
    - JD要求“Axure/Figma原型设计工具”能力，简历中未提供明确证据。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查中有任意一项结果为 "**不匹配**"（薪资不匹配）。
    2. ❌ 核心职责匹配度评估结果为 "**低**"（未触发）。

- **通过条件：**
    1. ❌ 硬性门槛审查结果中**有** "**不匹配**" 项。
    2. ✅ 核心职责匹配度为 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人具备与AI产品经理岗位高度匹配的专业能力和项目经验，尤其在AI产品设计、市场推广、团队协作方面表现突出。但其期望薪资30K超过JD薪资上限20K的20%（即24K），属于硬性不匹配项，因此被淘汰。