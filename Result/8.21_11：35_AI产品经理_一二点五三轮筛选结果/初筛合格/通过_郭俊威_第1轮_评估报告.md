------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：郭俊威  
**年龄**：28岁  
**工作经验**：约5年产品经理经验，涵盖工程造价、BIM建模、数据分析等方向  
**学历**：本科（非全日制）  
**当前职位**：产品经理  
**期望薪资**：13-14K  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科（非全日制）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 28岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）
    - **候选人期望:** 13-14K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1：设计产品功能与用户体验**  
        - 简历中多次提到“从0到1落地产品”，如“0-1落地AUTO CDM平台”、“主导定义并设计关键技术：模型集成出口、WBS进度计划关联、4D模拟引导、进度模拟、偏差生成等”，体现产品功能设计能力。  
        - 用户体验方面，“设计简洁高效的进度展示与比较流程”、“设计可视化数据组件（如趋势图、对比图）”等描述，显示候选人具备良好的用户体验设计意识。  
    - **职责2：制定产品路线图与迭代管理**  
        - “管理需求流转，推动迭代优化，完成V1 & V3三个核心迭代版本上线”、“制定产品迭代规划、更新、bug处理”等，表明候选人具备产品路线图规划与迭代管理经验。  
    - **职责3：协调跨职能团队推进产品落地**  
        - “链接UG、研发、测试、运营的工作，注重用户体验，组织产品验收评估，实现高质量交付”、“有效链接BIM联盟、研发团队、测试团队，确保平台符合工程实际需求与技术可行性”等描述，显示其具备良好的跨团队协作能力。  
    - **职责4：制定市场定位与竞争策略**  
        - “完成竞品化优品分析报告，提炼核心差异与优化方向，指导产品差异化定位（如细化应用场景）”，表明候选人具备一定的市场分析与竞争策略制定能力。  

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 多个从0到1的产品落地经验，涵盖B/S平台、数据分析、自动化建模等多个方向。
    - 具备良好的产品路线图规划与迭代管理能力。
    - 能够协调跨职能团队推进产品开发与交付。
    - 具备竞品分析、用户需求调研、PRD撰写等能力。

- **风险与不足:**
    - 简历中未明确提及AI/机器学习相关背景，如技术原理理解、AI模型训练与部署等经验。
    - 虽然具备产品设计能力，但未展示使用Axure/Figma等原型工具的具体证据。
    - 期望薪资略低于JD要求的薪资上限（但仍在合理范围内）。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 无“不匹配”项  
    2. ✅ 核心职责匹配度为“高”  

- **通过条件：**
    1. ✅ 所有硬性门槛审查结果为“匹配”  
    2. ✅ 核心职责匹配度为“高”  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、期望薪资均符合JD硬性要求。简历中展示了多个从0到1的产品经理经验，具备良好的产品设计、迭代管理、跨团队协作和市场分析能力，与JD中AI产品经理的核心职责高度匹配。尽管在AI技术背景方面略显薄弱，但可通过后续技术面试评估补充。