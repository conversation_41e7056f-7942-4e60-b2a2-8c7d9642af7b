------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：夏思琪  
**年龄**：31岁（2010年毕业，推断年龄为31岁）  
**工作经验**：1年AI产品经理经验，10年互联网产品设计经验  
**学历**：江西师范大学计算机软件工程本科（工学学士学位）  
**当前职位**：AI产品经理  
**期望薪资**：未明确提及，暂定为匹配  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配

- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 31岁
    - **匹配结果:** 匹配

- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary=20K）
    - **候选人期望:** 简历未提供具体期望薪资
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1（设计和定义AI产品功能与用户体验）：**  
      候选人主导了“电商平台竞品分析系统”和“教培行业智能客服系统”的设计与落地，涉及知识库构建、流程设计、对话逻辑梳理、用户反馈机制建设等，体现了较强的AI产品功能设计与用户体验优化能力。
    - **职责2（制定产品路线图与优先级）：**  
      候选人提到“看护与优化持续跟进各AI项目的线上表现，结合用户反馈与数据分析进行迭代优化”，说明具备产品持续优化和路线图管理的意识和能力。
    - **职责3（协调跨职能团队）：**  
      在“花卉识别项目”中负责从需求对接、数据标注质检、环境配置到模型训练与交付的全流程协调，涉及多个技术团队的协作，具备跨职能团队推进能力。
    - **职责4（制定市场定位与竞争策略）：**  
      简历中未明确提及市场定位、竞争策略或商业化路径的制定经验，该职责缺乏直接证据。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
    - 拥有1年AI产品经理经验，并主导多个AI项目的落地（如电商平台竞品分析、智能客服、花卉识别等），具备一定的AI产品全流程管理能力。
    - 拥有10年传统互联网产品设计经验，具备扎实的产品设计与用户体验基础。
    - 熟悉AI产品开发流程，具备与技术团队协同开发的经验。

- **风险与不足:**
    - 简历中未提供关于市场定位与竞争策略制定的具体经验，与JD中第四项核心职责存在差距。
    - 期望薪资未明确，存在潜在沟通成本风险。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合硬性门槛要求，且在AI产品设计、用户体验、跨职能协作等方面具备明确经验，核心职责匹配度为“中”。尽管在市场定位与竞争策略方面缺乏直接证据，但整体仍具备进入下一轮面试的资格。