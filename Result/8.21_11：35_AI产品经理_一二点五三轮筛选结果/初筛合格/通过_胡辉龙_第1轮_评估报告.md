------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：胡辉龙  
**年龄**：根据工作经历推断，至少有4年工作经验，年龄可能接近或超过35岁  

**工作经验**：约3年7个月（需求分析工程师1年1个月 + 产品经理1年6个月 + 实习/其他经历）  

**学历**：高职专升本（本科）  

**当前职位**：产品经理  

**期望薪资**：未明确提供，但根据岗位JD薪资范围及候选人背景，可能存在风险  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 高职专升本（相当于本科学历）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 根据2017-2021年本科学习经历及2024年工作时间推断，年龄可能在35岁左右或略超
    - **匹配结果:** 潜在风险（若实际年龄超过35岁，且未提供明确年龄信息）
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary=20K）
    - **候选人期望:** 未在简历中明确提供期望薪资
    - **匹配结果:** 匹配（因未提供）

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化
    3. 协调跨职能团队推进AI产品的开发与落地
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    1. **职责1：产品功能与用户体验设计**
       - 简历中多次提到“主导产品设计”、“优化迭代建设”、“提升用户体验”、“基于用户反馈持续改进系统数据”等描述，尤其在“生产运行管理系统”项目中提到“PMO及原型设计（Axure）”、“搭建LSTM神经网络模型”等，具备AI产品设计经验。
    2. **职责2：产品路线图与迭代管理**
       - 在“化工厂设备管理系统”项目中提到“推动传统‘定期检修模式’向AI驱动预测性维护转型”，体现出对产品迭代方向的把握；“项目从白盒、灰盒、复盖多区域、多级单位试点推广”也说明具备路线图规划经验。
    3. **职责3：跨职能团队协作**
       - 在多个项目中提到“与各个产品相关方沟通合作，协调研发、设计、运营同学，把控产品开发流程，推动产品落地实施”，符合跨职能协作要求。
    4. **职责4：市场定位与竞争策略**
       - 在“免费软件产品市场定位设计工作”中明确提及“市场定位设计”，但未详细描述竞争策略制定过程，相关证据略显不足。

- **匹配度结论:** 高（在核心职责1-3方面证据充分，职责4略有欠缺但不影响整体高匹配）

**3. 综合评估**

- **优势:**
    1. 具备丰富的AI产品开发与落地经验，涵盖多个行业（工业、电力、化工等）。
    2. 掌握Axure原型设计工具，具备PRD撰写、用户需求调研、产品迭代管理等关键技能。
    3. 成功主导多个AI项目，包括LSTM模型、ST-CNN网络、NLP应用等，技术落地能力强。

- **风险与不足:**
    1. 年龄存在潜在风险（接近或略超35岁上限）。
    2. 期望薪资未提供，可能存在与JD薪资范围不匹配的风险。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌
    2. 核心职责匹配度 评估结果为 "**低**"。 ❌

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 ✅
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 ✅

**筛选结果：** ✅ 通过  
**筛选理由：** 简历中学历满足要求，核心职责匹配度高，具备丰富的AI产品设计与落地经验，技术能力扎实。尽管年龄存在潜在风险（接近或略超35岁），但未超出阈值（1.2倍），且期望薪资未提供，因此不构成淘汰项。建议进入下一轮面试，重点关注年龄与薪资沟通确认。