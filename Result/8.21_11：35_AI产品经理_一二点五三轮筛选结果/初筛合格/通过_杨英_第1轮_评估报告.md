------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：杨英  
**年龄**：33岁  
**工作经验**：13年  
**学历**：本科（计算机科学与技术）  
**当前职位**：软件产品经理  
**期望薪资**：15-18K  

---

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 33岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）
    - **候选人期望:** 15-18K
    - **匹配结果:** 匹配（18K ≤ 20K）

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - **设计AI产品功能与用户体验：**
        - 简历中明确提到“主导AI语音、OCR、NLP等技术落地”，“设计端云协同架构”，“语音识别字准率93%”，“响应延迟<800ms”，“用户平均操作时长从72s降至8s”。
        - 具体项目如“AI智能化酒店服务机器人系统”、“酒店客户自助登记系统”均体现了AI功能设计与用户体验优化。
    - **制定产品路线图与优先级：**
        - “从0到1搭建NFT交易平台”，“重构计费规则引擎”，“推动AI产品从理论到落地”，“分省升级标准化手册”等描述，显示其具备产品路线规划与迭代管理能力。
    - **协调跨职能团队：**
        - 多次提到“带领团队”、“协调运营、研发、客服建立全国计费联调机制”，“跨部门协作”，“培养团队成员”等，表明其具备良好的跨职能协作能力。
    - **制定市场定位与竞争策略：**
        - “分析市面主流数字藏品交易平台，找到产品差异化优势”，“定向设计IPTV端「游戏大包+会员折扣」组合定价”，“创新模式：嵌入式硬件+云端AI双引擎架构”，体现其市场洞察与策略制定能力。

- **匹配度结论:** 高

---

**3. 综合评估**

- **优势:**
    - 具备13年产品经验，其中AI领域经验超10年，主导多个AI产品从0到1落地，商业化成功率100%。
    - 熟悉AI技术原理与产品化路径，具备PRD撰写、产品路线图规划、跨部门协作等关键技能。
    - 多个项目中体现出技术可行性判断、用户需求调研、产品迭代管理等能力。

- **风险与不足:**
    - 无硬性门槛风险项。
    - 简历中未明确提及Axure/Figma等原型工具使用经验，但其在项目中多次提到“输出20+核心模块PRD”、“构建可视化知识管理平台”，可间接推断具备相关能力。

---

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 无硬性门槛“不匹配”项。
    2. ✅ 核心职责匹配度为“高”。

- **通过条件：**
    1. ✅ 所有硬性门槛均匹配。
    2. ✅ 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合岗位要求，且在AI产品设计、技术可行性判断、产品路线图规划、跨职能协作、市场策略制定等方面均有丰富经验与具体成果，核心职责匹配度为“高”。虽未明确提及Axure/Figma使用经验，但其PRD撰写与产品设计能力已在多个项目中得到验证。