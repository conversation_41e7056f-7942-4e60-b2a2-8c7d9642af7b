------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾宪涛

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科（已毕业），MBA在读
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 42岁
    - **匹配结果:** 不匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（即最高20K）
    - **候选人期望:** 15-20K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - 曾负责Zigbee3.0全屋智能项目，编写PRD文档、进行需求管理和产品迭代，体现产品路线图规划与迭代管理能力。
    - 有多个跨团队协作经验（如中国移动空调智能运维项目、Zigbee3.0项目），体现跨职能团队协调能力。
    - 在多个项目中涉及智能系统设计（如智能柜、智能运维平台）、用户体验优化（如提升APP用户体验），体现产品功能与用户体验设计能力。
    - 缺乏明确的AI/机器学习相关产品经验描述，未见其主导AI产品市场定位、竞争策略制定的具体案例。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**  
    - 有多年产品管理经验，具备PRD撰写、产品路线图规划、跨团队协作等核心能力。  
    - 拥有多个智能产品开发经验，与AI产品经理岗位有一定契合度。  
    - 拥有多个产品从0到1落地的经验，具备产品生命周期管理能力。

- **风险与不足:**  
    - 年龄超过JD要求上限（35岁）且超出20%（35*1.2=42），属于“不匹配”项。  
    - 简历中未体现AI/机器学习相关产品经验，缺乏技术可行性判断和AI模型落地理解的直接证据。  
    - 未见其主导AI产品市场定位与竞争策略制定的具体案例。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. ❌ 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. ✅ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人年龄为42岁，远超JD要求的“35岁以下”，且超出20%阈值，属于“不匹配”项，直接淘汰。尽管其具备一定智能产品经验，但硬性门槛不达标，无法进入下一轮面试。