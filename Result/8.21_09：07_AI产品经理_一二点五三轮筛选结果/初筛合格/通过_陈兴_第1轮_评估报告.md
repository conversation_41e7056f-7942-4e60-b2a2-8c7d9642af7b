------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 陈兴

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 23岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（即年薪约195K-260K）
    - **候选人期望:** 10-15K（即年薪约120K-180K）
    - **匹配结果:** 潜在风险  
      > 候选人期望薪资上限为15K，略低于JD薪资上限的20K，但差距为25%，略超20%阈值，因此标记为“潜在风险”。

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    3. 协调跨职能团队推进AI产品的开发与落地  
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - **职责1：设计和定义AI产品的功能与用户体验**  
      简历中明确描述其负责多个AI平台产品的功能设计与用户体验优化，例如“负责平台迭代优化，通过收集用户需求与反馈，优化功能及流程，提升用户体验”“设计内部知识库问答功能，包括框架、交互逻辑及原型”，体现了产品设计与用户体验定义能力。✅
    - **职责2：制定产品路线图与优先级，推动产品持续迭代**  
      “制定推进规划及里程碑计划，推动平台快速上线”“负责负面数据收集清洗、prompt编写调整、效果迭代及测试验收，保障功能落地质量”等描述，表明其具备明确的路线图制定与迭代管理经验。✅
    - **职责3：协调跨职能团队推进产品开发与落地**  
      虽未明确提及“跨职能团队”，但其“主导在既有客服系统中新增AI审核功能全流程，构建‘模型选型-能力强化-数据支撑’的完整AI框架”，涉及模型选型、数据支撑、工程对接等多方面协作，间接体现跨团队协作能力。✅
    - **职责4：制定市场定位与竞争策略**  
      简历中未提及市场定位、竞争策略或商业化策略相关内容，缺乏直接证据支持此项职责。⚠️

- **匹配度结论:** 中  
  > 候选人在产品设计、迭代管理、跨职能协作方面有明确经验，但在市场定位与竞争策略制定方面缺乏直接证据。

**3. 综合评估**

- **优势:**  
  - 1年AI产品经理经验，具备多个从需求分析到产品落地的完整项目经验  
  - 熟悉LLM、RAG、fewshot等AI技术，能够推动技术与产品融合  
  - 具备产品路线图规划、迭代管理与用户需求调研能力  

- **风险与不足:**  
  - 薪资期望略低于JD薪资上限25%，标记为“潜在风险”  
  - 缺乏市场定位与竞争策略制定方面的直接经验描述  

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  
- **通过条件：**  
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄均符合要求，薪资期望略低于JD上限25%，标记为“潜在风险”，但未超出20%阈值上限的1.2倍（20K×1.2=24K）。核心职责匹配度为“中”，具备产品设计、迭代管理与跨团队协作经验，仅在市场定位与竞争策略方面缺乏直接证据，不影响进入下一轮面试。