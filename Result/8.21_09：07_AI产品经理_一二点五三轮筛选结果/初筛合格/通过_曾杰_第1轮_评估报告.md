------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾杰

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 硕士（计算机技术专业）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 38岁
    - **匹配结果:** 潜在风险  
      *（38岁 > 35岁，但未超过 35*1.2=42 岁）*
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary=20K）
    - **候选人期望:** 简历未提供明确期望薪资
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1：** 曾主导多个AI产品设计项目，如“文档分析系统”、“配电网无人机智能巡检系统”，在这些项目中均涉及AI技术应用（如YOLO模型、Prompt工程、RAG/Agent技术），并强调用户体验优化（如将传统检索转化为知识问答交互模式），具备AI产品功能与体验设计能力。
    - **职责2：** 在多个项目中制定了产品路线图并推动迭代，如“配电网无人机巡检运营平台”分三期推进，且在每一阶段明确产品演进方向，并提出“下一步迭代”、“长期规划”等策略，具备产品路线图规划与迭代管理经验。
    - **职责3：** 多次提到跨部门协作，如“组织编写产品-研发手册手册，减少沟通成本”，“对接主要合作伙伴，完成API搭建及产品整合集成”，“组建团队完成本体研发”，具备协调跨职能团队的能力。
    - **职责4：** 在“文档分析系统”项目中明确制定商业化策略（SaaS+租赁模式）；在“无人机巡检系统”中形成行业解决方案并推广至多地电网，具备市场定位与竞争策略制定能力。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 多年AI产品设计与落地经验，涵盖从需求分析到商业化推广的完整生命周期。
    - 具备明确的产品路线图规划能力，并能推动持续迭代。
    - 熟悉AI模型应用（如YOLO、Prompt工程、RAG/Agent），理解技术可行性判断。
    - 拥有PMP、NPDP等认证，具备项目管理与产品管理双重能力。

- **风险与不足:**
    - 年龄超过JD要求上限（38岁 > 35岁），但未超出阈值20%（42岁），属于潜在风险。
    - 简历未提供明确期望薪资，无法判断是否匹配JD薪资范围。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 无硬性门槛不匹配项。
    2. ✅ 核心职责匹配度为“高”。

- **通过条件：**
    1. ✅ 硬性门槛无“不匹配”项。
    2. ✅ 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历符合要求，核心职责匹配度高，具备AI产品设计、路线图规划、跨团队协作及市场策略制定的完整能力。年龄略超要求，但未超过20%阈值，属于潜在风险项。薪资未提供，暂未构成淘汰因素。