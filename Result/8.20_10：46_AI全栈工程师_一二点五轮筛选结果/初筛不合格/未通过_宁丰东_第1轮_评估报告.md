------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 宁丰东  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 本科及以上  
    - **候选人情况:** 本科（吉林大学珠海学院，计算机科学与技术）  
    - **匹配结果:** 匹配  
- **年龄要求:**
    - **JD 要求:** 未明确年龄上限，假设为常规岗位年龄上限（通常为35岁）  
    - **候选人情况:** 42岁  
    - **匹配结果:** 不匹配（年龄超过常规上限，且未在JD中找到明确放宽年龄限制的说明）  
- **薪资范围:**
    - **JD 要求:** 未提供具体薪资范围  
    - **候选人期望:** 30-40K  
    - **匹配结果:** 匹配（因JD未提供薪资上限，无法判断是否超限）  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。
    2. 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。
    3. 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。

- **简历证据分析:**
    1. **Agent框架设计与实现：**  
       - 候选人在“AI短视频创作平台”项目中使用 **LangGraph 构建多 Agent 系统**，并结合 **Milvus 向量数据库** 实现创意头脑风暴与视频脚本生成，具备从零搭建Agent框架的经验。
       - 在“AI招聘项目”中也使用了 **LangGraph** 和 **Qwen 模型 API** 实现人岗匹配和AI面试流程。

    2. **RAG、function calling 等能力实现：**  
       - 在“AI招聘项目”中，候选人基于 **RAG 构建简历检索系统**，并使用 **Qwen-Max 模型进行工作流规划与评估**，体现了 function calling 的实现能力。
       - 在多个项目中均有使用 **DashScope、Qwen 等大模型 API**，并结合 **WebSocket、TTS、语音识别** 进行多模态集成。

    3. **多模型统一接入与管理层设计：**  
       - 在多个项目中接入了 **Qwen、GPT3.5、Stable Diffusion、Whisper、CosyVoice** 等多个模型，具备统一接入与调用经验。
       - 使用 **LangChain、LangGraph** 框架进行模型抽象与集成，具备构建统一能力生态的能力。

- **匹配度结论:** 高  

**3. 综合评估**

- **优势:**
    - 具备丰富的 **Agent 架构设计与实现经验**，并在多个项目中使用 LangGraph 和 LangChain 框架。
    - 深度掌握 **RAG、Function Calling、多模型接入与集成** 等核心技能。
    - 拥有从0到1构建AI系统的实战经验，且涉及多个AI落地场景（如短视频、招聘、翻译、NFT生成等）。
    - 技术栈全面，涵盖 Python、FastAPI、Flask、异步编程、Docker、CI/CD、微服务等。

- **风险与不足:**
    - **年龄超出常规上限（42岁），且JD未明确放宽年龄限制**，构成硬性不匹配项。
    - 期望薪资为30-40K，虽未超出JD薪资范围，但缺乏JD具体薪资上限信息，存在潜在沟通成本。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。✅
    2. 核心职责匹配度 评估结果为 "**低**"。❌

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。❌
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。✅

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽然在技术能力方面与岗位要求高度匹配，具备从0到1构建AI Agent系统的能力，并掌握RAG、function calling、多模型接入等关键技术，但其年龄为42岁，明显超出常规岗位年龄上限（35岁），且JD未明确放宽年龄限制，构成硬性门槛“不匹配”，因此被淘汰。