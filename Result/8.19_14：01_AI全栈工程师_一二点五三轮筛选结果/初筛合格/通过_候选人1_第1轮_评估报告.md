------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 宁丰东  

---

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 未提供（默认无硬性学历限制）  
    - **候选人情况:** 本科（吉林大学珠海学院，计算机科学与技术）  
    - **匹配结果:** 匹配  

- **年龄要求:**  
    - **JD 要求:** 未提供（默认无硬性年龄限制）  
    - **候选人情况:** 42岁  
    - **匹配结果:** 匹配  

- **薪资范围:**  
    - **JD 要求:** 未提供（默认无硬性薪资上限）  
    - **候选人期望:** 30-40K  
    - **匹配结果:** 匹配  

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
    1. 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。  
    2. 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。  
    3. 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。  

- **简历证据分析:**  
    - **职责1：AI Agent架构设计与实现**  
        - 简历中明确提到：“主导公司AI Agent平台的架构设计与开发”  
        - 在“AI短视频创作平台”项目中使用“基于langgraph构建多Agent”，并描述了“搜索Agent、调研Agent、分析Agent、评估Agent的多角色团队”设计。  
        - 使用“浏览器插件mcp”、“多Agent创意头脑风暴”等，展示了任务规划与记忆管理的实际经验。  

    - **职责2：Agent核心功能实现**  
        - “AI招聘项目”中使用了“基于langgraph，milvus构建简历RAG”，并接入“qwen-max模型进行工作流规划和简历评估”，体现RAG与function calling能力。  
        - “AI视频翻译工具”项目中涉及“语音识别、翻译、音视频剪辑”，展示了文件处理能力。  
        - “NFT图片和3D模型生成工作流”项目中使用“langchain框架”、“gpt3.5模型”、“Stable Diffusion模型”，展示多模型集成与联网搜索能力。  

    - **职责3：多模型API统一接入与管理**  
        - 多个项目中使用“qwen-max”、“qwen2”、“gpt3.5”、“dashscope语音模型”等，展示了对多种大模型API的深度集成经验。  
        - 在“AI招聘项目”中使用“fastapi”构建后端服务，并接入多个模型API，展示统一管理层的设计能力。  

- **匹配度结论:** 高  

---

**3. 综合评估**

- **优势:**  
    1. 拥有多个AI Agent项目经验，涵盖短视频创意、招聘、视频翻译、NFT生成等多领域，具备从0到1构建Agent系统的完整能力。  
    2. 熟练使用LangGraph、LangChain、FastAPI、RAG、Function Calling等关键技术栈，技术栈与JD要求高度契合。  
    3. 多个项目涉及多模型API接入与集成，具备统一管理层设计经验，符合JD第3项核心职责。  

- **风险与不足:**  
    - 无硬性门槛不匹配项。  
    - 年龄42岁，略高于行业平均，但未超出JD要求范围，不构成淘汰因素。  

---

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查中无任意一项为“不匹配”。  
    2. 核心职责匹配度评估结果为“高”。  

- **通过条件：**  
    1. 硬性门槛审查中无“不匹配”项。  
    2. 核心职责匹配度为“高”或“中”。  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人具备完整的AI Agent系统设计与开发经验，熟练掌握LangGraph、LangChain、FastAPI、RAG、Function Calling等核心技术，且在多个项目中实现了多模型API的统一接入与管理。硬性门槛全部匹配，无淘汰项。具备高度匹配岗位需求的能力。