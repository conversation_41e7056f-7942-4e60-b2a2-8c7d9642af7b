------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 梁朝策

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 本科（软件工程，广东科技学院），大专（软件技术，广东岭南职业技术学院）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 23-30K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - 候选人主导过多个AI Agent产品的0-1设计与落地项目，如“AI Agent工作流的0-1规划设计”、“构思ASR+LLM+TTS全流程智能客户/数字人交互方案”，体现了对AI产品全生命周期的掌控能力。
    - 候选人具备将算法能力转化为产品功能的经验，如“负责大语言模型的选型与训练，优化模型性能”、“负责DeepSeek大模型的训练与调优”、“与算法工程师合作进行模型训练参数调优”，展示了其在技术与业务之间的转化能力。
    - 候选人设计了多个AI产品的评估与优化机制，如“RAG知识库的应用和解决方案支持”、“设备绩效及健康策略设计”，体现了数据驱动优化模型表现和用户体验的能力。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 多年AI产品经理经验，主导多个AI Agent产品的从0到1设计与落地。
    - 具备大模型训练与调优、Prompt工程、RAG等核心技术能力，与JD中的核心技能高度匹配。
    - 拥有PMP、敏捷、ACE阿里云高级认证等资质，具备良好的项目管理与云架构设计能力。
    - 具备数据驱动的产品优化经验，符合AI产品持续迭代的评估体系要求。

- **风险与不足:**
    - 无硬性门槛“潜在风险”项。
    - 简历中未明确提及对Agent架构的技术挑战（如幻觉、延迟、可靠性）的具体识别与应对经验，建议在后续面试中进一步确认。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资期望均符合岗位要求，且在AI产品设计、大模型调优、Agent架构理解等方面具备丰富经验，完全匹配JD中的核心职责与硬技能要求。尽管未明确提及对Agent技术挑战的应对经验，但整体匹配度高，具备进入下一轮面试的资格。