------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 吴兴强  

---

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 未提供（默认为本科及以上）  
    - **候选人情况:** 本科（电子信息工程）  
    - **匹配结果:** 匹配  

- **年龄要求:**  
    - **JD 要求:** 未提供（默认无限制）  
    - **候选人情况:** 28岁  
    - **匹配结果:** 匹配  

- **薪资范围:**  
    - **JD 要求:** 未提供  
    - **候选人期望:** 未提供  
    - **匹配结果:** 匹配  

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
    1. 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。  
    2. 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。  
    3. 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。  

- **简历证据分析:**  
    - **职责1（Agent架构设计）：**  
        简历中未明确提及设计AI Agent框架的经验，也未描述任务规划、记忆管理等模块的实现经验。  
        **缺乏直接证据。**

    - **职责2（RAG、function calling等实现）：**  
        候选人具备**RAG系统开发经验**，主导设计RAG全链路架构，涵盖语义解析、知识召回、排序等模块，使用混合检索架构（搜索引擎+数据库+向量库）和三阶排序策略。  
        **有明确RAG实现经验，但未提及function calling、代码执行、联网搜索等能力。**

    - **职责3（多模型API接入与管理层）：**  
        简历中提到使用**GPT-4、bge-reranker-large等模型**进行排序和评估，具备多模型协同经验，但**未提及统一API接入、模型切换、性能监控等核心职责内容**。  

- **匹配度结论:** 中  

---

**3. 综合评估**

- **优势:**  
    - 拥有完整的RAG系统开发经验，具备链路设计、检索优化、排序策略等关键能力。  
    - 具备大模型训练与部署经验（如LLaMA Factory、SFT、LoRA、量化等），熟悉推理优化与工程化部署。  

- **风险与不足:**  
    - **缺乏Agent框架架构设计经验**，未体现任务规划、工具调用、记忆管理等模块的设计与实现。  
    - **未展示function calling、代码执行、联网搜索等Agent核心功能的实现能力**。  
    - **未提及多模型API统一接入与管理层设计经验**，尽管使用了多个模型，但缺乏系统性设计描述。  

---

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  

- **通过条件：**  
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均满足要求，未发现“不匹配”项。虽然未完全覆盖Agent架构设计及function calling等核心职责，但具备较强的RAG开发能力和大模型工程化经验，匹配度为“中”，符合通过标准。