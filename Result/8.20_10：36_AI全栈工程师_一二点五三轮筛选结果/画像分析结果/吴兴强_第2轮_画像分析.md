# 候选人专业画像分析报告

**基本信息:**

- **姓名:** 吴兴强
- **分析日期:** 2025-04-05

**专业身份与能力层级:**

- **主要专业身份 (80-100分):**  
  **大模型算法工程师（分布式训练、RAG系统、模型部署优化） 85分**  
  简历中明确展示了在昇腾平台使用LLaMA Factory框架进行分布式训练、SFT/Lora微调，主导RAG系统设计与优化，以及模型推理部署与性能提升的完整闭环经验，项目描述详实、技术细节丰富，具备大模型全流程研发能力。

- **重要辅助技能 (60-79分):**  
  **机器学习建模与工程化部署 75分**  
  在反欺诈模型、风控评分卡、用户行为预测等项目中使用XGBoost、LightGBM、LR、SVM等传统机器学习算法，具备特征工程、数据清洗、接口开发、系统测试等能力，且有模型蒸馏、剪枝、部署优化经验。

  **自然语言处理与知识图谱构建 70分**  
  涉及BERT、TextCNN、BILSTM-CRF等NLP模型，应用于意图识别、实体抽取、KBQA系统构建，并使用Neo4j搭建中医知识图谱，具备NLP与知识工程交叉能力。

- **边缘接触能力 (40-59分):**  
  **数据可视化与业务分析 55分**  
  提到Excel、WYN可视化看板、数据透视等能力，但缺乏深入技术细节和复杂分析场景描述，更多作为数据处理工具使用。

  **AI生成内容（AIGC）与音视频处理 50分**  
  有AI翻唱视频制作项目，涉及RVC模型训练、音频分离、图像生成等，但属于个人项目，技术深度描述有限，未体现系统化工程经验。

**能力边界:**

- **擅长:**  
  大模型训练与部署优化（分布式训练、SFT/Lora、推理加速）、RAG系统架构设计与调优、传统机器学习建模（分类、风控、推荐）、NLP模型开发与应用（意图识别、实体抽取、KBQA）

- **适合:**  
  模型工程化部署、数据处理与特征工程、基于知识图谱的问答系统开发、AI产品落地与性能优化

- **不适合:**  
  无证据表明具备弱电智能化、安防系统、楼宇自控等硬件集成经验，未见涉及前端开发、移动应用开发、嵌入式系统开发等方向的关键词或项目证据。

**职业发展轨迹:**

- **一致性:**  
  职业路径高度聚焦于算法与大模型方向。从数据挖掘→机器学习建模→NLP与知识图谱→大模型训练与部署，逐步深入，具备清晰的技术演进逻辑，符合算法工程师向大模型工程师转型的路径。

- **专业深度:**  
  在大模型方向具备显著深度：涵盖昇腾平台部署、LLaMA Factory框架使用、SFT/Lora微调、DeepSpeed优化、RAG系统设计、模型量化与推理优化等完整技术栈，具备从训练到落地的全流程掌控能力。

**综合评估与建议:**

- **专业比重总分:** 87分  
- **可信度:** 高（项目描述详实，技术细节明确，有量化成果支撑）  
- **最终专业身份:** **资深大模型算法工程师（偏向分布式训练与RAG系统优化）**