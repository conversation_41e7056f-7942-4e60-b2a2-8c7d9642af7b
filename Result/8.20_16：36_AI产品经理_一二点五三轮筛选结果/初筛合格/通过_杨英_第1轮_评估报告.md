------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 杨英  

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 未提及  
    - **候选人情况:** 本科，黄冈师范学院，计算机科学与技术  
    - **匹配结果:** 匹配  

- **年龄要求:**  
    - **JD 要求:** 未提及  
    - **候选人情况:** 33岁  
    - **匹配结果:** 匹配  

- **薪资范围:**  
    - **JD 要求:** 未提及  
    - **候选人期望:** 15-18K  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。  
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。  
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。  

- **简历证据分析:**  
    - **全周期产品落地经验：** 候选人主导过多个AI产品，包括酒店服务机器人系统、智能登记系统、AI翻译系统等，均从0到1完成全生命周期开发与落地，符合“主导AI智能体产品全周期规划与落地”的要求。  
    - **技术与业务桥梁能力：** 多次将AI技术（如OCR、语音识别、大模型）转化为具体业务场景中的产品功能，例如将语音识别与大模型结合用于酒店服务机器人，提升服务响应效率6倍以上，体现技术与业务的转化能力。  
    - **数据驱动优化能力：** 每个项目均包含明确的数据指标，如用户增长、DAU提升、错误率降低等，且具备构建评估体系的经验，如建立计费成功率、对账差异率等6项核心指标来推动产品优化。  

- **匹配度结论:** 高  

**3. 综合评估**

- **优势:**  
    - 10年技术背景+3年AI产品经验，具备从算法理解到产品落地的完整链条能力。  
    - 多个AI产品成功商业化案例，用户规模超2.3亿，具备显著的市场验证能力。  
    - 数据驱动思维突出，项目中均有量化指标支撑产品决策。  

- **风险与不足:**  
    - 无  

**4. 初筛结论**

- **淘汰条件：**  
    1. 无硬性门槛“不匹配”项。  
    2. 核心职责匹配度为“高”，未达淘汰标准。  

- **通过条件：**  
    1. 所有硬性门槛均为“匹配”。  
    2. 核心职责匹配度为“高”。  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人具备完整的AI产品全周期管理经验，能将算法能力有效转化为产品价值，并通过数据驱动方式持续优化产品表现。无任何硬性门槛不匹配项，且匹配度评估为“高”，完全符合AI产品经理岗位的核心要求。