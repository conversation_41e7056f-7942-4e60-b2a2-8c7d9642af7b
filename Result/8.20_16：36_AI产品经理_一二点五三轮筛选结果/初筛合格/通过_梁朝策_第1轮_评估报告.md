------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 梁朝策  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 本科（软件工程），大专（软件技术）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 23-30K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - 候选人主导过AI Agent工作流的0-1规划设计，明确提到“提升产品的适用性和灵活性”，符合第一条核心职责。
    - 具体描述了负责大语言模型（如DeepSeek）的选型、训练与调优，并强调“提高AI Agent的理解能力和生成能力”，体现出将算法能力转化为产品功能的能力，符合第二条核心职责。
    - 设计了基于RAG知识库的解决方案支持、大模型设备绩效及健康策略设计等内容，说明其具备构建AI产品评估与优化体系的经验，符合第三条核心职责。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有AI Agent产品从0到1的完整规划与落地经验。
    - 熟悉大模型训练、调优与集成，具备将算法能力转化为产品功能的实战能力。
    - 具备云架构设计能力（阿里云ACE认证），支持AI产品的技术可行性落地。

- **风险与不足:**
    - 无“潜在风险”项，所有硬性门槛均匹配。
    - 简历中未明确提及构建AI产品评估体系的具体指标与方法，但已有相关经验描述，可进一步验证。

**4. 初筛结论**

- **筛选结果：✅ 通过**
- **筛选理由：**
    - 所有硬性门槛均匹配。
    - 候选人具备与AI产品经理岗位高度匹配的实战经验，特别是在AI Agent产品设计、大模型调优、技术与业务转化等方面表现突出。
    - 核心职责匹配度为“高”，符合岗位核心要求。