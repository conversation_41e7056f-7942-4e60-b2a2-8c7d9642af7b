------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 梁朝策

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 本科（广东科技学院，软件工程）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 23-30K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **第一条核心职责：**
      - 候选人有主导AI Agent工作流的0-1规划设计经验（见简历描述）。
      - 曾负责大语言模型选型与训练，优化模型性能（如DeepSeek大模型），并设计AI Agent产品流程与策略。
      - 有多个AI产品从概念到落地的完整项目经历，如扶梯AI不安全行为监测平台、设施设备管理云平台等。
    - **第二条核心职责：**
      - 具备将算法能力转化为产品功能的经验，如YOLO机器视觉、RAG知识库应用、Transform深度学习算法等。
      - 在温氏AI预测性维护项目中与算法工程师协作进行模型训练与调优，体现了技术与业务之间的桥梁作用。
    - **第三条核心职责：**
      - 在多个项目中通过数据驱动方式优化产品性能，如优化数据处理流程、提升预测精度、设计AI Agent交互方案等。
      - 有构建AI产品评估体系的间接证据，如提升客户运营效率、优化设备健康策略等。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 具备AI Agent全流程产品设计经验，覆盖从0到1的产品落地。
    - 拥有大模型选型、微调、Prompt策略等核心技术理解与实践经验。
    - 成功将算法能力转化为实际产品功能，具备良好的技术与业务沟通能力。
    - 有多个AI产品市场推广与用户反馈优化的成功案例。

- **风险与不足:**
    - 无明显风险项，所有硬性门槛均匹配。
    - 虽然简历中未明确提及“构建AI产品评估体系”的完整流程，但有相关间接证据支持其具备相应能力。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、期望薪资均符合JD要求（或未设限）。在核心职责方面，简历中提供了充分的证据支持其具备AI产品经理所需的核心能力，包括AI产品全流程管理、算法与产品结合能力、数据驱动优化经验等，整体匹配度为“高”。