------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：梁朝策  
**年龄**：29岁  
**工作经验**：8年  
**学历**：本科（软件工程）  
**当前职位**：产品经理  
**期望薪资**：23-30K  

---

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科（软件工程）
    - **匹配结果:** 匹配

- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配

- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）
    - **候选人期望:** 23-30K（ExpectedSalary = 30K）
    - **匹配结果:** 不匹配  
      （30K > 20K × 1.2 = 24K）

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化
    3. 协调跨职能团队推进AI产品的开发与落地
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    1. **产品功能与用户体验设计**：  
       - 简历中明确提到“设计AI Agent产品的流程”“构思ASR+LLM+TTS全流程智能客户/数字人交互方案”“设计扶梯AI不安全行为智能监测云平台”，涉及用户体验和技术可行性设计。  
       - **证据充分**
    2. **制定产品路线图与优先级**：  
       - 提到“主导AI Agent工作流的0-1规划设计”“制定产品策略”“负责DeepSeek大模型的训练与调优”，体现产品路线图与迭代管理能力。  
       - **证据充分**
    3. **协调跨职能团队推进产品开发**：  
       - 简历中多次提到“协调团队资源”“与算法工程师紧密协作”“领导产品与UI团队”，体现跨职能协作能力。  
       - **证据充分**
    4. **市场定位与竞争策略制定**：  
       - 提到“推动产品市场推广”“打造智能化管理支持”“显著提升公共场所的安全监测效率”，但缺乏明确的市场策略制定描述。  
       - **证据较弱**

- **匹配度结论:** 高

---

**3. 综合评估**

- **优势:**
    - 拥有AI产品全流程管理经验，涵盖AI Agent、大模型训练、机器视觉等多个方向。
    - 持有PMP、敏捷认证、ACE阿里云认证，具备项目管理与云架构设计能力。
    - 具备从0到1的产品落地经验，并有明确的商业化成果（如扶梯监测平台、设备管理系统等）。

- **风险与不足:**
    - 期望薪资高达30K，远超JD薪资上限20K，且超过阈值24K（20K×1.2），**不符合薪资要求**。
    - 市场策略制定方面证据较弱，未明确展示差异化竞争策略制定能力。

---

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"（薪资不匹配）
    2. ❌ 核心职责匹配度 评估结果为 "**低**"（实际为“高”）

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项（存在薪资不匹配）
    2. ✅ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"

**筛选结果：** ❌ 淘汰  
**筛选理由：** 虽然候选人在学历、年龄、核心职责匹配度方面表现优秀，且具备丰富的AI产品经验与多项专业认证，但其期望薪资30K远高于JD薪资上限20K（超过1.2倍），属于硬性不匹配项，因此不符合岗位基本要求。