------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：夏思琪  
**年龄**：根据简历中“10年互联网产品设计经验”及教育背景推断，候选人年龄约为33岁（2014年毕业，10年工作经验）  

**工作经验**：AI产品经理1年经验 + 10年互联网产品设计经验  

**学历**：江西师范大学 计算机软件工程 工学学士学位（双证）  

**当前职位**：AI产品经理  

**期望薪资**：未明确注明，但结合候选人当前职位及行业水平，推测其期望薪资可能在18-25K区间  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科学历（计算机专业）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 约33岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（最高20K）
    - **候选人期望:** 推测为18-25K（存在超过20K的可能性）
    - **匹配结果:** 潜在风险（若期望薪资为25K，则超出JD上限的1.2倍（24K），则为“潜在风险”）

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - **职责1：** 候选人在“电商平台竞品分析系统”和“教培行业智能客服系统”中主导了AI功能设计与用户体验优化，涉及知识库构建、流程设计、对话逻辑梳理等，体现出对AI产品功能与用户体验的理解。
    - **职责2：** 在“电商平台竞品分析系统”中，候选人负责从需求到部署的全流程，并持续优化模型准确率，体现了产品路线图制定与迭代管理能力。
    - **职责3：** 在多个AI项目中（如花卉识别、智能客服），候选人协调数据、模型、开发团队，推动产品落地，体现出良好的跨团队协作能力。
    - **职责4：** 候选人主导竞品分析、市场定位设计，并推动产品商业化落地（如亚马逊商品运营决策系统），具备一定的市场策略制定能力。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 具备1年AI产品经理经验，且有多个AI项目落地经历（如客服系统、图像识别模型等）
    - 拥有10年传统互联网产品设计经验，产品路线图规划、需求管理、用户调研等能力扎实
    - 有华为云等大厂背景，获得iF设计奖等荣誉，具备较强的产品与用户体验设计能力

- **风险与不足:**
    - 薪资期望可能超过JD上限，存在“潜在风险”
    - 对AI技术原理的深入理解（如深度学习框架、模型训练流程）描述较弱，主要聚焦于产品层面

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄均符合要求，核心职责匹配度高，具备丰富的AI产品实战经验与跨职能团队协作能力。虽然存在薪资“潜在风险”，但未达到淘汰标准。