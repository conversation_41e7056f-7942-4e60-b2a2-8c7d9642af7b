------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：杨英  
**年龄**：33岁  
**工作经验**：13年  
**学历**：本科（计算机科学与技术）  
**当前职位**：软件产品经理  
**期望薪资**：15-18K  

---

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配

- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 33岁
    - **匹配结果:** 匹配

- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary=20K）
    - **候选人期望:** 15-18K
    - **匹配结果:** 匹配（18K ≤ 20K）

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    3. 协调跨职能团队推进AI产品的开发与落地  
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    1. **设计和定义AI产品功能与用户体验：**  
       - 简历中明确描述了多个AI产品设计与落地经验，如“AI语音交互系统”、“OCR/NLP技术落地”、“AI驱动的酒店服务机器人系统”、“AI OCR证件登记系统”等。  
       - 在“酒店服务机器人项目”中详细描述了系统架构设计、语音交互优化、用户体验提升（如操作时长从72s降至8s），体现了对AI产品功能与用户体验的深度把控。  
       - **证据原文：**“设计端云协同架构，硬件成本降低60%，支持17种语言自动切换，运营效率提升1700%”，“用户平均操作时长从72s降至8s，错误率降低94%”。

    2. **制定产品路线图与优先级：**  
       - 在多个项目中体现出产品规划与迭代能力，如“酒店服务机器人系统开发项目”三阶段迭代、“NFT交易平台从0到1搭建”、“沃橙结算系统全生命周期管理”等。  
       - 明确描述了“构建业务能力坐标系”、“设计可扩展的计费规则引擎”、“分省升级标准化手册”等产品路线规划内容。  
       - **证据原文：**“从0到1打造行业首个AI驱动的服务型酒店机器人解决方案”，“构建动态任务编排系统”，“制定产品架构并完成交互原型设计”。

    3. **协调跨职能团队推进开发与落地：**  
       - 多次提到跨部门协作经验，如“协调运营、研发、客服建立全国计费联调机制”、“联合市场部开展10+场行业公开课”、“主导需求分析与开发团队协同调试与验证功能”。  
       - 在“酒店服务机器人”项目中作为“软件产品负责人全程主导系统架构设计及跨部门协同开发”。  
       - **证据原文：**“协调运营、研发、客服建立全国计费联调机制，季度问题追溯效率提升4倍”，“跨部门协作：联合市场部开展10+场行业公开课”。

    4. **市场定位与竞争策略：**  
       - 在“小沃数字藏品平台”项目中明确描述了“差异化优势分析”、“竞品研究”、“市场定位”等策略制定行为。  
       - 在“腾讯云游戏接入项目”中提出“定向设计IPTV端组合定价”、“用户复购率进入行业前20%”等市场策略。  
       - **证据原文：**“分析市面主流数字藏品交易平台，京东灵稀，蚂蚁鲸叹，找到产品差异化优势”，“创新模式: 定向设计IPTV端「游戏大包+会员折扣」组合定价”。

- **匹配度结论:** 高

---

**3. 综合评估**

- **优势:**
    - 13年产品经验，其中10年技术背景，具备扎实的AI产品设计与落地能力。
    - 多个AI项目从0到1落地经验，涵盖语音交互、OCR识别、NFT平台、计费系统等多个AI应用场景。
    - 拥有完整产品生命周期管理经验，包括需求分析、PRD撰写、原型设计、版本迭代、商业化落地等。
    - 具备跨职能团队协作经验，擅长推动技术、运营、市场等多部门协同。

- **风险与不足:**
    - 无硬性门槛不匹配项。
    - 期望薪资在JD薪资范围上限附近（18K vs 20K），但仍在1.2倍阈值内，属于匹配项。
    - 未明确提及是否持有AI产品经理相关认证（如PMP等），但该条为加分项而非核心要求。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 无硬性门槛“不匹配”项。
    2. 核心职责匹配度为“高”。

- **通过条件：**
    1. 硬性门槛审查结果均为“匹配”。
    2. 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合岗位硬性要求；简历中提供了丰富的AI产品设计与落地经验，覆盖JD中所有核心职责，匹配度为“高”，具备进入下一轮面试的资格。