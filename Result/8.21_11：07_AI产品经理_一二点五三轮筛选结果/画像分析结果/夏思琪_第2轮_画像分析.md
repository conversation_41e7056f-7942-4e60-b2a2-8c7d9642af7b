# 候选人专业画像分析报告

**基本信息:**

- **姓名:** 夏思琪
- **分析日期:** 2025-04-05

**专业身份与能力层级:**

- **主要专业身份 (80-100分):**  
  **AI产品经理（偏向大模型应用与智能系统设计）** —— 85分  
  候选人具备1年AI产品经理经验，主导电商平台竞品分析系统、教培行业智能客服系统等AI项目，涵盖从需求分析、模型训练、流程设计到部署上线的全流程，具备明确的AI产品开发经验与交付成果。

- **重要辅助技能 (60-79分):**  
  **用户体验设计（UX）** —— 75分  
  拥有近4年UX设计师经验，主导多个华为云产品交互设计（如CodeArts IDE、DevUI组件库等），具备完整的设计流程把控能力，曾获iF设计奖与红点设计奖。

  **传统互联网产品设计与管理** —— 70分  
  拥有约6年传统互联网产品经验，包括从0到1的产品设计、市场分析、用户研究、原型设计及组件库建设，涵盖金融、教育、工具类产品。

- **边缘接触能力 (40-59分):**  
  **AI编码与模型训练** —— 55分  
  候选人参与过智能客服模型的全流程开发、YOLO模型训练等AI技术工作，具备一定实操经验，但未见主导或独立完成算法层面的深度工作。

**能力边界:**

- **擅长:**  
  - AI产品设计与落地（大模型、RAG、Agent、智能客服、图像识别等）
  - 用户体验设计（UX）
  - 产品需求分析与版本迭代管理
  - 组件库与设计规范建设

- **适合:**  
  - 传统互联网产品设计与管理
  - 与AI技术团队协作的产品化工作
  - 企业级SaaS产品设计
  - 敏捷协作下的产品交付与优化

- **不适合:**  
  - 算法研究与深度模型调优（无明确主导经验）
  - 技术架构设计（无后端/前端开发主导经验）
  - 纯技术类岗位（如机器学习工程师、数据科学家）

**职业发展轨迹:**

- **一致性:** 高  
  职业路径从传统互联网产品设计 → UX设计 → AI产品设计，具备良好的延续性与专业积累，尤其在AI应用领域实现了角色转型与能力拓展。

- **专业深度:** 中  
  在AI产品领域具备一定深度（参与多个模型训练与部署项目），但以产品视角为主，技术深度有限。UX设计方面有扎实经验与奖项背书。

**综合评估与建议:**

- **专业比重总分:** 85分  
- **可信度:** 高  
  项目描述清晰、有明确职责与成果，具备华为云、美迪科技等知名企业背书，且有奖项与认证支撑专业能力。

- **最终专业身份:**  
  **AI产品经理（偏向大模型应用与智能系统设计）**  
  ——具备从需求分析到模型训练、部署上线的全流程产品经验，擅长用户体验与智能系统结合的产品化落地。