------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 杜开广

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 38岁（1986年生）
    - **匹配结果:** 不匹配（候选人年龄超过JD上限的1.2倍：35 * 1.2 = 42，38 < 42，但大于35，因此不匹配）
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（最高约260K/年）
    - **候选人期望:** 10-12K（年期望约120-144K）
    - **匹配结果:** 匹配（候选人期望薪资远低于JD上限）

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - 候选人简历中未提供与AI产品相关的任何职责经验描述。项目经验中仅提到“杭州计策者大会”和“阿里巴巴钉钉团队建设”，均为会议组织和团队行政工作，未体现产品设计、定义、路线图制定、迭代优化、跨职能协作或市场策略制定等职责。
    - 简历技能部分虽然列举了大量技术技能（如Python、SQL、Docker、Kubernetes、Git、敏捷开发等），但未说明如何将这些技能应用于AI产品管理场景。

- **匹配度结论:** 低（缺乏与AI产品经理核心职责直接相关的经验证据）

**3. 综合评估**

- **优势:**
    - 本科学历符合JD要求。
    - 技术技能丰富，具备一定的技术理解能力。

- **风险与不足:**
    - 年龄超过JD要求的35岁上限。
    - 缺乏AI产品相关经验，核心职责匹配度低。
    - 项目经验中无AI产品设计、管理、路线图制定、市场策略制定等实际案例。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 硬性门槛审查中“年龄要求”不匹配，且核心职责匹配度为“低”。候选人未提供任何与AI产品经理核心职责相关的经验证据，项目经历中仅体现会议组织和团队支持类工作，无法胜任AI产品经理岗位的技术与产品双重职责。