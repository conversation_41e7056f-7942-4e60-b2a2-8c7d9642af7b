------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 郭俊威

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科（非全日制）+ 大专（工程造价）
    - **匹配结果:** 匹配

- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 28岁
    - **匹配结果:** 匹配

- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（即期望薪资不应超过20K×1.2=24K）
    - **候选人期望:** 13-14K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **设计产品功能与用户体验：**  
      简历中多次提到主导产品从0到1的落地，如“IPCS工程造价解决方案”中描述“主导全生命周期管理：从0到1负责产品规划、设计、开发至交付”，并涉及“PRD撰写与评审”“交互与界面设计”，体现产品功能设计能力。  
    - **制定产品路线图与优先级：**  
      在“云增盾数据分析师系统”中提到“管理需求流转，推动迭代优化，当时完成V1 & V3三个核心迭代版本上线”，显示具备产品迭代管理经验。  
    - **协调跨职能团队：**  
      在多个项目中均有“链接UG、研发、测试、运营的工作”“有效链接BIM联盟、研发团队、测试团队”等描述，体现良好的跨团队协作能力。  
    - **市场定位与竞争策略：**  
      在“云增盾数据分析师系统”项目中提到“完成竞品化优品分析报告，提炼核心差异点与优化方向，指导产品差异化定位”，说明具备市场分析与产品定位能力。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 多个从0到1落地的B/S产品经验，具备完整的产品生命周期管理能力；
    - 有PRD撰写、竞品分析、产品迭代管理、跨团队协作等关键能力；
    - 产品设计注重用户体验，有原型设计和交互优化的实践经验。

- **风险与不足:**
    - 虽然具备工程背景和产品经验，但简历中未体现AI/机器学习相关知识或技术理解；
    - 项目中未提及使用Axure/Figma等原型工具，仅提到交互设计；
    - 没有直接涉及AI模型训练、部署或算法逻辑的产品经验；
    - 缺乏AI产品经理认证或相关深度学习框架的使用经验。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合硬性门槛要求，且在产品设计、迭代管理、跨团队协作、市场分析等方面具备与JD高度匹配的能力。虽然缺乏AI技术背景，但其产品方法论和执行能力较强，具备进一步面试评估的潜力。建议进入下一轮面试，重点考察其对AI技术的理解与产品落地能力。