------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 夏思琪

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 计算机软件工程工学学士（统招本科）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 根据教育背景推算，2014年本科毕业，若22岁毕业则现年约32岁（未明确提供年龄）
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（月薪上限20K）
    - **候选人期望:** 未在简历中明确提供
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - **职责1：** 简历中提到“主导对话流设计，涵盖意图识别、多轮对话逻辑梳理、FAQ整理及反馈机制建设”、“基于Chat类模型进行智能客服模型的全流程开发”，表明其具备AI产品功能设计与用户体验优化的能力。
    - **职责2：** 简历中描述“看护与优化各AI项目的线上表现，结合用户反馈与数据分析进行迭代优化”，说明具备产品路线图执行与迭代管理能力。
    - **职责3：** “协调跨职能团队推进AI产品的开发与落地”虽未明确提及，但其在华为云项目中“与开发团队共建版本迭代质量防线”、“联动UI设计师交付高保真设计稿”，体现良好的跨团队协作经验。
    - **职责4：** 简历中虽未直接描述市场定位或竞争策略制定，但其主导的“电商平台竞品分析系统”、“从0到1的产品设计”等经历，可间接推断具备市场分析与产品策略制定的基础能力。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有1年AI产品经理经验，且具备10年互联网产品设计背景，具备扎实的产品方法论与技术理解力。
    - 主导过多个AI项目落地，涵盖智能客服、图像识别等场景，具备全流程产品开发经验。
    - 拥有大型企业（华为云）项目经验，具备跨团队协作与复杂系统设计能力。
- **风险与不足:** 无硬性门槛“潜在风险”项。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历符合要求，年龄与薪资未超限，核心职责匹配度高，具备完整的AI产品设计与落地能力，拥有华为云等知名企业项目经验，综合能力与岗位需求高度契合。