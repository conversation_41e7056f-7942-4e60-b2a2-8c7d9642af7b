------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 陈兴

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上  
    - **候选人情况:** 本科  
    - **匹配结果:** 匹配  
- **年龄要求:**
    - **JD 要求:** 35岁以下  
    - **候选人情况:** 23岁  
    - **匹配结果:** 匹配  
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（即年薪约 195K - 260K）  
    - **候选人期望:** 10-15K（月薪，即年薪约 120K - 180K）  
    - **匹配结果:** 匹配（期望薪资低于JD上限）

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1（产品功能与用户体验设计）：**  
      候选人在多个项目中展示了产品功能设计能力，例如“设计内部知识库问答功能，包括框架、交互逻辑及原型”，“根据用户反馈设计插件并编写prompt”，体现出对用户体验和功能可用性的关注。  
    - **职责2（制定路线图与迭代优化）：**  
      候选人明确提到“制定推进规划及里程碑计划，推动平台快速上线”，并在多个项目中描述了“版本迭代”、“持续优化平台性能”等行为，表明具备产品路线图规划与迭代管理经验。  
    - **职责3（协调跨职能团队）：**  
      候选人提到“推动LLM模型与审核流程融合”，“协调模型选型、能力强化、数据支撑的完整AI框架”，显示出与技术团队协作的能力。  
    - **职责4（市场定位与竞争策略）：**  
      简历中未明确提及市场分析、竞争策略制定或商业化策略相关内容，缺乏直接证据。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
    - 具备AI产品设计与迭代经验，尤其在NLP方向有多个项目落地案例；
    - 熟悉LLM、RAG、fewshot等AI技术，能有效推动技术与产品融合；
    - 具备产品路线图规划与原型设计能力，能独立完成PRD相关工作。

- **风险与不足:**
    - 无明确市场分析或竞争策略制定经验，与JD中“制定AI产品的市场定位与竞争策略”要求存在差距；
    - 项目经历偏向技术融合与功能设计，对商业化落地、用户增长等策略层面涉及较少；
    - 薪资期望低于JD范围上限，虽在匹配范围内，但可能反映对岗位价值认知或市场定位存在偏差。

**4. 初筛结论**

- **淘汰条件：**
    - 无硬性不匹配项；
    - 核心职责匹配度为“中”，未达“低”标准。
- **通过条件：**
    - 所有硬性门槛均匹配；
    - 核心职责匹配度为“中”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合硬性要求，核心职责方面具备较强的产品设计与迭代能力，尤其在AI技术融合方面有丰富经验。尽管缺乏市场定位与竞争策略方面的直接证据，但整体匹配度为“中”，具备进入下一轮面试的资格。