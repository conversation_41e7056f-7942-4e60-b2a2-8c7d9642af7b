------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：杨英  
**年龄**：33岁  
**工作经验**：13年  
**学历**：本科（计算机科学与技术）  
**当前职位**：软件产品经理  
**期望薪资**：15-18K  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 33岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）
    - **候选人期望:** 15-18K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - **职责1（功能与用户体验设计）：**
      - 简历中明确提到“主导AI语音交互系统与多语言智能适配”，“设计端云协同架构”，“语音识别字准率93%”，“响应延迟<800ms”，“支持17种语言自动切换”。
      - 在“AI智能化酒店服务机器人系统开发项目”中，详细描述了从语音交互、知识图谱构建到多模态系统集成的完整产品设计流程，体现出对用户体验和技术可行性的深度把控。
    - **职责2（产品路线图与迭代管理）：**
      - 多次提到“从0到1搭建NFT交易平台”，“重构计费规则引擎”，“构建AI翻译流程优化系统”，“完成12省市系统整合”，体现出清晰的产品路线图制定能力。
      - 在“沃橙结算系统”、“数字藏品平台”等项目中，均展示了产品从立项、设计、开发到持续迭代的完整生命周期管理。
    - **职责3（跨职能团队协调）：**
      - 明确提到“协调运营、研发、客服建立全国计费联调机制”，“推动12省完成系统自主运维转型”，“团队培养出3名AI工程化人才”。
      - 在“AI智能化酒店服务机器人系统开发项目”中，作为软件产品负责人，主导系统架构设计及跨部门协同开发。
    - **职责4（市场定位与竞争策略）：**
      - 在“小沃数字藏品平台”中，有“竞品分析”、“差异化优势挖掘”、“市场定位”等明确动作。
      - 在“腾讯云游戏接入项目”中，有“差异化增长策略”、“定向定价设计”、“权益生态闭环构建”等市场策略制定与执行。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有10年+技术背景，计算机科学与技术专业，具备扎实的AI产品技术理解力。
    - 主导过多个从0到1的AI产品落地项目，涵盖语音交互、OCR、NLP、多语言翻译、智能机器人等，商业化成功率100%。
    - 在多个项目中展现出卓越的产品路线图规划、跨团队协作、用户需求洞察与市场策略制定能力。

- **风险与不足:**
    - 无硬性门槛“潜在风险”或“不匹配”项。
    - 未明确提及Axure/Figma等原型设计工具的使用经验，但其在多个项目中描述了“输出20+核心模块PRD”、“设计产品原型与功能清单”，可推测具备相关能力。

**4. 初筛结论**

- **淘汰条件：**
    1. 无硬性门槛“不匹配”项。
    2. 核心职责匹配度为“高”。

- **通过条件：**
    1. 硬性门槛审查结果中无“不匹配”项。
    2. 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、期望薪资均符合JD要求，且在AI产品设计、技术落地、跨团队协作、市场策略等方面具备丰富经验与实证成果，核心职责匹配度为“高”。建议进入下一轮面试。