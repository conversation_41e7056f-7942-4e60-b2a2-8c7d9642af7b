------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：曾杰    
**年龄**：38岁 

**工作经验**：10年以上

 **学历**：硕士

**当前职位**：产品经理

**期望薪资**：未提供



**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 本科
    - **候选人情况:** 硕士
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 38岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 14-20K
    - **候选人期望:** 未提供
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 负责AI技术在户外产品中的应用规划，推动智能户外装备的创新与发展
    - 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平
    - 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行

- **简历证据分析:**
    - **AI技术应用经验：** 候选人在多个项目中主导AI技术落地，如“配电网无人机智能巡检系统”中使用YOLO模型进行绝缘子破损检测，并设计AI缺陷识别逻辑（高概率区间自动判定、低概率人工复核），具备AI产品设计能力。
    - **产品需求转化能力：** 在“文稿在线项目组”中，候选人通过验收测试用例分析验证AI功能可行性，并从用户需求出发设计轻量化界面，体现较强的需求转化与产品设计能力。
    - **项目全流程推动经验：** 在多个项目中均体现出从需求分析、方案设计、技术落地到商业化验证的全流程推动能力，如主导“智网无人机智能操作系统4.0”，实现从需求确认到交付仅3个月，具备项目全流程管理能力。
    - **户外产品背景：** 简历中未明确提及候选人具备户外用品或户外用户场景相关的项目经验，但在“配电网无人机智能巡检系统”中涉及复杂地形（山区、戈壁滩）巡检场景，具备一定的户外环境产品设计经验。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    1. 具备丰富的AI产品设计经验，多个项目涉及AI技术落地与产品化。
    2. 强大的项目全流程推动能力，能从需求分析到交付全程主导，具备出色的执行力。
    3. 拥有硕士学历，技术背景扎实，具备Java/Python等开发能力，理解AI技术实现路径。

- **风险与不足:**
    1. 简历中未明确展示候选人对户外用户需求的深度理解或相关产品经验，尽管在电力巡检项目中涉及复杂地形，但与“户外产品”用户群体可能存在差异。
    2. 期望薪资未提供，后续需确认是否与JD薪资范围匹配。

**4. 初筛结论**

- **淘汰条件：**
    1. 无硬性门槛不匹配项。
    2. 核心职责匹配度为“高”，符合通过标准。

- **通过条件：**
    1. 硬性门槛审查结果中没有“不匹配”项。
    2. 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均满足硬性门槛要求；在AI产品设计、需求转化、项目全流程推动方面均有丰富且高质量的项目经验，核心职责匹配度为“高”。尽管缺乏明确的户外产品用户理解证据，但其在复杂环境下的产品设计经验具备较强迁移能力，建议进入下一轮面试进一步评估其户外产品适配能力。