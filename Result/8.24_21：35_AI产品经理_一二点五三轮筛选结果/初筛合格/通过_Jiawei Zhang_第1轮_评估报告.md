------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：Jiawei Zhang  
**年龄**：31  

**工作经验**：8年  

**学历**：硕士  

**当前职位**：全栈工程师  

**期望薪资**：27-45K  

---

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 本科
    - **候选人情况:** 硕士
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 31岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 14-20K
    - **候选人期望:** 27-45K
    - **匹配结果:** 潜在风险  
        - 候选人期望薪资上限45K > JD薪资上限20K * 1.2 = 24K，存在显著溢价，属于**潜在风险**。

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 负责AI技术在户外产品中的应用规划，推动智能户外装备的创新与发展
    - 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平
    - 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行

- **简历证据分析:**
    - **AI技术应用与产品化经验**：候选人在JiawisTech Solutions LLC主导“AI交互平台”项目，涉及多模型AI对话服务与企业级解决方案，具备AI模型集成与调优经验（简历中提及“AI模型集成与大语言模型调优有深入研究”），并带领团队完成AI客服系统的部署与NLP流程梳理，说明其具备将AI技术转化为产品功能的能力。
    - **需求转化与解决方案设计**：候选人在多个项目中承担核心开发与架构设计职责，如金融证券交易系统重构、政府房产交易系统优化等，显示出较强的需求理解与技术方案输出能力。但在简历中**未明确提及对户外用户或场景的理解**，缺乏与户外产品相关的市场洞察经验。
    - **跨部门推动能力**：候选人在政府房产交易项目中“协调与政府单位需求变更对接，组织任务分配及阶段评审”，并“指导新人快速上手项目结构与编码规范”，表明其具备良好的团队协作与项目推动能力。

- **匹配度结论:** 中  
    - 候选人具备AI技术应用、产品需求转化与项目推动能力，但缺乏与户外产品或用户场景相关的直接经验。

---

**3. 综合评估**

- **优势:**
    - 具备AI项目落地经验，尤其在AI客服系统、推荐引擎原型设计方面有实际产出。
    - 拥有多年技术背景，擅长跨团队协作与复杂系统推动，适合产品经理中偏技术路线的岗位。
    - 拥有硕士学历，学习能力强，技术理解深入。

- **风险与不足:**
    - 期望薪资远高于JD薪资范围上限，存在较大成本风险。
    - 缺乏户外行业或用户场景理解，与岗位中“智能户外装备”的结合点不明确。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌  
    2. 核心职责匹配度 评估结果为 "**低**"。 ❌  

- **通过条件：**
    1. 硕士学历满足本科门槛。 ✅  
    2. 年龄未设限。 ✅  
    3. 核心职责匹配度为“中”。 ✅  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人满足学历与年龄门槛，核心职责匹配度为“中”，具备AI产品落地经验与项目推动能力。虽存在期望薪资超出JD范围的**潜在风险**，但未超过20%阈值的直接淘汰线，建议在下一轮面试中重点评估其对户外用户需求的理解与成本接受度。