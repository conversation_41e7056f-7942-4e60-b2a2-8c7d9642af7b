------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾杰  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 硕士（计算机技术）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 38岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 简历未提供
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **职责1：主导AI智能体产品的全周期规划与落地**
        - 简历中明确描述了多个AI产品从0到1的开发过程，如“文稿在线智能项目组”中主导基于大型模型构建文档分析系统，设计多任务协作机制、Prompt工程、模型轻量化方案，并完成商业化验证与长期规划。
        - 在“配电网无人机智能巡检系统”项目中，曾主导从需求分析、模型设计（YOLO、概率区间划分）、系统部署、测试验收到商业化推广的全流程，体现了对AI产品全生命周期管理的丰富经验。
    - **职责2：在技术与业务之间架设桥梁**
        - 在多个项目中，候选人展示了将AI能力转化为业务价值的能力。例如在无人机巡检系统中，通过AI识别线路缺陷，实现人工替代、成本节约400万元/年。
        - 在文档分析系统中，将传统文档检索转化为知识问答交互模式，准确度提升60%，响应时间优化至3分钟内，体现出对用户价值的精准把握。
    - **职责3：构建AI产品的评估体系，数据驱动优化**
        - 在无人机项目中，设置了明确的模型判定阈值（高概率区间0.9、疑似区间0.7-0.9、低概率区0.7），并设计了人工复核机制，体现出对AI模型评估体系的构建能力。
        - 在文稿在线项目中，通过API/Prompt模式构建测试案例集，进行多格式文档验证，展示了数据驱动的验证与优化思维。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有多个AI产品从0到1的设计与落地经验，涵盖文档分析、无人机巡检等不同场景。
    - 熟悉Prompt工程、RAG、Agent技术，并在实际项目中应用LangChain等主流框架。
    - 具备将AI能力转化为业务价值的实战经验，且能构建评估体系进行模型优化。
    - 拥有PMP、NPDP认证，具备扎实的产品与项目管理能力。

- **风险与不足:**
    - 简历未明确提供期望薪资，若后续沟通中薪资超出JD设定范围，可能构成潜在风险。
    - 虽然技能中提到LangChain等Agent框架，但具体使用细节未在项目经历中详述，建议后续深入询问。

**4. 初筛结论**

- **淘汰条件：**
    1. 无任何硬性门槛“不匹配”项。
    2. 核心职责匹配度为“高”，符合通过条件。
- **通过条件：**
    1. 所有硬性门槛审查结果为“匹配”。
    2. 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合要求（或未设限），并在简历中提供了多个AI产品从0到1的完整开发经验，覆盖Prompt工程、模型评估、数据驱动优化等JD核心职责，匹配度为“高”，具备进入下一轮面试的资格。