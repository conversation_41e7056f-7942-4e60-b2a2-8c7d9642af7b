# 候选人专业画像分析报告

**基本信息:**

- **姓名:** 吴伟铭
- **分析日期:** 2025-04-22

---

**专业身份与能力层级:**

- **主要专业身份 (80-100分):** AI产品经理（聚焦AIGC、AI图像生成、写真类产品）  
  **得分区间：85-92分**  
  - 多段连续工作经验（约2年）围绕AI图像生成、写真类产品展开，具备从0到1的完整产品孵化经验。
  - 涉及Stable Diffusion、LoRA训练、RAG、意图识别等核心技术，具备一定技术理解力。
  - 项目经验丰富，涵盖AI写真、AI换装、AI漫画工具、AI拍照打印一体机等多个AIGC方向。

- **重要辅助技能 (60-79分):** 产品设计、项目管理、跨部门协作  
  **得分区间：65-75分**  
  - 简历中多次提及产品设计、流程优化、协同工具使用等，具备良好的产品方法论。
  - 具备跨部门协作经验，尤其在与算法、设计、开发侧的协同中表现突出。
  - 掌握敏捷开发流程，具备项目推进与资源协调能力。

- **边缘接触能力 (40-59分):** 技术实现、编程语言、智能硬件产品  
  **得分区间：45-55分**  
  - 提及“掌握各类编程语言的基础知识”，但未具体说明掌握程度，也未在项目中体现编码能力。
  - 涉及智能硬件产品设计（如AI拍照打印一体机），但更偏向产品定义而非技术实现。
  - 在AI平台搭建中更多是需求方角色，未体现具体开发能力。

---

**能力边界:**

- **擅长:**
  - AI图像生成产品设计（Stable Diffusion、文生图、扩图、LoRA训练）
  - AI写真类产品从0到1的孵化与优化
  - AIGC平台类产品设计（如Agent工作流、RAG知识库整合）
  - 与算法团队协同进行模型效果调优

- **适合:**
  - AI Agent平台类产品设计
  - AIGC内容创作工具的产品策划
  - 跨部门协同的AI项目管理
  - 图像AI能力整合与产品化（如与华为手机合作）

- **不适合:**
  - 需要深度技术实现或编码能力的岗位（如AI工程师、算法工程师）
  - 非图像方向的AIGC产品（如语音、NLP类）
  - 无AI背景的传统互联网产品岗位
  - 智能硬件的技术开发与工程实现

---

**职业发展轨迹:**

- **一致性:** 高  
  - 职业路径高度聚焦于AI图像生成与AIGC领域，从实习到正式工作连续三段经历均围绕AI写真、AI图片处理、AI Agent等展开。
  - 从产品助理角色逐步过渡到主导产品设计，职业成长路径清晰。

- **专业深度:** 中  
  - 对AI图像生成技术有深入理解，尤其在Stable Diffusion、LoRA训练、写真生成流程等方面具备实操经验。
  - 但更多是作为产品方角色参与，未体现底层技术实现或算法建模能力，技术深度有限。

---

**综合评估与建议:**

- **专业比重总分:** 88分  
- **可信度:** 高  
  - 项目描述具体，多次提及技术关键词（如Stable Diffusion、RAG、LoRA、意图识别等），具备技术应用证据。
  - 工作经历时间连续、职责清晰，具备较高的可信度。

- **最终专业身份:** 资深AI图像生成产品经理（偏向AIGC写真与内容创作平台）