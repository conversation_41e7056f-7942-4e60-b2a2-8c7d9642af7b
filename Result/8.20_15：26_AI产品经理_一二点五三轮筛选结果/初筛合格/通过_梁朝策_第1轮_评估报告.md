------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 梁朝策

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 本科（软件工程）+ 大专（软件技术）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 23-30K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **职责1匹配证据：**
        - 简历中明确提到“主导AI Agent工作流的0-1规划设计”，“构思ASR+LLM+TTS全流程智能客户/数字人交互方案”，“负责设计扶梯AI不安全行为智能监测云平台”，体现出全周期AI产品规划与落地能力。
    - **职责2匹配证据：**
        - “负责大语言模型的选型与训练”，“负责DeepSeek大模型的训练与调优”，“协调团队资源，制定产品策略”，“AI Agent的理解能力和生成能力”等描述，说明候选人具备将算法能力转化为产品功能的经验。
    - **职责3匹配证据：**
        - “RAG知识库的应用和解决方案支持”，“优化模型性能”，“优化数据处理效率”，“提升客户人机交互的智能化水平”等描述，体现出数据驱动优化模型与用户体验的能力。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有多个AI Agent与大模型实际项目经验，涵盖从0-1的产品设计、模型调优、系统集成与市场推广。
    - 熟悉主流AI产品技术栈（如RAGFlow、Coze、FastGPT），具备LLM选型、Prompt工程、模型微调等硬技能。
    - 有完整的AI产品生命周期管理经验，且具备云平台设计与交付能力（阿里云ACE认证）。

- **风险与不足:**
    - 无硬性门槛“潜在风险”项。
    - 简历中未提及构建AI产品评估体系的具体方法或指标体系，但在多个项目中提到“优化模型性能”、“提升效率”等成果，可视为间接证据。

**4. 初筛结论**

- **淘汰条件：**
    - 未满足任意淘汰条件。
- **通过条件：**
    - 硬性门槛审查中无“不匹配”项。
    - 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资期望均符合岗位未设门槛的要求。在核心职责方面，简历中提供了丰富的AI Agent产品经验、大模型调优与落地案例，具备将技术能力转化为产品价值的能力，整体匹配度高，具备进入下一轮面试的资格。