------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 杜嘉琪

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 南京航空航天大学，计算机科学与技术专业，学士
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 1994年2月出生，当前年份2025年，年龄为31岁
    - **匹配结果:** 匹配（因JD未设定年龄上限）
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 简历中未提供期望薪资
    - **匹配结果:** 匹配（因JD未设定薪资上限）

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **第一条职责（主导AI产品全周期）：**
        - 简历中未见任何主导AI产品从规划到落地的项目经验描述。候选人主要为后端开发工程师角色，参与云平台功能模块的开发工作。
    - **第二条职责（技术与业务桥梁）：**
        - 简历中未体现其在业务与技术之间进行沟通、转化算法能力为产品功能的经验。
    - **第三条职责（构建评估体系）：**
        - 简历中提到使用Python编写自动化测试脚本、部署API网关，但未体现构建AI模型评估体系或数据驱动优化模型表现的经验。

- **匹配度结论:** 低

**3. 综合评估**

- **优势:**
    - 拥有丰富的云平台后端开发经验，熟悉Spring Boot、微服务架构、DevOps流程，具备良好的技术背景。
    - 曾在腾讯云与华为云两家大厂工作，具备良好的工程能力与项目协作经验。

- **风险与不足:**
    - 简历中缺乏与AI产品经理岗位直接相关的产品设计、市场分析、用户需求转化、AI模型评估等经验。
    - 所有工作经历均为软件开发角色，未体现出产品主导或跨职能沟通能力。

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人具备良好的技术背景和云平台开发经验，但其简历中未体现与AI产品经理岗位相关的核心职责经验，包括AI产品设计、技术与业务沟通、AI模型评估体系建设等关键能力。因此，核心职责匹配度评估结果为“低”，不符合岗位基本要求。