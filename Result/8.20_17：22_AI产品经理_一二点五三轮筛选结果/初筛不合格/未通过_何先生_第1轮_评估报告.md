------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 何先生

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 郑州轻工业大学，计算机应用技术（软件方向），大专
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 出生于1984年，现年40岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 简历未提供
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **职责1（产品全周期管理）：** 简历中未见明确主导AI产品从规划到落地的全过程经验。仅有的项目描述集中在推荐系统开发，且未体现产品经理视角的统筹管理职责。
    - **职责2（技术与业务沟通）：** 简历中虽有参与项目开发、收集用户需求的经历，但缺乏明确描述其在技术团队与业务团队之间进行“翻译”或“协调”的职责与实践。
    - **职责3（数据驱动优化）：** 在某电商平台推荐系统项目中提到“用户购买转化率提升30%”，显示出一定数据意识，但未体现构建评估体系、持续优化模型表现等AI产品特有的评估机制经验。

- **匹配度结论:** 低

**3. 综合评估**

- **优势:** 
    - 拥有计算机专业背景，具备一定技术理解力。
    - 在电商平台商品推荐系统项目中展示了算法应用经验。
    - 具备市场调研与用户需求分析的基础能力。

- **风险与不足:** 
    - 缺乏AI产品经理所需的主导AI产品全流程经验。
    - 对LLM、Prompt工程、Agent架构等核心技能无明确体现。
    - 未展示在技术与业务之间进行产品化抽象的能力。
    - 核心职责匹配度低，无法胜任AI产品经理岗位要求。

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 不满足。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 尽管候选人学历、年龄、薪资均符合要求，但其在核心职责匹配度评估中结果为“低”，尤其在主导AI产品全周期管理、技术与业务沟通、构建评估体系等方面缺乏明确证据，无法满足AI产品经理岗位的核心要求。