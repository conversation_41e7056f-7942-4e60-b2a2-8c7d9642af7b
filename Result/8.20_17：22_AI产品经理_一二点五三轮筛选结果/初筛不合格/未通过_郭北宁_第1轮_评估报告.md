------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 郭北宁

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 本科（非全日制）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 40岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 12-16K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - 候选人简历中**未体现AI产品设计、规划或落地经验**，也**未展示主导AI产品全生命周期管理的能力**。
    - 简历内容主要围绕**售前技术支持、云解决方案设计、IT基础设施项目交付**，缺乏**将算法能力转化为产品功能**的实际案例。
    - 没有提及**构建评估体系、数据驱动优化模型表现**等职责相关的经验或能力。
    - 简历中提到的项目（如专属云、上云平台、存储采购）均为**传统IT基础设施类项目**，与AI产品开发或管理无直接关联。

- **匹配度结论:** 低

**3. 综合评估**

- **优势:** 
    - 具备丰富的售前技术支持经验，熟悉云架构与IT基础架构。
    - 获得多项技术认证，具备方案设计与客户沟通能力。
- **风险与不足:** 
    - 完全缺乏AI产品相关经验，未体现任何AI产品经理所需的核心职责匹配项。
    - 简历中未提及Prompt工程、Agent架构、模型评估体系等JD中明确要求的技能。
    - 没有展示任何将AI能力转化为产品功能的实际经验。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ❌ 淘汰  
**筛选理由：** 尽管候选人满足所有硬性门槛要求，但其简历中**未体现任何AI产品经理岗位的核心职责相关经验**，也**未展示必要的技术理解能力与产品化能力**，因此核心职责匹配度为“低”，不符合岗位基本要求。