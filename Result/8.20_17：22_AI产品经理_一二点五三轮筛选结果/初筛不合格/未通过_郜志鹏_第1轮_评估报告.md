------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 邹志凯

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 本科（广东工业大学，计算机科学与技术）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 未提供年龄信息
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 未提供期望薪资
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **职责1分析：** 简历中显示候选人主导过多个产品的策划、设计和实施工作（如“智能生活助手APP”和“智能家居控制系统”），但**未提及AI智能体、大模型或Agent相关产品经验**，缺乏直接匹配证据。
    - **职责2分析：** 简历中未体现其在技术与业务之间进行翻译、将算法能力转化为产品功能的经验。**未见与AI、LLM、Prompt工程或模型能力转化相关的描述**。
    - **职责3分析：** 简历中提及“收集用户反馈，优化产品功能”“进行产品测试和优化”，但**未涉及构建评估体系、数据驱动优化模型表现等AI产品特有的评估机制**。

- **匹配度结论:** 低

**3. 综合评估**

- **优势:**
    - 具备产品经理基础工作能力，如需求分析、竞品分析、产品设计、项目管理等。
    - 拥有计算机专业背景，掌握Java、Python等编程语言，技术基础扎实。
- **风险与不足:**
    - **核心硬技能严重缺失**：未体现对LLM、Prompt工程、Agent架构、AI产品交互设计等JD中列出的核心硬技能的理解或经验。
    - **缺乏AI产品落地经验**：简历中所有项目均为传统智能产品（如APP、控制系统），**未涉及AI模型、大模型或智能体相关产品设计**。
    - **数据驱动能力不足**：虽有优化产品功能的描述，但**未体现通过数据分析、模型评估来驱动产品迭代的能力**。

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 不符合通过条件（因匹配度为低）。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽具备产品经理的基础能力，但**未展示与AI产品经理岗位相关的核心技能与项目经验**，尤其在LLM、Agent架构、Prompt工程、AI产品交互设计等方面缺乏直接证据，核心职责匹配度评定为“低”，不符合初筛通过标准。