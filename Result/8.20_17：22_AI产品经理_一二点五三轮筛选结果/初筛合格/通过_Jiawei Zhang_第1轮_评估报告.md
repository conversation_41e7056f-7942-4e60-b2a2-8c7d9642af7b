------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** Jiawei Zhang

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 硕士（计算机与信息科学）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 31岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 27-45K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - 在 **JiawisTech Solutions LLC** 担任技术合伙人期间，主导了“AI交互平台”项目，负责多模型AI对话服务与企业级解决方案的部署，涉及使用 **LangChain** 和 **OpenAI API**，并带领团队完成AI客服系统的NLP问答流程梳理，提升响应率与满意度。这与“主导AI智能体产品的全周期落地”职责高度相关。
    - 其项目经验中提到“使用用户行为数据构建推荐引擎原型，并负责核心算法逻辑与业务对接流程设计”，体现出其具备将算法能力转化为产品功能的能力，符合“在技术与业务之间架设桥梁”的要求。
    - 在多个项目中涉及性能监控、日志分析、异常检测算法开发，如“开发性能异常检测算法，提前识别潜在风险点”，显示出其具备数据驱动优化系统表现的经验，可支持构建AI产品评估体系。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:** 
    - 具备AI产品落地经验，包括AI客服系统、推荐引擎原型开发，涉及NLP流程梳理与模型集成。
    - 技术背景深厚，拥有8年开发经验，熟悉主流AI工程工具链（如LangChain、OpenAI API）。
    - 有团队管理与产品推进经验，符合“主导产品全周期”的要求。
- **风险与不足:** 
    - JD未明确学历、年龄、薪资要求，因此无潜在风险项。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 硬性门槛均未设限，候选人满足所有匹配条件。其具备AI产品实际落地经验、技术与业务桥梁能力、数据驱动优化能力，与JD中三大核心职责高度契合，匹配度为“高”。