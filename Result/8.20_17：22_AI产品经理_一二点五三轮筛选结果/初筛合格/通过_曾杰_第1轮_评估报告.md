------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾杰

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 硕士（计算机技术专业，非全日制）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 38岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 未提及
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - 曾主导多个AI相关产品项目，如“文稿在线智能项目组”的文档分析系统（基于大型语言模型构建），涉及Prompt工程、多任务协作、端到端推理等能力，符合“AI智能体产品全周期规划与落地”要求。
    - 在“配电网无人机智能巡检系统”项目中，设计YOLO模型用于缺陷检测，设定模型输出阈值策略（如高概率区间自动判定、疑似区间人工复核），体现对算法能力的理解与产品化转化能力。
    - 多个项目中均有明确的数据指标反馈（如准确率提升50%、年节约成本400万元等），说明候选人具备数据驱动的评估与优化意识。
    - 在“文稿在线”项目中提到“引入多Agent工具应对复杂业务场景”，体现对Agent架构的理解与应用意识。

**匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有多个AI产品主导经验，涵盖从需求验证、模型集成、系统设计到商业化落地的完整流程。
    - 熟悉Prompt工程、Agent技术（LangChain等框架）及RAG技术，具备将算法能力转化为产品价值的能力。
    - 项目成果显著，具备数据驱动优化与评估体系构建的实际经验。

- **风险与不足:**
    - 硕士为非全日制学历，可能在部分企业中存在认知偏差（但JD未明确全日制要求）。
    - 年龄38岁，虽在合理职业发展区间，但需结合企业团队结构评估文化适配性（JD未设年龄限制）。

**4. 初筛结论**

- **淘汰条件：** 无  
- **通过条件：** 全部满足

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资期望均符合岗位要求（或未设限制），且在简历中提供了多个AI产品主导经验，涵盖JD中核心职责的全部要点，具备将算法能力产品化、构建评估体系、主导全周期落地的完整能力，匹配度高。