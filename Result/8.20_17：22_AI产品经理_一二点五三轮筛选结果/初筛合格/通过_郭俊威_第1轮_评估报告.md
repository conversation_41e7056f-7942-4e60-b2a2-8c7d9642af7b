------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 郭俊威

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 本科（非全日制）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 28岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 13-14K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - 候选人具备多个**从0到1落地产品**的经验（如IPCS工程造价解决方案、AUTO CDM平台、TBM工程进度管理平台等），体现了较强的**全周期产品管理能力**，符合第一条职责要求。
    - 在产品设计过程中，候选人多次提到**与技术团队协作**（如“链接UG、研发、测试、运营的工作”、“与数据团队协作，定义并验证核心指标计算规则与算法逻辑”），并能将复杂技术能力转化为产品功能（如“参数化建模与规则引擎”），体现了一定的**技术与业务沟通能力**，符合第二条职责要求。
    - 在“云增盾数据分析师系统”项目中，候选人主导了**智能分析模块**设计，并与数据团队协作验证算法逻辑，同时设计了**可视化分析工具**用于提升用户体验，部分体现了**数据驱动优化**的能力，但未明确提及AI模型或评估体系的构建经验。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
    - 多个0-1产品落地经验，具备较强的产品规划与执行能力。
    - 有与技术团队协作的经验，能理解并转化技术能力为产品功能。
    - 具备数据分析意识，曾主导设计智能分析模块和可视化组件。

- **风险与不足:**
    - 简历中未体现对AI产品特别是**大型语言模型（LLM）**、**Prompt工程**、**Agent架构**等核心技术的理解与应用经验。
    - 未明确展示对**AI产品交互设计**（如对话流程、错误处理机制）或**模型评估体系**的实际经验。
    - 虽有数据驱动优化经验，但主要集中于传统数据分析，未涉及AI模型的表现优化。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → **未触发**
    2. 核心职责匹配度 评估结果为 "**低**"。 → **未触发**

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → **满足**
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → **满足**

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人通过所有硬性门槛审查，具备较强的产品管理能力和技术与业务之间的沟通能力，部分符合AI产品经理的核心职责要求。尽管在AI核心技术理解与应用方面经验不足，但其产品方法论和执行力具有可迁移性，适合进入下一轮面试进一步评估其AI产品适配潜力。