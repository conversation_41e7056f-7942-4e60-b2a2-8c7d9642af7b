------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾杰  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 硕士（计算机技术专业，非全日制）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 38岁
    - **匹配结果:** 潜在风险  
      *解释：候选人年龄超出JD上限（35岁），但未超过35*1.2=42岁，因此判定为“潜在风险”*
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪
    - **候选人期望:** 简历中未提供期望薪资
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1：设计AI产品功能与用户体验**  
      曾主导多个AI产品设计，如“配电网无人机智能巡检系统”中设计图像虚焦识别机制，提升准确率5%，速度提升50%；在“文稿在线”项目中优化UI界面，简化操作步骤，实现自动化文档处理。  
      **证据：**  
      > "设计 UI 界面优化，简化操作步骤，大幅减小复杂度。"  
      > "图像虚焦区域识别设计...准确率提升 5%，阵列处理速度提升 50%"

    - **职责2：制定产品路线图与优先级**  
      在多个项目中明确制定了产品迭代计划，如“文稿在线项目”中分阶段进行需求验证、轻量化设计、效果验证、商业化验证与长期规划；在“无人机巡检平台”中提出三期推进计划。  
      **证据：**  
      > "一、需求验证阶段...二、轻量化设计...三、效果验证阶段...四、下一步迭代...五、长期规划"  
      > "项目规模：（千万级别项目，分三期推进）"

    - **职责3：协调跨职能团队落地AI产品**  
      多次担任跨职能团队协调者角色，如在“珠海恒信德盛”中对接合作伙伴实现API集成；在“无人机巡检系统”中组建团队完成模型代换任务。  
      **证据：**  
      > "外部合作部门协作：对接主要合作伙伴，实现 API 搭建及产品整合集成"  
      > "全面测试验证后，组建团队完成 1 年本标段任务，推动模型代换"

    - **职责4：制定市场定位与竞争策略**  
      在“无人机巡检系统”中明确进行竞品分析与市场推广，制定标准化输出（如《云南电网规范》），并推动产品在全国多省市落地应用。  
      **证据：**  
      > "市场分析与迭代：根据产品形态、目标用户群体、产品性能、竞品分析、市场需求，调整产品策略。"  
      > "成果已在多地推广应用...累计产值超 2500 万元"

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有多个AI产品从0到1的完整落地经验，涵盖图像识别、文档自动化、无人机巡检等多个AI场景。
    - 具备较强的产品路线图规划能力，且有明确的商业化验证经验。
    - 拥有PMP与NPDP认证，具备系统化的产品管理方法论。

- **风险与不足:**
    - 年龄超出岗位要求（35岁以下），超出幅度为11.4%（38 vs 35），属于“潜在风险”。
    - 简历中未提及Axure或Figma等原型工具的具体使用经验。
    - 虽然具备AI产品经验，但未明确展示对AI/机器学习基本原理的理论理解（如算法类型、模型训练等）。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → 无  
    2. 核心职责匹配度 评估结果为 "**低**"。 → 否  

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → 满足  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → 满足  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历满足要求，薪资信息未提供视为匹配，年龄略超JD上限但未超过20%阈值，判定为“潜在风险”而非“不匹配”。核心职责匹配度评估为“高”，具备完整AI产品落地经验与商业化成果。虽未明确展示原型工具使用经验，但其职责中已体现产品设计能力，不影响通过初筛。