------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 陈兴

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上  
    - **候选人情况:** 本科  
    - **匹配结果:** 匹配  
- **年龄要求:**
    - **JD 要求:** 35岁以下  
    - **候选人情况:** 23岁  
    - **匹配结果:** 匹配  
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（最高值约为20K）  
    - **候选人期望:** 10-15K  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1（功能与用户体验设计）：**  
      候选人负责多个AI平台产品的功能设计与优化，如内容安全审核平台、AI智能客服平台等，均涉及用户需求调研、prompt设计、原型设计等具体工作。例如“负责平台迭代优化，通过收集用户需求与反馈，优化功能及流程，提升用户体验”、“设计内部知识库问答功能，包括框架、交互逻辑及原型”等描述，直接体现其在用户体验与功能设计方面的能力。
    - **职责2（产品路线图与迭代优化）：**  
      在多个项目中均有明确的规划与推进描述，如“制定推进规划及里程碑计划，推动平台快速上线”、“负责负面数据收集清洗、prompt编写调整、效果迭代及测试验收，保障功能落地质量”，表明其具备制定路线图与推动产品迭代的实际经验。
    - **职责3（跨职能协作与落地推进）：**  
      描述中多次出现“推动LLM模型与审核流程融合”、“协调模型选型-能力强化-数据支撑的完整AI框架”等内容，体现出候选人具备与技术团队协作推进产品落地的能力。
    - **职责4（市场定位与竞争策略）：**  
      简历中未见直接涉及市场定位或竞争策略制定的具体描述，相关职责证据不足。

- **匹配度结论:** 中  

**3. 综合评估**

- **优势:**  
  - 拥有AI产品全流程设计与管理经验，尤其在AI模型落地、prompt工程、用户需求分析方面有扎实实践。  
  - 熟悉LLM、RAG、fewshot等AI技术，并能结合业务场景进行产品化落地。  
  - 具备良好的原型设计能力与PRD文档撰写经验，符合核心技能要求。  

- **风险与不足:**  
  - 缺乏市场定位与竞争策略制定方面的直接经验，可能影响其在产品战略层面的胜任力。  
  - 薪资期望低于JD范围，虽不构成淘汰项，但后续面试中需评估其对岗位价值的理解与匹配度。  

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合硬性门槛要求，具备AI产品设计与迭代管理的丰富经验，能够胜任岗位的核心职责。虽然缺乏市场策略方面的直接经验，但整体匹配度为“中”，满足进入下一轮面试的条件。