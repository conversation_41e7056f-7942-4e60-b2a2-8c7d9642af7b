------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾宪涛  

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 专科及以上  
    - **候选人情况:** 本科（法律专业），大专（大数据技术与应用）  
    - **匹配结果:** 匹配  

- **年龄要求:**  
    - **JD 要求:** 35岁以下  
    - **候选人情况:** 42岁  
    - **匹配结果:** 不匹配  

- **薪资范围:**  
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）  
    - **候选人期望:** 15-20K  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    3. 协调跨职能团队推进AI产品的开发与落地  
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**  
    - 曾主导多个产品从0到1的开发流程，具备完整的产品生命周期管理经验（如Zigbee3.0全屋智能项目、护理助手APP等），符合第2、4条职责。  
    - 有PRD文档编写经验（如资产管理项目、Zigbee3.0项目），并参与产品原型设计，具备一定的产品定义与用户体验设计能力，部分匹配第1条职责。  
    - 担任项目经理（如公安部大数据智能化项目）和跨部门协调经验（如恒信德公司职责中提到“协调其他岗位在产品上的配合布置”），具备跨职能团队协作能力，匹配第3条职责。  
    - 但简历中**未见明确涉及AI/机器学习相关内容的经验或技术可行性判断的实践**，未体现AI产品相关的技术理解或落地经验。

- **匹配度结论:** 中  

**3. 综合评估**

- **优势:**  
    - 有丰富的产品经理经验，涵盖医疗、智能硬件、金融等多个领域。  
    - 拥有ACP项目管理认证、产品经理认证等专业证书，具备产品路线图规划与迭代管理能力。  
    - 多次负责PRD文档撰写、产品原型设计、市场分析与竞品调研，符合岗位部分核心技能要求。

- **风险与不足:**  
    - 年龄42岁，超过JD要求的35岁上限，且超过上限的1.2倍（42 > 35 * 1.2 = 42），**属于“不匹配”项**。  
    - 简历中未体现AI相关产品经验、机器学习原理理解、技术可行性判断能力等核心硬技能。  
    - 虽掌握Python语言，但其描述偏向投资交易，未体现与AI产品开发相关的技术落地能力。

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。✅  
    2. 核心职责匹配度 评估结果为 "**低**"。❌  

- **通过条件：**  
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。❌  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。✅  

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人年龄42岁，超出JD要求的35岁上限，且超过1.2倍容忍阈值，属于“不匹配”项。尽管其具备丰富的产品经理经验与项目管理能力，但未体现AI产品相关的技术理解与实践经验，综合不满足岗位要求。