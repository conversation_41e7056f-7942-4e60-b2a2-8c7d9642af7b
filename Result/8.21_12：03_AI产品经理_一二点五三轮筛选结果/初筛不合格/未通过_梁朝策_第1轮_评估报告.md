------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：梁朝策  
**年龄**：29岁  

**工作经验**：8年产品经理经验，涵盖AI产品设计、工业互联网平台、低代码平台等多个领域  

**学历**：广东科技学院 本科（软件工程）  

**当前职位**：产品经理  

**期望薪资**：23-30K  

---

### **1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科（软件工程）
    - **匹配结果:** 匹配

- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配

- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（即月薪上限为20K）
    - **候选人期望:** 23-30K
    - **匹配结果:** 不匹配（候选人期望薪资下限23K > 20K × 1.2 = 24K，虽其期望薪资上限较高，但其最低期望仍超出上限）

---

### **2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化
    3. 协调跨职能团队推进AI产品的开发与落地
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    1. **AI产品设计与用户体验**：候选人在多个项目中主导AI Agent产品设计，包括ASR+LLM+TTS全流程交互方案、扶梯AI不安全行为监测平台（YOLO机器视觉+边缘计算），体现出对AI产品功能设计与用户体验优化的能力。
    2. **产品路线图与迭代管理**：候选人主导多个AI产品从0到1的落地，如AI Agent工作流、大模型训练调优、RAG知识库应用等，并推动产品持续优化与迭代，具备明确的产品规划能力。
    3. **跨职能协作与落地推进**：候选人多次协调算法、开发、硬件等团队完成产品开发，如在温氏AI预测性维护项目中与算法工程师协作调优模型，在扶梯AI项目中负责硬件选型与云平台设计，体现了良好的跨职能协作能力。
    4. **市场定位与竞争策略**：候选人负责产品市场推广、渠道孵化支持，成功将产品商业化落地（如扶梯AI平台应用于多个场景、设施设备管理系统服务10个领先客户），说明其具备市场策略制定与推广能力。

- **匹配度结论:** 高

---

### **3. 综合评估**

- **优势:**
    - 多年AI产品设计经验，覆盖从策略到落地的全流程
    - 拥有PMP认证、敏捷认证、阿里云ACE认证，具备系统化产品管理能力
    - 多个AI产品实现商业化落地，具备市场策略与推广经验

- **风险与不足:**
    - 期望薪资明显超出JD薪资上限（23K vs 20K），属于硬性门槛“不匹配”

---

### **4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查中有“不匹配”项（期望薪资超出上限）
    2. ❌ 核心职责匹配度为“高”

- **通过条件：**
    1. ❌ 硬性门槛审查中存在“不匹配”项
    2. ✅ 核心职责匹配度为“高”

**筛选结果：** ❌ 淘汰  
**筛选理由：** 尽管候选人在AI产品设计、跨职能协作、市场推广等方面具备高度匹配的能力与经验，且学历、年龄均符合要求，但其期望薪资（23-30K）显著高于JD薪资上限（20K），超出20%阈值，构成硬性门槛“不匹配”，因此被淘汰。