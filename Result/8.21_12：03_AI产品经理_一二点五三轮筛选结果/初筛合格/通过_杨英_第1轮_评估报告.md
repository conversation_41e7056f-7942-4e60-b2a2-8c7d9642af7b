------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：杨英  
**年龄**：33岁  

**工作经验**：13年  

**学历**：本科（计算机科学与技术）  

**当前职位**：软件产品经理  

**期望薪资**：15-18K  

---

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 33岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）
    - **候选人期望:** 15-18K
    - **匹配结果:** 匹配

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1（功能与用户体验设计）：**
        - 候选人主导过多个AI产品设计，如“AI语音交互系统”、“OCR/NLP技术落地”，并强调“用户体验优化”、“用户操作时长从72秒降至8秒”等成果。
        - 在“酒店服务机器人系统”中详细描述了“端云协同架构”、“语音+大模型双引擎”、“对话压缩”等技术设计，体现出对技术可行性与用户体验的双重把控。
    - **职责2（产品路线图与迭代）：**
        - 在“酒店服务机器人系统”项目中，候选人分三阶段推进系统演进，从语音中枢→知识引擎→多模态集成，清晰体现了产品路线图的规划与迭代能力。
        - 在“沃橙结算系统”中也有明确的产品生命周期管理描述。
    - **职责3（跨职能团队协作）：**
        - 在多个项目中均有跨部门协作描述，如“协调运营、研发、客服建立全国计费联调机制”、“跨平台业务落地中协调腾讯与联通团队”、“联合市场部开展公开课”等，体现出较强的跨职能协作能力。
    - **职责4（市场定位与竞争策略）：**
        - 在“小沃数字藏品平台”项目中，候选人明确提到“差异化优势分析”、“竞品分析”、“市场定位”等内容，并通过“权益保障体系”、“数字资产港”等功能构建竞争壁垒。
        - 在“腾讯云游戏接入项目”中，提出“IPTV端组合定价策略”，实现差异化增长。

- **匹配度结论:** 高

---

**3. 综合评估**

- **优势:**
    1. 拥有13年丰富的产品经理经验，其中10年具备技术背景（计算机专业），且有多个AI产品从0到1落地的完整经历。
    2. 具备完整的产品生命周期管理经验，涵盖产品设计、路线图制定、跨部门协作、商业化落地等全流程。
    3. 多个项目体现市场洞察与竞争策略能力，具备差异化产品设计经验。

- **风险与不足:**
    - 无硬性门槛风险项。
    - 个别项目描述中缺乏具体工具使用细节（如Axure/Figma），但整体产品文档与原型设计能力在多个项目中有所体现。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 无硬性门槛“不匹配”项。
    2. 核心职责匹配度为“高”，未达淘汰标准。
- **通过条件：**
    1. 所有硬性门槛均“匹配”。
    2. 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、期望薪资均完全符合岗位要求，且在AI产品设计、路线图制定、跨职能协作、市场策略制定等方面均有丰富且高质量的项目经验支撑，核心职责匹配度为“高”。无任何淘汰项，建议进入下一轮面试。