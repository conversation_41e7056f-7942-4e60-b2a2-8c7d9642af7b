------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 宁丰东  

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 未提供  
    - **候选人情况:** 本科（吉林大学珠海学院，计算机科学与技术）  
    - **匹配结果:** 匹配  

- **年龄要求:**  
    - **JD 要求:** 未提供  
    - **候选人情况:** 42岁  
    - **匹配结果:** 匹配  

- **薪资范围:**  
    - **JD 要求:** 未提供  
    - **候选人期望:** 30-40K  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
    1. 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。  
    2. 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。  
    3. 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。  

- **简历证据分析:**  
    1. **Agent架构设计经验：**  
       - 在“AI短视频创作平台”项目中，候选人使用 **LangGraph 构建多 Agent 系统**，包括搜索Agent、调研Agent、分析Agent、评估Agent，结合互联网搜索、TikTok关联视频数据和评论进行创意设计。  
       - 在“AI招聘项目”中，使用 **langgraph 构建 RAG 系统**，并接入 Qwen-Max 模型进行工作流规划和简历评估。  
       - 在“NFT图片和3D模型生成工作流”项目中，使用 **langchain 框架** 构建 Agent 工作流，说明其具备独立设计Agent系统的能力。  
       - 这些项目均体现了其对 **ReAct、Planning、Tool Use 等机制的理解与实现能力**。

    2. **RAG 与 Function Calling 实现能力：**  
       - 在“AI招聘项目”中，构建了基于 langgraph 的简历 RAG 系统，并使用 Qwen-Max 进行工作流规划，体现其 **RAG 的实际落地经验**。  
       - 在“AI短视频创作平台”项目中，通过浏览器插件实现视频素材提取，并结合多模态分析提取创意模板，具备 **Function Calling 的实际应用场景**。

    3. **大模型API集成经验：**  
       - 多个项目中使用 **Qwen、DashScope、开源模型（如 Whisper、Stable Diffusion）**，并实现了模型接入与调用。  
       - 在“AI视频翻译工具”项目中，使用 **Qwen2 模型进行翻译和汇总压缩**，并在“AI招聘项目”中接入 Qwen-Max，说明其 **深度使用 OpenAI 系列模型及国产模型 API 的经验**。

    4. **技术实现能力：**  
       - 精通 Python，使用 FastAPI 构建后端服务，如“AI短视频创作平台”使用 FastAPI 和 PostgreSQL、DuckDB 作为后台 API 和数据库分析。  
       - 在多个项目中应用异步编程与高并发处理逻辑，如“AI招聘项目”中使用 WebSocket 与模型交互，体现其 **异步编程与高并发服务支撑能力**。

- **匹配度结论:** 高  

**3. 综合评估**

- **优势:**  
  - 拥有多个从0到1的AI Agent项目经验，涵盖短视频创作、招聘、NFT生成等复杂场景。  
  - 精通 Python 与 FastAPI，具备构建高并发 AI 服务的能力。  
  - 深度使用 LangChain、LangGraph 等框架，具备自主设计 Agent 架构的能力。  
  - 具备多模型接入经验，涵盖 Qwen、DashScope、开源模型等。

- **风险与不足:**  
  - 无明显“潜在风险”项。  
  - JD未提供明确薪资与年龄上限，因此未触发任何“潜在风险”判断。  

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  

- **通过条件：**  
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历为本科，符合基本要求；年龄与期望薪资均未超出JD未提供的上限，故未构成风险；在简历中提供了多个与JD核心职责高度匹配的项目经验，具备从0到1构建Agent系统、实现RAG与Function Calling、多模型集成等关键能力，整体匹配度为“高”。