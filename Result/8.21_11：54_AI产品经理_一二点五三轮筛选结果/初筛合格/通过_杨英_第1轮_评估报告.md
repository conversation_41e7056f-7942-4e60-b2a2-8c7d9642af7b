------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：杨英    
**年龄**：33岁 

**工作经验**：13年

 **学历**：本科（计算机科学与技术）

**当前职位**：软件产品经理

**期望薪资**：15-18K  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 33岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）
    - **候选人期望:** 15-18K（ExpectedSalary = 18K）
    - **匹配结果:** 匹配（18K ≤ 20K）

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - **设计和定义AI产品功能与用户体验：** 候选人主导AI语音交互系统、OCR/NLP产品落地，商业化成功率100%，具备完整的产品设计能力。其在“AI智能化酒店服务机器人系统开发项目”中主导系统架构设计，并实现语音交互-知识服务-场景应用的全链路智能化升级，体现强用户体验设计能力。
    - **制定产品路线图与优先级：** 在多个项目中（如酒店服务机器人系统、AI翻译项目）均有明确的阶段划分与迭代路径描述，如三阶段构建智能交互解决方案，体现路线图规划能力。
    - **协调跨职能团队推进产品落地：** 多次提及“跨部门协作”、“协调运营、研发、客服建立全国计费联调机制”，并主导项目从需求分析到上线的全流程推进，具备跨职能团队协作经验。
    - **制定市场定位与竞争策略：** 在“小沃数字藏品平台”项目中进行竞品分析，设计差异化产品策略，并推动平台市场份额提升至行业Top 3，体现市场定位与策略制定能力。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    1. 具备扎实的AI产品设计与落地经验，主导多个AI产品从0到1全过程，商业化成功率100%。
    2. 有完整的产品生命周期管理经验，涵盖路线图制定、跨团队协作、市场策略制定等核心职责。
    3. 技术背景深厚（计算机科学与技术），能与开发团队高效协同，具备技术可行性判断能力。

- **风险与不足:**
    无硬性门槛风险项。
    附加项中未明确提及使用Axure/Figma等原型工具的具体项目经验，但整体产品文档输出能力（如86份PRD）与项目成果（如用户路径模型重构）可间接佐证其原型设计与文档撰写能力。

**4. 初筛结论**

- **淘汰条件：**
    1. 无
- **通过条件：**
    1. 所有硬性门槛审查结果为“匹配”
    2. 核心职责匹配度为“高”

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、期望薪资均符合岗位硬性要求，且在AI产品设计、路线图规划、跨团队协作及市场策略制定等方面具备丰富的项目经验与明确成果，核心职责匹配度高，具备进入下一轮面试的资格。