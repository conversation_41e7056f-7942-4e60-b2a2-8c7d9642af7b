------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：夏思琪  
**年龄**：根据简历中“2010 - 2014 江西师范大学统招本科”，以及“2021.10 - 2024.04”等时间信息推算，候选人年龄约为34岁。  
**工作经验**：1年AI产品经验 + 10年互联网产品设计经验  
**学历**：江西师范大学计算机软件工程专业本科（工学学士学位）  
**当前职位**：AI产品经理  
**期望薪资**：未明确提供，但结合其AI产品经理职位及过往公司性质，可合理推测其期望薪资在15K以上  

------



**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科学历（计算机相关专业）
    - **匹配结果:** 匹配

- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 约34岁
    - **匹配结果:** 匹配

- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）
    - **候选人期望:** 未明确提供，但结合其职位和经验，预估在15K以上
    - **匹配结果:** 匹配（未超过20K上限）

------



**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    3. 协调跨职能团队推进AI产品的开发与落地  
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1：**  
      候选人曾主导电商平台竞品分析系统、教培行业智能客服系统、智能客服全流程开发、花卉识别YOLO模型训练等项目，涉及需求对接、流程设计、用户反馈收集、模型调优等，体现了对AI产品功能与用户体验的设计能力。  
      **证据：** “主导对话流设计，涵盖意图识别、多轮对话逻辑梳理、FAQ整理及反馈机制建设，提升客服效率”、“需求对接、数据标注质检、环境配置、模型训练到最终交付等环节”、“看护与优化各AI项目的线上表现，结合用户反馈与数据分析进行迭代优化”。

    - **职责2：**  
      候选人在多个AI项目中负责持续跟进与迭代优化，如“不断提升模型准确率与产品体验”、“结合用户反馈与数据分析进行迭代优化”，体现了制定路线图和推动迭代的能力。  
      **证据：** “看护与优化各AI项目的线上表现，结合用户反馈与数据分析进行迭代优化”。

    - **职责3：**  
      曾与开发团队共建版本迭代质量防线、推动产品落地上线及迭代优化，具备跨职能协作经验。  
      **证据：** “与前端开发团队合作，推动产品的落地上线及迭代优化”、“与开发团队共建版本迭代质量防线”。

    - **职责4：**  
      简历中未见明确涉及市场定位、竞争策略、商业化成果等内容，缺乏直接证据支持该职责匹配。  

- **匹配度结论:** 中

------



**3. 综合评估**

- **优势:**
  - 拥有1年AI产品经理经验，参与多个AI项目从需求到交付的全流程
  - 具备良好的用户体验设计与产品文档撰写能力
  - 能与开发团队协作，推动产品落地与迭代优化

- **风险与不足:**
  - 缺乏市场策略制定、商业分析、差异化竞争策略等经验
  - 期望薪资接近薪资上限，需关注其后续谈判空间

------



**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人满足所有硬性门槛要求，且在AI产品设计、迭代优化、跨团队协作方面具备实际经验，虽在市场策略方面经验不足，但整体匹配度为“中”，符合岗位基本资格要求。