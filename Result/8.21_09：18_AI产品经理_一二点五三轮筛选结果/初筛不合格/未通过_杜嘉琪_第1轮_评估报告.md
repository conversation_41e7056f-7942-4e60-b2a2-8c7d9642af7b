------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 杜嘉琪

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 南京航空航天大学，计算机科学与技术专业，学士学位
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 出生日期1994年02月，当前年份2024年，年龄30岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（月薪上限20K）
    - **候选人期望:** 简历中未提供期望薪资
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1：设计和定义AI产品的功能与用户体验**  
      → 简历中无任何关于产品设计、用户体验、需求调研、PRD撰写等经验描述。候选人经历集中在**软件开发**与**云平台后端开发**，未体现任何AI产品设计能力。
    - **职责2：制定产品路线图与优先级**  
      → 简历中无任何产品规划、路线图制定、版本管理、优先级排序等内容。
    - **职责3：协调跨职能团队推进AI产品开发**  
      → 简历中虽提及“完成项目文档编写，包括需求分析、设计文档等”，但未体现跨团队协作、项目推进、产品与研发/测试/市场等多方协调的经验。
    - **职责4：制定AI产品的市场定位与竞争策略**  
      → 简历中无任何市场分析、用户调研、竞争策略、产品定位相关内容。

- **匹配度结论:** 低

**3. 综合评估**

- **优势:**  
  - 技术背景扎实，具备丰富的云平台开发经验，熟悉主流开发框架与DevOps工具链。  
  - 有在华为、腾讯等大厂工作的经历，具备良好的工程能力与文档编写能力。

- **风险与不足:**  
  - **核心职责匹配度低**：简历中未体现任何AI产品经理所需的核心能力，如产品设计、用户调研、PRD撰写、产品路线图规划、市场策略制定等。  
  - **缺乏AI产品相关经验**：简历中无任何AI、机器学习、产品定义、用户体验设计等关键词或项目经历。  
  - **岗位理解偏差**：该候选人更偏向**软件工程师**或**系统开发工程师**角色，而非AI产品经理。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. ✅ 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. ❌ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人虽满足学历、年龄和薪资等硬性门槛，但**核心职责匹配度低**，简历中未体现任何AI产品经理所需的职责能力，如产品设计、路线图规划、跨团队协作、市场策略制定等关键能力。其背景更偏向于**软件开发工程师**而非AI产品经理。