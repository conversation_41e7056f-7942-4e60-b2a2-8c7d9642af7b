------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 陈兴

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 23岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪
    - **候选人期望:** 10-15K
    - **匹配结果:** 不匹配

> **说明**：JD薪资上限为20K，候选人期望上限为15K，15K > 20K * 0.8 = 16K，因此判定为“**不匹配**”。

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1（产品功能与用户体验）：** 候选人主导多个AI产品功能设计，如内容安全审核平台、智能客服平台，明确提到“设计编写prompt并进行效果测试”、“优化功能及流程，提升用户体验”、“设计内部知识库问答功能，包括框架、交互逻辑及原型”，具备明确的产品功能设计与用户体验优化经验。
    - **职责2（产品路线图与迭代优化）：** 候选人负责多个平台的迭代优化，如“制定推进规划及里程碑计划，推动平台快速上线”、“持续优化平台性能”、“负责负面数据收集清洗、prompt编写调整、效果迭代及测试验收”，表明其具备路线图制定与迭代管理能力。
    - **职责3（协调跨职能团队）：** 虽未明确提及“跨职能团队”字样，但其负责“模型选型-能力强化-数据支撑”的完整AI框架搭建，涉及与模型团队、开发团队的协作，可推断具备跨团队推进经验。
    - **职责4（市场定位与竞争策略）：** 简历中未提供关于市场定位、竞争策略、商业化策略等方面的直接证据。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
    - 具备AI产品全流程设计与迭代经验，熟悉大模型、RAG、fewshot等技术，能结合业务场景设计功能。
    - 拥有多个实际AI产品落地项目经验，涵盖内容审核、客服、对话机器人等领域。
    - 年龄与学历均符合岗位要求，具备良好的技术理解与产品落地能力。

- **风险与不足:**
    - **薪资要求低于JD上限20K，且超出下限的80%（15K > 16K）**，存在“不匹配”项。
    - 简历中未体现关于市场定位、竞争策略制定方面的经验或能力。
    - 缺乏产品需求文档（PRD）撰写、Axure/Figma工具使用等硬技能的直接证据。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"。（薪资）
    2. 核心职责匹配度 评估结果为 "**低**"。（未触发）

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。（❌ 不满足）
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。（✅ 满足）

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人薪资期望上限（15K）高于JD薪资上限（20K）的80%阈值（即16K），属于硬性门槛“不匹配”项，因此被淘汰。尽管其在AI产品设计、迭代、技术落地等方面具备较强经验，核心职责匹配度为“中”，但仍无法进入下一轮。