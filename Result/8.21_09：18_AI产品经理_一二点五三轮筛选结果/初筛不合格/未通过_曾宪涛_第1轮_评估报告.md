------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾宪涛

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科，在读MBA
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 42岁
    - **匹配结果:** 不匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪
    - **候选人期望:** 15-20K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - 曾主导多个智能产品项目，如Zigbee3.0全屋智能系统、中国移动空调智能运维平台、医养管理系统等，具备**产品功能定义、用户体验设计、PRD撰写能力**。
    - 在多个项目中担任产品经理，负责**产品路线图制定、迭代优化与优先级管理**，如“Zigbee3.0全屋智能”项目中“定义产品定位、范围，制定产品需求书，推进产品实施与迭代优化”。
    - 有协调跨职能团队的经验，如“在产品研发过程中负责需求管理、工作协调”、“负责产品在开发过程中的需求管理、工作协调”。
    - 具备**市场定位与竞争策略制定经验**，如“关注行业企业，了解行业商业模式和竞争优势，对行业内产品进行深度迭代”，“智联家居销售额突破400万”。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    1. 多年智能产品管理经验，涵盖AI相关的智能运维、智能家居、医养系统等领域。
    2. 拥有多个从0到1的产品落地项目经验，具备完整的产品生命周期管理能力。
    3. 持有ACP项目管理认证、产品管理认证等专业资质，具备较强的项目推进与团队协作能力。

- **风险与不足:**
    - 年龄超过岗位要求上限7岁，超出阈值20%（35*1.2=42），因此判定为“不匹配”。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 虽然候选人在核心职责匹配度方面表现优异，具备AI相关产品管理经验与完整项目落地能力，但其年龄为42岁，超出JD要求的35岁以下限制，且已超过20%的容忍阈值（42 > 35 * 1.2），因此判定为“不匹配”，不符合硬性门槛要求。