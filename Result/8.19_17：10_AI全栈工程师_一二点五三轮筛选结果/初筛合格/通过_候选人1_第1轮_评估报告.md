------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 宁丰东  

---

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 未提供  
    - **候选人情况:** 本科（吉林大学珠海学院，计算机科学与技术）  
    - **匹配结果:** 匹配  

- **年龄要求:**  
    - **JD 要求:** 未提供  
    - **候选人情况:** 42岁  
    - **匹配结果:** 匹配  

- **薪资范围:**  
    - **JD 要求:** 未提供  
    - **候选人期望:** 30-40K  
    - **匹配结果:** 匹配  

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
  1. 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。  
  2. 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。  
  3. 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。  

- **简历证据分析:**  
  1. 候选人主导开发了多个基于Agent的AI系统，包括“AI短视频创意Agent”、“AI招聘系统”等项目，**明确使用LangGraph构建多Agent系统，并结合RAG进行信息检索与生成**，与JD第1、2条职责高度匹配。  
  2. 在“AI招聘项目”中，**使用Qwen-Max进行工作流规划与评估，并通过DashScope TTS和语音识别模块构建面试Agent**，体现了function calling与多模型集成能力，与JD第2条职责匹配。  
  3. 在多个项目中**接入Qwen、GPT3.5等模型API，并构建统一的AI能力调用接口**，具备模型切换与管理能力，符合JD第3条职责要求。  

- **匹配度结论:** 高  

---

**3. 综合评估**

- **优势:**  
  - 具备从0到1构建AI Agent系统的丰富经验，涵盖任务规划、工具调用、RAG、多模型集成等核心能力。  
  - 技术栈全面，涵盖Python、LangChain/LangGraph、FastAPI、Milvus、PostgreSQL等关键技术组件。  
  - 多个项目中担任技术负责人，体现良好的系统设计与项目管理能力。

- **风险与不足:**  
  - 无硬性门槛“潜在风险”或“不匹配”项。  

---

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  

- **通过条件：**  
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人满足所有硬性门槛要求，且在核心职责方面具备丰富的项目经验与技术能力，特别是在Agent系统设计、RAG实现、多模型集成等方面与JD高度匹配。