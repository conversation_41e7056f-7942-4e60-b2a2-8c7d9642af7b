------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 吴兴强  

---

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 未提供（默认为本科）  
    - **候选人情况:** 上海电机学院，本科  
    - **匹配结果:** 匹配  

- **年龄要求:**  
    - **JD 要求:** 未提供，假设无明确限制  
    - **候选人情况:** 28岁  
    - **匹配结果:** 匹配  

- **薪资范围:**  
    - **JD 要求:** 未提供  
    - **候选人期望:** 未提供  
    - **匹配结果:** 匹配  

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
    1. 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。  
    2. 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。  
    3. 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。  

- **简历证据分析:**  
    - **职责1（Agent架构设计）：** 简历中未见明确涉及Agent系统架构设计的内容，也未提及任务规划、记忆管理等模块的实现经验。  
    - **职责2（Agent核心功能实现）：** 简历中提及了RAG系统的开发与调优经验（“主导RAG全链路架构设计”、“构建混合检索系统”、“开发多路召回融合算法”、“设计动态排序引擎”等），具备较强RAG工程能力。但未提及function calling、代码执行、联网搜索等能力的实现经验。  
    - **职责3（模型API统一接入）：** 简历中提到“基于昇腾910B硬件平台搭建Docker容器化集群”、“训练Hengqin-R1大模型”、“申请了多项专利”，但未提及OpenAI、Claude等多模型API接入与统一管理层设计经验，缺乏直接证据支持该职责匹配。  

- **匹配度结论:** 中  

---

**3. 综合评估**

- **优势:**  
    1. 具备扎实的RAG系统设计与调优经验，涵盖检索、排序、融合等关键环节，具备较强工程实现能力。  
    2. 有大模型训练与部署经验，熟悉SFT、LoRA、量化、动态批处理等技术，具备良好的模型工程化落地能力。  

- **风险与不足:**  
    1. 缺乏Agent系统架构设计与实现经验，未体现任务规划、记忆管理等模块的构建能力。  
    2. 未体现function calling、代码执行、联网搜索等Agent核心功能的实际开发经验。  
    3. 未展示对多模型API（如OpenAI、Claude）的接入与统一管理层设计经验，可能影响职责3的履行。  

---

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度评估结果为 "**低**"。  

- **通过条件：**  
    1. 硬性门槛审查结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 所有硬性门槛均匹配，核心职责匹配度为“中”，虽未完全覆盖Agent架构与function calling等能力，但在RAG系统开发与大模型工程化方面具备扎实经验，具备进一步评估价值。