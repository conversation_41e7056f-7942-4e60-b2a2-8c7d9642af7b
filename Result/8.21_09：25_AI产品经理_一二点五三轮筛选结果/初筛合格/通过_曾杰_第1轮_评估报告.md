------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾杰  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 硕士（计算机技术专业）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 38岁
    - **匹配结果:** 潜在风险  
      *（38 ≤ 35 * 1.2 = 42，因此未超过淘汰阈值）*
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）
    - **候选人期望:** 简历中未提供
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    3. 协调跨职能团队推进AI产品的开发与落地  
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    1. 曾主导“文稿在线智能文档分析系统”项目，基于大型模型构建文档分析系统，设计多任务协作流程，验证AI技术可行性，并通过Prompt/API模式进行效果验证，体现了AI产品功能设计与技术可行性判断能力。（证据：项目职责一、二、三）  
    2. 在“配电网无人机智能巡检系统”项目中，制定从0到1的产品路线图，明确技术迭代路径（如YOLO模型应用、缺陷识别策略设计），并推动模型代换与系统优化，体现出清晰的产品迭代与优先级管理能力。（证据：项目职责二、三）  
    3. 多次协调跨职能团队推进产品开发，如在珠海恒信德盛主导项目申报、API对接、产品集成，推动技术落地，展现出良好的团队协作与资源整合能力。（证据：珠海恒信德盛项目职责五）  
    4. 在多个项目中提出商业化策略，如SaaS+租赁模式、标准化输出、行业标杆打造，成功实现项目收益提升与成本节约，体现出市场定位与竞争策略制定能力。（证据：文稿在线项目职责六、配电网项目职责四）

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**  
    - 多个AI产品从0到1的完整落地经验，涵盖文档分析、无人机巡检等AI应用场景  
    - 具备PRD撰写、产品路线图规划、跨团队协作、商业化策略制定等核心能力  
    - 拥有PMP、NPDP认证，具备系统化产品与项目管理方法论  

- **风险与不足:**  
    - 年龄38岁，略超JD要求的35岁上限，属于潜在风险项  
    - 虽然简历中提及AI模型应用（如YOLO、Prompt工程、LangChain等），但未明确展示对AI/机器学习基本原理的理论理解  
    - 缺乏Axure/Figma等原型设计工具的直接证据（但具备产品设计能力）

**4. 初筛结论**

- **淘汰条件：** 未满足  
- **通过条件：** 满足

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历满足要求，虽然年龄略超（38岁）但未超过淘汰阈值（42岁），属于潜在风险项；核心职责匹配度高，具备AI产品设计、技术可行性判断、路线图规划、跨团队协作、市场策略制定等全方位能力，符合岗位要求。建议在后续面试中重点考察其AI基础理论理解及原型设计能力。