------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾宪涛

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科（已毕业），MBA在读
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 42岁
    - **匹配结果:** 不匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪
    - **候选人期望:** 15-20K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - 曾主导“Zigbee3.0全屋智能”项目，负责PRD文档起草、需求管理与产品迭代优化，体现产品路线图规划与迭代管理能力。
    - 在“中国移动空调智能运维项目”中涉及数据建模、软硬件设计，显示一定的技术可行性判断经验。
    - 在多个项目中协调跨职能团队（如UI设计、开发、测试等），具备跨团队协作能力。
    - 项目中涉及用户需求调研、竞品分析、市场分析等职责，体现市场定位与策略制定能力。
    - 然而，简历中未明确提及AI/机器学习相关产品经验，缺乏与AI产品直接相关的技术背景描述。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
    - 具备丰富的产品经理经验，涵盖多个智能产品项目，如全屋智能、智能运维等。
    - 有PRD撰写、产品迭代管理、跨团队协作等硬技能的实操经验。
    - 拥有多个产品管理类认证（如ACP项目管理认证、产品管理认证等）。

- **风险与不足:**
    - 年龄超过岗位要求上限（42岁 > 35岁 × 1.2 = 42岁），但刚好等于阈值上限，仍判定为“不匹配”。
    - 简历中未体现AI/机器学习相关的具体产品经验或技术理解，缺乏与AI产品经理岗位直接匹配的背景。
    - 缺乏原型设计工具（如Axure/Figma）使用经验的明确说明。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"。（年龄）
    2. ❌ 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. ✅ 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人年龄42岁，超出岗位要求的35岁以下（硬性门槛），因此直接淘汰。尽管其在产品管理、项目经验和核心技能方面与岗位具备中等匹配度，但不符合基本年龄要求。