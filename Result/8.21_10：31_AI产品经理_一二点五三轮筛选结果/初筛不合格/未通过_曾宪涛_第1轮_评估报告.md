------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾宪涛

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科（已毕业），在读MBA
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 42岁
    - **匹配结果:** 不匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪
    - **候选人期望:** 15-20K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - 曾主导多个APP产品（如医养管家APP、护理助手APP、BiuAPP）的设计与迭代，涉及用户调研、PRD撰写、原型图设计、用户体验优化等，**具备产品功能设计与用户体验优化的经验**。
    - 在多个项目中负责制定产品路线图并推动产品迭代（如Zigbee3.0全屋智能项目、AVOT云台监控系统等），**具备路线图规划与迭代管理能力**。
    - 多次担任产品经理并协调开发、设计、测试等团队推进产品上线（如中国联通空管智能项目、公安部大数据项目），**具备跨职能协作经验**。
    - 在医疗、智能家居、资产管理等产品中进行市场调研、竞品分析、用户需求整理，**具备市场定位与策略制定能力**。

**匹配度结论:** 高

**3. 综合评估**

- **优势:**
    1. 拥有丰富的产品经理经验，覆盖医疗、智能硬件、大数据平台等多个领域。
    2. 具备完整的产品生命周期管理经验，包括需求调研、PRD撰写、原型设计、版本迭代、用户反馈收集等。
    3. 有多个从0到1的产品落地案例，具备良好的市场洞察与策略制定能力。

- **风险与不足:**
    1. 年龄为42岁，超过JD要求的35岁上限，且超出幅度达20%以上，**直接判定为“不匹配”**。
    2. 虽具备AI产品相关经验的潜力（如公安大数据项目、空管智能项目），但未明确展示AI/机器学习原理理解、模型部署流程等JD要求的核心技能。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人年龄为42岁，超过JD要求的35岁上限，且超出幅度达20%以上，**直接判定为“不匹配”**，因此不符合进入下一轮面试的基本资格。虽然其在核心职责方面匹配度较高，但仍因硬性门槛不符被淘汰。