------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾杰  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上  
    - **候选人情况:** 硕士（计算机技术专业）  
    - **匹配结果:** 匹配  

- **年龄要求:**
    - **JD 要求:** 35岁以下  
    - **候选人情况:** 38岁  
    - **匹配结果:** 潜在风险  
        - 计算：35 * 1.2 = 42，候选人38 ≤ 42，且大于35，故标记为“潜在风险”  

- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary = 20K）  
    - **候选人期望:** 简历未提供  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    3. 协调跨职能团队推进AI产品的开发与落地  
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    1. **设计AI产品功能与用户体验：**  
        - 曾主导“文稿在线”项目，涉及AI实现文档自动化处理，优化UI界面，简化操作步骤，设计Prompt模块，验证AI可行性。  
        - 在“配电网无人机智能巡检系统”中，明确AI与业务融合逻辑，设计标准化AI检测流程，提升故障识别率。  
    2. **制定产品路线图与迭代管理：**  
        - 在多个项目中体现路线图规划能力，如无人机巡检项目分阶段推进，设定标准化流程与认证标准，制定长期升级路径（如引入多Agent技术）。  
        - 文稿在线项目有明确的下一阶段迭代计划，包括提升文档编辑精度、商业化验证等。  
    3. **协调跨职能团队：**  
        - 在多个项目中均有与研发、工程、客户部门协作的描述，如“对接主要合作伙伴实现API搭建”、“组织部门间需求评审”、“推动API模块构建测试案例集”等。  
    4. **市场定位与竞争策略：**  
        - 在“智网无人机智能操作系统4.0”项目中，根据产品形态、目标用户、竞品分析调整产品策略。  
        - 文稿在线项目提出SaaS+租赁的商业化路径，无人机巡检项目形成行业解决方案并被纳入地方标准。  

- **匹配度结论:** 高  

**3. 综合评估**

- **优势:**
    - 拥有多个AI产品从0到1的落地经验，涵盖文档自动化处理、无人机巡检等AI应用场景。
    - 具备完整的产品生命周期管理经验，涵盖需求分析、PRD撰写、技术可行性判断、路线图规划、迭代管理、商业化路径设计。
    - 技术背景扎实，掌握Python、API设计、Prompt工程、LangChain开发等AI产品所需硬技能，且具备PMP与NPDP认证。

- **风险与不足:**
    - 年龄超过JD要求上限35岁，但未超出20%阈值（35*1.2=42），标记为“潜在风险”。

**4. 初筛结论**

- **淘汰条件：** 无  
- **通过条件：** 满足  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历符合要求，薪资未提供视为匹配，年龄38岁略高于JD要求但未超过20%阈值，标记为“潜在风险”。其在AI产品设计、路线图规划、跨职能协作、市场策略制定等方面均有充分经验与项目证据，核心职责匹配度为“高”，符合岗位核心要求。