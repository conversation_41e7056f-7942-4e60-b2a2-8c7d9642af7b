------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：曾杰  
**年龄**：38岁  
**工作经验**：10年以上  
**学历**：硕士  
**当前职位**：产品经理  
**期望薪资**：未提及  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 本科
    - **候选人情况:** 硕士
    - **匹配结果:** 匹配

- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 38岁
    - **匹配结果:** 匹配

- **薪资范围:**
    - **JD 要求:** 14-20K
    - **候选人期望:** 未提及
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 负责AI技术在户外产品中的应用规划，推动智能户外装备的创新与发展
    - 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平
    - 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行

- **简历证据分析:**
    - 曾主导“配电网无人机智能巡检系统”项目，设计AI辅助巡检解决方案，融合无人机采集与AI识别技术，提升电力巡检效率与智能化水平（证据：“设计AI辅助巡检的应用解决方案，结合现有机器人检验标准...应急响应类故障识别率达93%”）。
    - 在“文稿在线项目组”中验证AI技术可行性，涉及Prompt工程、RAG与Agent技术应用，展示了对AI技术的理解与转化能力（证据：“设计上云条件接入SDK，验证AI技术的可行性”、“引入多Agent技术机制，应对复杂业务场景”）。
    - 在多个项目中展示全流程推动能力，如“智网无人机智能操作系统4.0”项目中实现需求确认到交付仅3个月，具备跨部门协作与推动经验（证据：“标准化全流程管理（需求分析、上线运维）”、“组织编写产品-研发手册，减少沟通成本”）。
    - 有明确的用户需求理解与产品策略调整经验，如“根据产品形态、目标用户群体、产品性能、竞品分析、市场需求，调整产品策略”，但**缺乏明确的“户外产品”或“户外用户”相关经验**。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
    - 多年AI产品经验，涉及无人机巡检、文档自动化处理等AI应用场景，符合“AI技术应用”要求。
    - 多个项目体现全流程推动能力，包括需求管理、开发协同、上线运维等，满足“项目全流程推动”要求。
    - 有明确的AI技术探索与应用经验，如Prompt工程、RAG、Agent等，符合“AI技术应用”技能要求。

- **风险与不足:**
    - 尽管具备AI产品经验，但**未体现与“户外产品”相关的背景或用户理解**，不符合JD第一条核心职责。
    - 年龄为38岁，虽然JD未设年龄限制，但在AI产品经理这类对技术敏感度和敏捷响应要求较高的岗位中，属于偏高年龄。
    - 期望薪资未提及，需在后续环节确认是否匹配JD薪资范围。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 硬性门槛全部匹配，核心职责匹配度为“中”。虽然候选人具备AI产品经验与全流程推动能力，但**缺乏明确的“户外产品”背景**，建议在后续面试中重点考察其能否理解并转化户外用户需求。