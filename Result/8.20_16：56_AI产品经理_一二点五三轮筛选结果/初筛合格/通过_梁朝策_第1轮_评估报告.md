------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 梁朝策

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及  
    - **候选人情况:** 广东科技学院 本科 软件工程（2020-2022）  
    - **匹配结果:** 匹配  
- **年龄要求:**
    - **JD 要求:** 未提及  
    - **候选人情况:** 29岁  
    - **匹配结果:** 匹配  
- **薪资范围:**
    - **JD 要求:** 未提及  
    - **候选人期望:** 23-30K  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **第一条职责匹配：**  
      候选人主导了多个AI Agent产品的0-1设计与落地，如“AI Agent工作流的0-1规划设计”、“构思ASR+LLM+TTS全流程智能客户/数字人交互方案”、“设施设备管理云平台项目中大模型设备绩效及健康策略设计”，并有多个实际项目交付成果（如扶梯不安全行为监测平台、设施设备管理系统等），体现出完整的产品生命周期管理能力。  
      **证据原文：**  
      > “主导AI Agent工作流的0-1规划设计，确保其满足客户应用场景的需求，提升产品的适用性和灵活性。”  
      > “构思ASR+LLM+TTS全流程智能客户/数字人交互方案，提升客户人机交互的智能化水平。”  
      > “负责DeepSeek大模型的训练与调优，确保其符合客户内部工作流程，优化数据处理效率。”

    - **第二条职责匹配：**  
      候选人具备将算法能力转化为产品功能的丰富经验，例如“设计AI Agent产品的流程，协调团队资源，制定产品策略”，“与算法工程师紧密协作，进行模型训练参数调优、数据清洗及精度提升”，“采用XGBOOST算法优化模型的训练效果”，体现出在技术与业务之间进行有效转化的能力。  
      **证据原文：**  
      > “与算法工程师紧密协作，进行模型训练参数调优、数据清洗及精度提升，确保预测结果的准确性和可靠性。”  
      > “采用XGBOOST算法，优化模型的训练效果，提高项目的预测能力。”

    - **第三条职责匹配：**  
      候选人在多个项目中涉及数据驱动优化，如“提升数据处理效率”、“提升公共场所的安全监测效率”、“推动设备管理的云端化，优化资源配置”，并在项目中应用RAG知识库、大模型策略等，说明其具备通过数据持续优化产品体验的能力。  
      **证据原文：**  
      > “负责DeepSeek大模型的训练与调优，确保其符合客户内部工作流程，优化数据处理效率。”  
      > “推动设备管理的云端化，优化资源配置，提高客户运营效率。”

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    1. 具备完整的AI Agent产品从0到1的设计与落地经验，覆盖规划、开发、交付全流程。
    2. 有多个大模型调优、Prompt策略、RAG知识库应用的实际项目经验，技术理解深入。
    3. 兼具技术背景（AI、云架构）与产品管理能力，能够有效衔接技术与业务。

- **风险与不足:**
    - 无“不匹配”项。
    - 无“潜在风险”项。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。（未触发）
    2. 核心职责匹配度 评估结果为 "**低**"。（未触发）

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。（满足）
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。（满足）

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人完全满足岗位的硬性门槛要求（学历、年龄、薪资），且在核心职责方面展现出高度匹配的能力，包括AI Agent产品全周期管理、算法与业务融合、数据驱动优化等。具备多个实际AI产品设计与落地经验，符合AI产品经理岗位的核心能力要求。