------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 杨英  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及  
    - **候选人情况:** 本科，计算机科学与技术专业  
    - **匹配结果:** 匹配  
- **年龄要求:**
    - **JD 要求:** 未提及  
    - **候选人情况:** 33岁  
    - **匹配结果:** 匹配  
- **薪资范围:**
    - **JD 要求:** 未提及  
    - **候选人期望:** 15-18K  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **第一条职责（全周期产品落地）：**  
      候选人主导过多个AI产品落地项目，包括“AI语音交互系统”、“多语言智能适配系统”、“酒店服务机器人系统”、“AI OCR登记系统”等，具备完整的AI产品从0到1的落地经验。其主导的“AI语音交互系统”中，采用端云协同架构降低硬件成本，并实现多语言自动切换，具有明确的技术落地与产品设计能力。  
      **证据：**  
      > “设计端云协同架构，硬件成本降低60%，支持17种语言自动切换，运营效率提升1700%。”  
      > “AI语音交互系统与多语言智能适配...技术方案实现运营效率提升1700%。”

    - **第二条职责（技术与业务转化）：**  
      候选人具备技术背景（计算机科学与技术专业），并在多个项目中担任技术与业务之间的桥梁角色。例如在“中国联通增值业务计费系统”项目中，主导从各省分散系统整合为全国统一平台，协调多方技术与业务需求。  
      **证据：**  
      > “精通技术架构与开发流程，擅长技术团队协同”  
      > “梳理6大产品线、20+省分异构计费规则，识别重复建设、系统孤岛等关键痛点。”

    - **第三条职责（构建评估体系）：**  
      候选人在多个项目中通过数据驱动优化模型表现与用户体验。例如在“酒店服务机器人系统”中，构建对话压缩机制提升信息密度，设计上下文感知系统提升对话准确率；在“NFT交易平台”中通过数据优化实现日均交易量增长。  
      **证据：**  
      > “构建对话压缩，实现大模型输出内容50%以上的信息密度提升。”  
      > “构建动态状态机实现FAQ回复（准确率95%）与开放域对话的无缝切换。”  
      > “用户平均操作时长从72s降至8s，错误率降低94%。”

- **匹配度结论:** 高  

**3. 综合评估**

- **优势:**  
    - 具备扎实的AI技术背景（计算机科学与技术专业），主导过多个AI产品从0到1的落地，涵盖语音识别、OCR、大模型、NLP等核心技术。
    - 拥有在技术与业务之间架设桥梁的丰富经验，能够将算法能力转化为实际产品功能。
    - 多个项目中通过数据驱动方式优化模型表现与用户体验，具备构建AI产品评估体系的能力。

- **风险与不足:**  
    - 无硬性门槛“潜在风险”或“不匹配”项。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人无任何硬性门槛不匹配项，且在核心职责方面具备高度匹配的项目经验与能力，能够主导AI产品全周期落地、实现技术与业务的转化，并通过数据驱动优化模型表现与用户体验。