------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 吴兴强  

---

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 未提供具体学历要求  
    - **候选人情况:** 本科（电子信息工程）  
    - **匹配结果:** 匹配  

- **年龄要求:**  
    - **JD 要求:** 未提供具体年龄要求  
    - **候选人情况:** 28岁  
    - **匹配结果:** 匹配  

- **薪资范围:**  
    - **JD 要求:** 未提供薪资范围  
    - **候选人期望:** 未提供期望薪资  
    - **匹配结果:** 匹配  

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
    1. 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。  
    2. 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。  
    3. 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。  

- **简历证据分析:**  
    1. **Agent框架设计能力：** 简历中未明确提及设计或实现AI Agent框架的经验，也未展示对ReAct、Planning、Tool Use等机制的理解与应用。因此，**缺乏直接证据**支持第一条职责要求。  
    2. **RAG系统开发与集成：** 简历中详细描述了主导RAG全链路架构设计与调优经验，包括混合检索系统、多路召回融合算法、动态排序引擎等，具备较强RAG工程能力。这与第二条职责中关于RAG实现的要求高度匹配。  
    3. **多模型API接入与管理：** 简历中提及使用GPT-4、bge-reranker-large等模型，并基于MindIE RC3框架重构推理服务，但未明确说明对接多个大模型API并统一管理的经验。因此，**缺乏直接证据**支持第三条职责要求。  

- **匹配度结论:** 中  

---

**3. 综合评估**

- **优势:**  
    - 具备扎实的RAG系统设计与调优经验，涵盖语义解析、召回、排序等完整链路，技术深度较强。  
    - 有大模型训练与部署落地经验，熟悉SFT、LoRA、混合并行、量化优化等关键技术，工程能力突出。  

- **风险与不足:**  
    - 未展示Agent框架设计与实现经验，缺乏对ReAct、工具调用等关键机制的理解和实践。  
    - 未明确展示对接多个大模型API并统一管理层的实现经验。  
    - 应聘岗位为AI全栈工程师，但简历中未体现后端服务（如FastAPI/Flask）开发经验或异步编程能力。  

---

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  

- **通过条件：**  
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 硬性门槛全部匹配，核心职责匹配度为“中”，未出现“不匹配”项。尽管在Agent框架设计与多模型API管理层方面缺乏直接证据，但具备较强的大模型工程化能力和RAG系统开发经验，具备进一步评估的潜力。