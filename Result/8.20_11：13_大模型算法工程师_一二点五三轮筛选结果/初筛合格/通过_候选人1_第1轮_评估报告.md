------



**简历初筛评估报告**

**应聘岗位:** 大模型算法工程师  
**候选人:** 谢宝正

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 郑州科技学院 计算机科学与技术 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 简历未提供年龄
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·月结（MaxSalary=20K）
    - **候选人期望:** 简历未提供期望薪资
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 负责大规模语言模型的训练、微调与优化，提升模型性能和泛化能力，支撑业务场景落地
    - 研究并应用模型压缩、量化、蒸馏等关键技术，降低模型部署成本，提升推理效率
    - 实现模型推理加速与部署，提升服务性能，确保模型在生产环境的稳定运行

- **简历证据分析:**
    1. **关于大规模语言模型训练与微调**：
        - 简历明确提到使用 Qwen、ChatGLM4 等大模型，并具备 LoRA、P-Tuning、全参微调等混合微调经验（见“技能特长”与“项目经验”部分）。
        - 在“自媒体电商智能问答客服”项目中，基于 Qwen 构建垂域知识增强框架，通过 LoRA 微调实现精准检索，具备实际训练与调优经验。
    2. **关于模型压缩与推理优化**：
        - 简历中未直接提及模型压缩、量化或蒸馏技术的具体应用。
        - 虽然在“智能卖货主播系统”中提到优化 Wav2Lip 推理流程，但该优化属于语音生成与渲染优化，与模型压缩无关。
    3. **关于模型部署与服务化**：
        - 多个项目中使用 FastAPI、Docker、RAG、TTS、ASR 等技术实现模型部署与服务化（如“智能卖货主播系统”、“自媒体电商智能问答客服”）。
        - 简历中提到使用 Qwen、Qwen-VL 等模型构建系统，并部署至生产环境，具备一定服务化能力。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
    1. 具备扎实的大模型微调与工程化经验，掌握 LoRA、RAG、TTS、ASR 等关键技术。
    2. 多个实际项目落地经验，涵盖电商、客服、数字人等业务场景，具备模型部署与服务化能力。

- **风险与不足:**
    1. 简历中未提供模型压缩、量化或蒸馏等关键技术的应用经验，与 JD 中“模型压缩与量化技术”要求存在差距。
    2. 年龄信息缺失，若后续补充信息后超出 JD 要求（35岁以下）的 20%，将构成潜在风险。
    3. 期望薪资信息未提供，若后续提供超出薪资范围上限 20%，也将构成潜在风险。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 无硬性门槛不匹配项。
    2. ✅ 核心职责匹配度为“中”，未达“低”标准。

- **通过条件：**
    1. ✅ 所有硬性门槛均匹配。
    2. ✅ 核心职责匹配度为“中”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历符合要求，年龄与薪资信息虽未提供，但暂不构成不匹配。在大模型训练、微调与部署方面具备丰富经验，符合岗位核心职责要求，具备进入下一轮面试的资格。需在后续面试中进一步确认年龄与薪资信息，并补充模型压缩与量化相关经验的评估。