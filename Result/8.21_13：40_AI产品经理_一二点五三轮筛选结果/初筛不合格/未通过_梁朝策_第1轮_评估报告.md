------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：梁朝策    
**年龄**：29岁 

**工作经验**：8年产品经理经验，涉及AI产品、工业互联网、低代码平台等领域  

**学历**：本科（广东科技学院，软件工程）  

**当前职位**：产品经理  

**期望薪资**：23-30K  

---

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（年收入上限约260K）
    - **候选人期望:** 23-30K（年收入上限约360K）
    - **匹配结果:** 不匹配  
        **理由：** 候选人期望薪资上限（360K）> JD薪资上限（260K）*1.2 = 312K

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    1. 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    2. 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    3. 协调跨职能团队推进AI产品的开发与落地  
    4. 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    1. **设计和定义AI产品功能与用户体验**  
        - 候选人主导了多个AI产品设计，包括“AI Agent工作流的0-1规划设计”、“ASR+LLM+TTS全流程交互方案”、“扶梯AI不安全行为智能监测平台”，均涉及用户体验设计与技术可行性评估。  
        - 使用了YOLO、RAGFlow、Coze、FastGPT等技术工具，具备AI产品功能设计经验。  
        - **证据：** “主导AI Agent工作流的0-1规划设计，确保其满足客户应用场景的需求，提升产品的适用性和灵活性。”

    2. **制定产品路线图与优先级，推动迭代优化**  
        - 曾主导“设备管理云平台”、“设施设备管理系统”、“扶梯AI监测平台”等多个产品从0到1落地，并持续优化迭代。  
        - **证据：** “负责制定产品策略，确保项目按时交付，满足客户需求。”、“推动产品市场推广。”

    3. **协调跨职能团队推进产品开发与落地**  
        - 多次担任产品Owner，协调资源推进项目，涉及算法、开发、硬件选型等多个职能团队。  
        - **证据：** “协调团队资源，制定产品策略，确保项目按时交付，满足客户需求。”

    4. **制定市场定位与竞争策略**  
        - 多个产品实现商业化落地，如“扶梯AI监测平台”应用于多个公共场所，“设备管理云平台”服务数十租户，体现市场定位与竞争能力。  
        - **证据：** “成功转化3个项目，应用于商场、医院、高铁站”、“服务10个领先客户，完成50个终端选型认证”。

- **匹配度结论:** 高

---

**3. 综合评估**

- **优势:**
    - 拥有丰富的AI产品设计经验，涵盖从需求分析到商业化落地的全流程
    - 具备PMP认证、敏捷方法、CMMI 5流程等项目管理能力
    - 多个AI产品成功落地并实现商业化成果，市场敏感度高

- **风险与不足:**
    - **薪资要求远高于JD上限（360K vs 260K）**，超出20%阈值，构成“不匹配”

---

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "**不匹配**" 项。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 薪资要求超出JD上限20%以上（360K > 260K * 1.2），构成硬性不匹配，故淘汰。尽管核心职责匹配度为“高”，但仍不符合通过条件。