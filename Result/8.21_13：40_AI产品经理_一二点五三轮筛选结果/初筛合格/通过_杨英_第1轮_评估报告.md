------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人**：杨英  
**年龄**：33岁  

**工作经验**：13年

 **学历**：本科（计算机科学与技术）

**当前职位**：软件产品经理

**期望薪资**：15-18K  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 33岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（MaxSalary=20K）
    - **候选人期望:** 15-18K（未明确是否含13薪）
    - **匹配结果:** 匹配（假设15-18K不含13薪，按18K×1.2=21.6K > 18K）

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - **职责1：设计和定义AI产品功能与用户体验**
        - 简历中明确描述主导AI语音交互系统、OCR、NLP等技术落地，商业化成功率100%。
        - 在“AI智能化酒店服务机器人系统开发项目”中，主导系统架构设计及跨部门协同开发，设计语音+大模型双引擎架构，响应延迟<800ms。
        - 在“酒店客户自助登记系统”中，集成AI OCR文本识别与人脸比对算法，操作时长从72秒降至8秒，错误率降低94%。
    - **职责2：制定产品路线图与优先级**
        - 在“联通沃家游戏大厅用户增长项目”中，重构页面架构，提升DAU 65%，新用户转化率提升51%。
        - 主导“AI翻译项目”，重构翻译流程，提升版本迭代速度3倍。
        - 在多个项目中体现产品规划与迭代能力，如数字藏品平台从0到1搭建、酒店机器人系统三阶段迭代等。
    - **职责3：协调跨职能团队推进AI产品开发落地**
        - 在“酒店服务机器人系统”中，带领团队完成系统架构设计，并协调跨部门开发。
        - 在“腾讯云游戏接入项目”中，协调腾讯与辽宁联通，构建统一账户体系，实现跨端权益同步。
        - 在“小沃数字藏品平台”中，协调产品、研发、运营团队完成产品上线与迭代。
    - **职责4：制定AI产品的市场定位与竞争策略**
        - 在“数字藏品平台”项目中，通过竞品分析找到产品差异化优势，最终达成行业Top 3市占率。
        - 在“腾讯云游戏接入项目”中，设计差异化定价策略，提升复购率进入行业前20%。
        - 多次在项目中强调市场洞察与差异化竞争策略，如AI翻译项目提升准确率37%，OCR系统提升用户操作效率等。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    1. 拥有扎实的AI技术背景（计算机科学与技术），并主导过多个AI产品从0到1的落地，商业化成功率100%。
    2. 在AI语音交互、OCR、NLP、图像识别、多模态交互等方面有深入实践，并具备产品路线图规划与市场策略制定能力。

- **风险与不足:**
    - 无硬性门槛不匹配项。
    - 期望薪资未明确是否含13薪，但当前薪资15-18K在JD薪资范围上限20K以内，符合匹配条件。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中无任何一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**高**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历为本科，符合JD要求；年龄33岁，在35岁以下范围内；期望薪资15-18K，在JD薪资范围15-20K·13薪内，匹配。在核心职责评估中，候选人具备完整的AI产品设计、路线图制定、跨团队协作及市场策略制定能力，且拥有多个从0到1的AI产品落地经验，商业化成果显著，匹配度为“高”。