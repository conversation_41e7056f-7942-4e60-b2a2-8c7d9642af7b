------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 杜嘉琪

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 南京航空航天大学 计算机科学与技术专业 学士学位
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 出生日期为1994年2月，当前年份2025年，年龄为31岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（年薪上限约为260K）
    - **候选人期望:** 简历中未提供明确的期望薪资
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1（设计AI产品功能与用户体验）：** 简历中未提及任何与AI产品设计、用户体验研究、产品需求分析或用户调研相关的职责或项目经验。候选人主要工作集中在后端开发、部署和测试脚本编写，缺乏产品经理视角的用户需求分析和产品定义经验。
    - **职责2（制定产品路线图与迭代管理）：** 简历中未提及任何与产品路线图制定、迭代管理、优先级排序相关的内容。候选人参与的是功能模块开发，未体现产品规划能力。
    - **职责3（协调跨职能团队）：** 简历中提及“参与华为云平台的开发工作”，但并未说明其在跨职能团队中的角色或协调职责，仅描述了开发和测试工作，缺乏项目管理或团队协调的证据。
    - **职责4（制定市场定位与竞争策略）：** 简历中未提及任何市场分析、竞争策略、商业模式设计或产品定位相关内容，缺乏与该职责相关的经验。

- **匹配度结论:** 低

**3. 综合评估**

- **优势:**  
    - 拥有丰富的后端开发经验（Java、Python），熟悉云平台（华为云、腾讯云）及API网关部署流程。  
    - 技术栈全面，掌握大量DevOps工具、微服务架构和自动化测试技术，具备良好的技术背景。

- **风险与不足:**  
    - 简历中未体现任何产品经理相关的职责或经验，包括产品定义、需求文档撰写、路线图规划、用户调研、市场策略等。  
    - 虽然技术能力强，但岗位JD强调“AI产品经理”角色，需具备产品思维与市场洞察力，而非纯技术开发能力。  
    - 缺乏AI/机器学习相关知识、产品原型设计工具（Axure/Figma）、PRD撰写、市场定位制定等JD中明确要求的核心技能。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 硬性门槛审查 中有任意一项结果为 "不匹配"。  
    2. ✅ 核心职责匹配度 评估结果为 "低"。

- **通过条件：**
    1. ❌ 硬性门槛审查 结果中**没有** "不匹配" 项。  
    2. ❌ 核心职责匹配度 评估结果为 "中" 或 "高"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人通过了所有硬性门槛审查，但核心职责匹配度评估为“低”。简历中未体现任何与AI产品经理岗位相关的职责经验，如产品定义、用户调研、路线图规划、市场策略制定等关键能力，无法胜任该岗位的核心要求。