------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾宪涛

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科（中国人民解放军陆军军医大学），在读MBA（珠海市职业技术学校：大专）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 42岁
    - **匹配结果:** 不匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪
    - **候选人期望:** 15-20K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - **职责1（AI产品设计与用户体验）：** 候选人在“Zigbee3.0全屋智能”项目中负责编写PRD文档、需求管理和用户体验优化，具备产品功能定义和用户体验设计的经验。
    - **职责2（产品路线图与迭代）：** 在多个项目中，候选人负责产品规划、迭代方向制定、需求优先级排序等工作，例如“Zigbee3.0全屋智能”项目中关注行业动态并进行深度迭代。
    - **职责3（跨职能团队协调）：** 候选人曾担任项目经理（如中国移动空调智能运维项目、公安部大数据智能化项目），有协调多方团队推进项目的经验。
    - **职责4（市场定位与竞争策略）：** 简历中提到关注行业商业模式、竞争优势，并进行产品市场分析和竞品分析，但缺乏具体AI产品市场策略的详细描述。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
    - 多年产品经理经验，涵盖多个行业（医疗、智能家居、快递、公安等）
    - 具备PRD文档撰写、产品路线图规划、跨部门协作等核心能力
    - 拥有ACP项目管理认证和多个产品管理相关证书
- **风险与不足:**
    - 年龄超出JD要求上限（42岁 vs 35岁以下），且超出阈值20%（35×1.2=42），**直接判定为“不匹配”**
    - 虽然具备产品设计、迭代、跨团队协作等经验，但未明确展示AI/机器学习原理理解、技术可行性判断等AI产品经理专属能力
    - 缺乏明确的AI产品商业化落地经验说明

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人年龄为42岁，远高于JD要求的35岁以下，且超出阈值（35×1.2=42），**根据硬性门槛审查规则，直接判定为“不匹配”**。尽管其在核心职责匹配度方面达到“中”等级，但因年龄硬性不匹配，仍被淘汰。