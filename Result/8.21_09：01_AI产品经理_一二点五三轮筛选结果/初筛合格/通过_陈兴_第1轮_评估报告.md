------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 陈兴

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 23岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪
    - **候选人期望:** 10-15K
    - **匹配结果:** 匹配（候选人期望薪资低于JD薪资上限）

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1（设计和定义AI产品功能与用户体验）：**  
      简历中明确提到“负责平台迭代优化，通过收集用户需求与反馈，优化功能及流程，提升用户体验”，并在多个项目中描述了“设计编写prompt并进行效果测试”、“设计内部知识库问答功能，包括框架、交互逻辑及原型”等内容，体现了其在AI产品功能设计和用户体验优化方面的实际经验。  
    - **职责2（制定产品路线图与优先级）：**  
      简历中提到“制定推进规划及里程碑计划，推动平台快速上线”，并在多个项目中体现路线图规划和迭代管理能力，如“负责平台版本迭代”、“持续优化平台性能”等，符合该职责要求。  
    - **职责3（协调跨职能团队推进产品开发）：**  
      简历中提到“构建‘模型选型-能力强化-数据支撑’的完整AI框架”，涉及模型选型、数据支撑等跨职能协作内容，具备协调技术与业务推进产品落地的经验。  
    - **职责4（制定市场定位与竞争策略）：**  
      简历中未明确提及市场定位、竞争策略或商业化成果相关内容。仅提到功能设计与技术落地，缺乏对市场策略层面的描述，是该候选人相对较弱的一环。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
    - 拥有AI产品经理相关工作经验，且在多个AI平台项目中担任主导角色。
    - 具备AI产品设计、迭代优化、跨职能协作等核心职责的实践经验。
    - 熟悉LLM、RAG、fewshot等AI技术，能够撰写prompt并进行效果测试，符合岗位对技术可行性和产品文档能力的要求。

- **风险与不足:**
    - 薪资期望低于JD薪资范围上限，但仍在合理区间内，未构成淘汰项。
    - 缺乏市场定位与竞争策略制定方面的明确经验，可能影响产品商业化能力的评估。
    - 工作经验仅1年，虽有项目经验但整体经验尚浅，需在后续面试中进一步考察其策略制定与行业理解能力。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。（未触发）  
    2. 核心职责匹配度 评估结果为 "**低**"。（未触发）  
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。（满足）  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。（满足）  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均符合硬性门槛要求，且在AI产品设计、功能定义、路线图规划与跨职能协作方面具备明确的项目经验，符合岗位核心职责要求。尽管市场策略与商业化经验较弱，但整体匹配度为中等，具备进入下一轮面试的资格。