------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 杨英  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及  
    - **候选人情况:** 本科，黄冈师范学院，计算机科学与技术专业  
    - **匹配结果:** 匹配  
- **年龄要求:**
    - **JD 要求:** 未提及  
    - **候选人情况:** 33岁  
    - **匹配结果:** 匹配  
- **薪资范围:**
    - **JD 要求:** 未提及  
    - **候选人期望:** 15-18K  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **职责1：主导AI智能体产品的全周期规划与落地**
        - 简历中明确提到主导了多个AI项目落地，如“AI语音交互系统”、“酒店服务机器人系统”、“AI OCR登记系统”等，具备从0到1的产品规划与工程落地能力。
        - 其中酒店服务机器人项目涉及端云协同架构设计、语音识别、大模型集成、多模态任务编排，具备完整产品生命周期管理经验。

    - **职责2：在技术与业务之间架设桥梁**
        - 简历中多次体现将AI技术转化为商业价值的能力，如“OCR+人脸比对提升登记效率90%”、“AI翻译系统替代人工流程”、“AI驱动游戏平台转化率提升”等。
        - 担任多个AI产品负责人，推动技术成果落地并实现显著业务增长（如用户增长3733%、营收增长1200万+）。

    - **职责3：构建AI产品评估体系**
        - 多个项目中展示了数据驱动优化能力，如“对话系统F1值0.87”、“响应延迟<800ms”、“准确率提升至92%”、“错误率降低94%”、“DAU提升65%”等。
        - 在多个产品中建立评估机制，如“意图识别模型训练”、“动态状态机设计”、“用户行为路径分析”、“A/B测试策略”等，具备系统化评估能力。

- **匹配度结论:** 高  

**3. 综合评估**

- **优势:**
    - 拥有10年技术背景与AI产品落地经验，主导5+AI产品成功商业化，用户规模超2.3亿。
    - 具备AI产品从技术理解、架构设计、工程实现到商业化闭环的全周期能力。
    - 数据驱动能力强，多个项目中体现出明确的指标优化成果和用户价值转化能力。

- **风险与不足:**
    - 无学历、年龄、薪资方面的“潜在风险”项。
    - JD中未明确要求硕士学历，候选人本科学历不影响匹配结果。
    - 无任何“不匹配”项。

**4. 初筛结论**

- **淘汰条件：**
    1. 不适用（无硬性门槛不匹配项）
    2. 不适用（核心职责匹配度为“高”）

- **通过条件：**
    1. 硬性门槛审查中无“不匹配”项 ✅
    2. 核心职责匹配度为“高” ✅

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人具备扎实的AI产品经验与技术理解能力，主导多个AI产品从0到1落地，具备全周期管理能力与数据驱动优化经验，与JD中三大核心职责高度匹配。无任何硬性门槛风险项，建议进入下一轮面试。