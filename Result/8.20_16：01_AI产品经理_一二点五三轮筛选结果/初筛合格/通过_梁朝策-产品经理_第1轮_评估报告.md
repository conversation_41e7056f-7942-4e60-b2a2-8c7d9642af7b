------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 梁朝策

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 本科（软件工程），大专（软件技术）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 23-30K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **职责1（全周期AI产品设计与落地）：**
        - 简历中明确提到“主导AI Agent工作流的0-1规划设计”，“构思ASR+LLM+TTS全流程智能客户/数字人交互方案”，并有“RAG知识库的应用和解决方案支持”经验，符合全周期AI产品设计与落地的要求。
    - **职责2（技术与业务桥梁）：**
        - 候选人曾“负责DeepSeek大模型的训练与调优，确保其符合客户内部工作流程”，并“负责客户的整体需求与产品设计”，体现出技术能力与业务需求之间的转化能力。
    - **职责3（构建评估体系、数据驱动优化）：**
        - 简历提到“大模型设备绩效及健康策略设计”、“优化数据处理效率”、“提升客户人机交互的智能化水平”，表明其具备通过数据优化模型表现和用户体验的能力。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有实际AI Agent产品从0到1的完整经历，覆盖工作流设计、模型训练、交互方案构建等关键环节。
    - 熟悉LLM、RAG、Coze、FastGPT等主流AI Agent技术栈，具备Prompt工程与模型微调能力。
    - 拥有PMP、敏捷、阿里云ACE认证等专业背景，具备良好的产品与项目管理能力。

- **风险与不足:**
    - 简历中未明确提及构建AI产品评估体系的具体方法论，但有优化模型表现和用户体验的实际经验，因此该职责匹配仍属“高”。
    - 无明显“潜在风险”项。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 ❌
    2. 核心职责匹配度 评估结果为 "**低**"。 ❌
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 ✅
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 ✅

**筛选结果：** ✅ 通过  
**筛选理由：** 所有硬性门槛均匹配，核心职责匹配度为“高”，具备AI产品经理所需的核心能力与项目经验，符合岗位要求。