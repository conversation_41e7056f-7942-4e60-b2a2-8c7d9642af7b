------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 梁朝策

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 本科（软件工程），大专（软件技术）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提及
    - **候选人情况:** 29岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提及
    - **候选人期望:** 23-30K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - 候选人主导了多个AI Agent产品的从0到1的规划与设计，如“AI Agent工作流的0-1规划设计”、“ASR+LLM+TTS全流程智能客户/数字人交互方案”、“设施设备管理云平台中RAG知识库的应用和解决方案支持”，体现出对AI智能体产品的全周期管理能力。
    - 候选人具备将算法能力转化为产品功能的经验，如“负责DeepSeek大模型的训练与调优”，“设计AI Agent产品的流程，协调团队资源”，“设计传感器与硬件选型，优化数据处理流程”，说明其具备技术与业务之间的桥梁能力。
    - 候选人多次参与模型优化与评估相关工作，如“提升模型训练效果”、“优化数据处理效率”、“提升预测结果的准确性和可靠性”，并结合RAG、TTS、ASR等技术进行产品优化，具备数据驱动优化模型表现的能力。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 拥有多个AI Agent产品的从0到1实践经验，覆盖工作流设计、模型训练、大模型调优、RAG应用等多个关键环节。
    - 具备扎实的技术背景，持有ACE阿里云认证，熟悉Prompt工程、模型微调、Agent架构等核心技能。
    - 具备完整的产品生命周期管理经验，涵盖需求分析、产品设计、项目交付、市场推广等。

- **风险与不足:**
    - 无硬性门槛“潜在风险”项。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人所有硬性门槛均匹配，且在核心职责方面具备高度匹配的产品经验与技术能力，尤其在AI Agent产品全流程管理、算法产品化、模型优化等方面表现突出。