------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 谢宝正  

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 未提供（默认无学历限制）  
    - **候选人情况:** 本科（郑州科技学院，计算机科学与技术）  
    - **匹配结果:** 匹配  

- **年龄要求:**  
    - **JD 要求:** 未提供（默认无年龄限制）  
    - **候选人情况:** 未在简历中明确提供年龄  
    - **匹配结果:** 匹配  

- **薪资范围:**  
    - **JD 要求:** 未提供（默认无薪资限制）  
    - **候选人期望:** 未在简历中提供  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
    - 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。  
    - 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。  
    - 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。  

- **简历证据分析:**  
    - **职责1：AI Agent架构设计**  
        - 简历中虽未明确提及“从零设计Agent框架”，但提到了使用 **AutoGEN、Dify、Coze** 等多智能体框架，并参与了 **MCP协议实现**（mcp-server 与 mcp-client 实现），具备一定的Agent系统构建经验。  
        - 在“自媒体电商智能问答客服”项目中，开发了**多轮对话管理模块**，支持[优惠查询-下单指导-物流追踪]全流程意图串联，表明其具备任务规划与状态管理能力。  

    - **职责2：Agent核心功能集成**  
        - 在多个项目中均有使用 **RAG技术**，如“自媒体电商智能问答客服”项目中基于Qwen实现垂域知识增强框架，结合LoRA微调与RAG技术，实现动态知识库检索。  
        - 在“智能卖货主播系统”中实现基于LLM的智能解说生成模块，涉及**function calling**（TTS、ASR、数字人渲染等模块调用），并结合FastAPI构建服务端，说明具备集成外部工具与模块调用的经验。  
        - 但未明确提及“代码执行”与“联网搜索”相关能力，这部分经验存在缺失。  

    - **职责3：多模型API统一接入与管理**  
        - 在多个项目中使用了 **Qwen、ChatGLM4、Qwen-VL、DeepSeek、CosyVoice、FunASR** 等多个大模型API，并在“自媒体电商智能问答客服”项目中实现基于Qwen的微调与RAG检索，具备模型切换与性能调优经验。  
        - 使用 **FastAPI** 构建服务接口，具备构建高性能服务的经验。  

- **匹配度结论:** 中  

**3. 综合评估**

- **优势:**  
    - 熟练掌握 **Python**，并在多个项目中使用 **FastAPI** 构建后端服务，符合岗位对异步编程与高性能API服务的要求。  
    - 具备 **RAG 技术实现经验**，在多个项目中结合垂域知识库提升模型效果。  
    - 使用 **AutoGEN、Dify、Coze** 等主流Agent框架，并参与MCP协议对接，具备Agent生态理解。  
    - 多个项目涉及 **多模型集成与调用**，具备一定的模型管理层设计能力。  

- **风险与不足:**  
    - 未提供明确的从零构建Agent框架的经验，仅使用现成框架进行集成，可能在架构设计层面经验略显不足。  
    - 缺乏“代码执行”与“联网搜索”相关项目经验，这两项是JD中明确要求的核心功能。  
    - 未明确提及 **模型切换的成本控制与性能监控机制** 的具体实现细节。  

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  

- **通过条件：**  
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 所有硬性门槛均匹配，核心职责匹配度为“中”，具备Agent框架使用经验、RAG实现能力与多模型集成经验，虽未完全覆盖所有核心功能模块，但具备较强的学习能力与项目落地经验，可进入下一轮面试。