------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 陈兴

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 23岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪（年收入上限为 20K × 13 = 260K）
    - **候选人期望:** 10-15K（月薪期望上限为15K，年收入上限为15K × 12 = 180K）
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性  
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化  
    - 协调跨职能团队推进AI产品的开发与落地  
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力  

- **简历证据分析:**
    - **职责1（功能与用户体验设计）：**  
      简历中明确描述了候选人负责多个AI平台的功能设计，如内容安全审核平台、文化内容审核平台、AI智能客服平台等，涉及prompt设计、功能优化、用户体验提升等内容，如“负责平台迭代优化，通过收集用户需求与反馈，优化功能及流程，提升用户体验”，具备明确的证据支持。

    - **职责2（制定产品路线图）：**  
      候选人在文化内容审核平台项目中“制定推进规划及里程碑计划，推动平台快速上线”，并在多个项目中持续进行功能迭代与优化，表明其具备产品路线图规划与迭代管理能力。

    - **职责3（协调跨职能团队）：**  
      简历中提到其“主导AI+平台项目”，“推动LLM模型与审核流程融合”，“搭建RAG系统”等，均涉及与技术、数据等团队的协作，虽未明确提及“跨职能团队”，但项目描述中已体现出协调多方资源推进项目落地的能力。

    - **职责4（市场定位与竞争策略）：**  
      简历中未提供直接证据表明候选人具备市场定位或竞争策略制定的经验，缺乏相关描述，属于职责匹配中的弱项。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
  - 拥有1年AI产品经理实际经验，主导多个AI平台产品设计与迭代，具备技术理解与产品落地能力。
  - 熟悉LLM、RAG、fewshot等AI技术，能结合业务场景设计产品功能，具备较强的技术可行性判断能力。
  - 具备产品需求文档撰写、用户需求调研、原型设计等能力，符合岗位核心技能要求。

- **风险与不足:**
  - 简历中未体现市场定位与竞争策略制定的相关经验，可能在战略层面存在短板。
  - 期望薪资虽匹配，但经验尚浅（仅1年），需进一步评估其综合能力是否满足岗位要求。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → 无  
    2. 核心职责匹配度 评估结果为 "**低**"。 → 未达  

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → 满足  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → 满足  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均满足JD硬性要求；在AI产品经理岗位上有实际项目经验，覆盖产品设计、迭代优化、技术落地等多个核心职责，具备较强的技术理解与产品实现能力。虽然缺乏市场定位方面的直接证据，但整体匹配度为“中”，符合进入下一轮面试的资格。