------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 曾宪涛

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 专科及以上
    - **候选人情况:** 本科（法律专业），大专（大数据技术与应用）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 35岁以下
    - **候选人情况:** 42岁
    - **匹配结果:** 不匹配
- **薪资范围:**
    - **JD 要求:** 15-20K·13薪
    - **候选人期望:** 15-20K
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性
    - 制定产品路线图与优先级，推动AI产品持续迭代与优化
    - 协调跨职能团队推进AI产品的开发与落地
    - 制定AI产品的市场定位与竞争策略，打造差异化产品竞争力

- **简历证据分析:**
    - 曾主导多个智能产品项目，如“Zigbee3.0全屋智能”、“中国移动空调智能运维项目”等，具备产品设计、PRD撰写、用户体验优化等经验，**与第一条职责部分匹配**。
    - 多次参与产品从0到1的设计、迭代与优化，如“医养管理系统”、“护理助手APP”等项目，具备产品路线图制定与迭代管理的实践经验，**与第二条职责基本匹配**。
    - 担任项目经理角色（如公安部大数据智能化项目、中国移动项目），具备跨部门协调与项目推进经验，**与第三条职责部分匹配**。
    - 在多个项目中涉及市场分析、竞品分析、用户调研等工作，但未见明确提及AI产品市场定位与竞争策略制定的直接经验，**与第四条职责匹配度较低**。

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
    - 多年B端、C端产品经验，涵盖智能硬件、医养系统、大数据平台等多个领域。
    - 具备PRD撰写、产品路线图规划、用户调研、跨部门协作等核心能力，且持有ACP项目管理认证。

- **风险与不足:**
    - 年龄超过JD要求上限（35岁）的20%，直接判定为“不匹配”。
    - 缺乏明确的AI产品相关经验描述，如技术可行性评估、AI模型理解、深度学习框架使用等。
    - 虽有产品经理经验，但未见AI产品经理相关认证（如PMP、AI产品管理培训证书等）。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人年龄为42岁，超过JD要求的“35岁以下”上限，且超出20%阈值，属于“不匹配”项，因此被淘汰。尽管其核心职责匹配度为“中”，但仍无法通过初筛。