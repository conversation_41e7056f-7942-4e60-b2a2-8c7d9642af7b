------



**简历初筛评估报告**

**应聘岗位:** AI产品经理  
**候选人:** 梁朝策

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未提及  
    - **候选人情况:** 本科（软件工程）  
    - **匹配结果:** 匹配  
- **年龄要求:**
    - **JD 要求:** 未提及  
    - **候选人情况:** 29岁  
    - **匹配结果:** 匹配  
- **薪资范围:**
    - **JD 要求:** 未提及  
    - **候选人期望:** 23-30K  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。
    - 在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。
    - 构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。

- **简历证据分析:**
    - **职责1（AI智能体产品全流程管理）：**
      简历中明确提到“主导AI Agent工作流的0-1规划设计”，并“负责AI Agent产品的流程设计”，具备从0到1的产品设计经验，符合该职责要求。
    - **职责2（技术与业务桥梁）：**
      简历显示候选人“负责大语言模型的选型与训练”，并“将算法策略应用于客户内部流程”，说明其具备将技术能力转化为业务价值的能力，有明确证据支撑。
    - **职责3（构建评估体系与数据驱动优化）：**
      简历中提到“负责DeepSeek大模型的训练与调优”，并“优化数据处理效率”，说明其具备模型调优与数据优化经验，虽未明确提及评估体系构建，但已有相关能力体现。

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**
    - 具备AI Agent产品从0到1的设计经验，主导过多个AI相关项目。
    - 熟悉LLM模型选型、训练与调优，具备Prompt工程与模型微调策略的应用经验。
    - 拥有PMP、敏捷认证及阿里云ACE认证，项目管理与云架构设计能力强。
    - 多个项目中成功将AI能力落地为业务功能，体现良好的技术与业务衔接能力。

- **风险与不足:**
    - 无硬性门槛方面的“潜在风险”或“不匹配”项。
    - 简历中对“构建AI产品评估体系”的描述较为简略，缺乏具体方法论或指标体系的详细说明。

**4. 初筛结论**

- **淘汰条件：** 无  
- **通过条件：** 全部满足  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人无任何硬性门槛不匹配项，学历、年龄、期望薪资均在合理范围内；在核心职责方面，其具备AI Agent产品全流程设计经验，能将算法能力有效转化为产品功能，并有模型调优和数据驱动优化的实操经验，整体匹配度高。