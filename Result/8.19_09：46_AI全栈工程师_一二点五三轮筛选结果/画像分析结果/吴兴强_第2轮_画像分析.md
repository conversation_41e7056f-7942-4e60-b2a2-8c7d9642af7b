# 候选人专业画像分析报告

**基本信息:**

- **姓名:** 吴兴强
- **分析日期:** 2025-04-05

**专业身份与能力层级:**

- **主要专业身份 (80-100分):** 大模型算法工程师（分布式训练与优化、SFT/Lora微调、推理部署优化）【85分】
- **重要辅助技能 (60-79分):** RAG系统开发与调优、自然语言处理（NLP）、模型工程化部署、知识图谱构建【72分】
- **边缘接触能力 (40-59分):** 数据挖掘、风控建模、推荐系统、图像生成与AI翻唱技术应用【55分】

**能力边界:**

- **擅长:** 大模型训练优化、分布式训练、SFT/Lora微调、RAG系统设计、推理部署优化、NLP模型应用
- **适合:** 医疗领域AI系统开发、模型工程化落地、知识图谱构建、数据处理与分析
- **不适合:** 计算机视觉底层算法研究、嵌入式系统开发、传统软件工程架构设计、弱电智能化系统集成

**职业发展轨迹:**

- **一致性:** 高。职业路径聚焦于算法工程与大模型应用，从数据挖掘→机器学习→深度学习→大模型演进，具备清晰的技术积累路径。
- **专业深度:** 深。在大模型领域具备从训练优化、微调策略、RAG系统到推理部署的全链路实践经验，技术栈完整。

**综合评估与建议:**

- **专业比重总分:** 85分
- **可信度:** 高（项目描述具体，包含技术栈、架构设计、性能指标等量化成果）
- **最终专业身份:** 资深大模型算法工程师（偏向医疗领域模型训练优化与RAG系统开发）