# 候选人专业画像分析报告

**基本信息:**

- **姓名:** 孙茂森
- **分析日期:** 2025-04-05

**专业身份与能力层级:**

- **主要专业身份 (80-100分):** 大模型应用开发工程师（RAG/Agent方向）【90分】
  - 证据：参与多个RAG和Agent项目开发，涉及模型微调、提示词优化、搜索模块重构、交互系统搭建等核心工作；具备Langchain、FastAPI、vLLM等核心技术栈经验；有多个大模型部署和服务化经验。
  
- **重要辅助技能 (60-79分):** 自然语言处理工程师【75分】
  - 证据：税务问答系统、金融问答系统、意图识别、Prompt工程、模型微调（ChatGLM3、Llama2、Qwen系列等）、文本分类、文本生成等NLP相关项目经验；熟悉transformers、TRL等NLP开发库。
  
- **边缘接触能力 (40-59分):** 数据挖掘与机器学习工程师【55分】
  - 证据：参与多个Kaggle竞赛（如Identifying Age-Related Conditions、重复购买预测、工业蒸汽量预测等），使用XGBoost、LightGBM、TabPFN、LSTM等算法进行建模；具备特征工程、数据清洗、模型融合等经验。

**能力边界:**

- **擅长:**
  - 大模型应用开发（RAG/Agent）
  - 模型微调与部署（Qwen、ChatGLM、Llama等）
  - Prompt工程与提示词优化
  - NLP系统开发与服务化
  - 多模态交互系统搭建（ASR+LLM+TTS）

- **适合:**
  - NLP算法开发与优化
  - 机器学习建模与特征工程
  - 大模型评测与调优
  - 搜索系统与推荐系统相关工作

- **不适合:**
  - 图像识别与计算机视觉（仅接触过微调internvl用于地方菜识别，经验有限）
  - 知识图谱构建与应用（仅提及了解，无明确项目经验）
  - 传统软件开发（非简历重点，缺乏明确证据）
  - 弱电智能化、安防、楼宇自控等硬件集成类工作（无相关关键词）

**职业发展轨迹:**

- **一致性:** 高【聚焦大模型应用开发方向】
  - 从算法实习生到多个大模型项目实践，职业路径高度聚焦在大模型应用开发（RAG/Agent/NLP）方向，技能积累具有明显连续性。

- **专业深度:** 中高【具备大模型应用开发全流程能力】
  - 从数据采集、清洗、模型微调、部署、Prompt优化、系统搭建到服务化，具备完整的大模型应用开发能力；具备多种模型架构（Qwen、Llama、ChatGLM等）的实战经验。

**综合评估与建议:**

- **专业比重总分:** 88分
- **可信度:** 高
  - 依据：简历包含多个具体项目描述，涵盖大模型训练、微调、部署、评测、Prompt优化等核心环节，技术关键词密度高，部分成果有量化数据支撑（如准确率提升、响应速度提升等）。
- **最终专业身份:** 资深大模型应用开发工程师（RAG/Agent方向）