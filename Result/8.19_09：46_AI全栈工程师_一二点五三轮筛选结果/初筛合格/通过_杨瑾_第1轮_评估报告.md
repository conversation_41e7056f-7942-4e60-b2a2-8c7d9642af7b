------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 杨瑾  

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 未明确说明  
    - **候选人情况:** 计算机专业本科（2021.03-2023.06 浙江工业大学）  
    - **匹配结果:** 匹配  
- **年龄要求:**  
    - **JD 要求:** 未提供年龄范围  
    - **候选人情况:** 1993年出生，当前年龄为 31 岁  
    - **匹配结果:** 匹配  
- **薪资范围:**  
    - **JD 要求:** 未提供薪资范围  
    - **候选人期望:** 简历未提供期望薪资  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
    1. 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。  
    2. 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。  
    3. 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。  

- **简历证据分析:**  
    1. **Agent框架与架构设计：**  
        - 简历中未提及“Agent框架设计”、“任务规划”、“记忆管理”等内容。  
        - 项目一中提到“基于Qwen2.5-32B模型进行Lora微调”，但未体现系统级架构设计或工具调用机制的实现。  
    2. **RAG、function calling、文件处理等核心能力：**  
        - 项目一中提及“文本向量化”、“检索与生成”、“图关系检索”、“query改写”等内容，说明具备一定的RAG实践经验。  
        - 但未提及“function calling”、“联网搜索”、“代码执行”等能力的实现。  
    3. **多模型API统一接入与管理：**  
        - 虽然简历中提到“熟悉OpenAI、ChatGLM-6B等大模型”，但项目经历中未体现“多模型API统一接入”、“成本控制”、“性能监控”等内容。  
        - 项目中模型部署方式为Docker，未涉及API管理层设计。  

- **匹配度结论:** 中  

**3. 综合评估**

- **优势:**  
    1. 具备RAG相关实践经验，涉及文本向量化、检索、生成、重排等关键环节。  
    2. 熟悉大模型微调（LoRA）和部署（vLLM），具备一定的工程落地能力。  

- **风险与不足:**  
    1. 缺乏Agent系统架构设计经验，未体现任务规划、工具调用等核心模块的实现。  
    2. 未展示function calling、代码执行、联网搜索等能力模块的实际经验。  
    3. 项目中模型调用和API管理部分缺乏系统级设计描述。  

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  

- **通过条件：**  
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人满足所有硬性门槛要求。在核心职责方面，虽未完全覆盖Agent架构设计和系统级功能集成，但具备RAG、大模型微调与部署经验，匹配度为“中”，符合岗位初筛标准。