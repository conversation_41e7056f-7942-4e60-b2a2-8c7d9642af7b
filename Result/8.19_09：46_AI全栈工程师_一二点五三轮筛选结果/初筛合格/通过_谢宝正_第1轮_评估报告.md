------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 谢宝正  

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 未明确指定学历要求  
    - **候选人情况:** 本科（郑州科技学院，计算机科学与技术）  
    - **匹配结果:** 匹配  

- **年龄要求:**  
    - **JD 要求:** 未提供年龄范围  
    - **候选人情况:** 未提供年龄信息  
    - **匹配结果:** 匹配  

- **薪资范围:**  
    - **JD 要求:** 未提供薪资范围  
    - **候选人期望:** 未提供期望薪资  
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
    1. 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。  
    2. 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。  
    3. 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。  

- **简历证据分析:**  
    1. **Agent框架设计与核心模块开发：**  
        - 简历中明确提到使用 AutoGEN、Dify、Coze 等多智能体框架，并有 MCP 协议实现经验，说明具备构建 Agent 框架的能力。  
        - 在“自媒体电商智能问答客服”项目中，构建了基于 RAG 的垂域知识增强框架，支持多轮对话管理模块，覆盖“任务规划”、“工具调用”等核心能力。  

    2. **RAG、function calling、系统集成：**  
        - 在“自媒体电商智能问答客服”项目中，使用 Qwen 模型构建垂域知识增强框架，通过 LoRA 微调和 RAG 技术实现知识检索，具备 RAG 实践经验。  
        - 使用 FastAPI 构建后端服务，并结合缓存机制和索引优化提升系统响应速度，说明具备系统集成与 API 服务开发能力。  

    3. **大模型API统一接入与管理层设计：**  
        - 简历中列出使用 Qwen、DeepSeek、ChatGLM4 等多个大模型，并在项目中实现模型微调与部署，具备多模型集成经验。  
        - 在“智能卖货主播系统”项目中，结合 TTS、ASR、数字人渲染等多个模块，说明具备构建统一能力生态的经验。  

- **匹配度结论:** 高  

**3. 综合评估**

- **优势:**  
    1. 具备丰富的 Agent 开发经验，熟练使用多种主流 Agent 框架，并有实际项目落地。  
    2. 熟练掌握 RAG、LoRA、FastAPI 等核心技术，能够完成从模型微调到系统集成的全流程开发。  
    3. 有多个 AI 项目经验，涵盖电商客服、直播、海报生成等场景，具备较强的工程化落地能力。  

- **风险与不足:**  
    - 未提供年龄与期望薪资信息，后续需确认是否符合岗位预算与团队结构要求。  

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  
- **通过条件：**  
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历符合本科要求，未提供年龄与薪资信息，故无“不匹配”项。其在多个项目中展现了与 JD 要求高度匹配的技术能力与架构设计经验，尤其是 Agent 构建、RAG 实践、多模型集成等方面表现突出，匹配度为“高”。后续需补充年龄与薪资信息以进一步评估潜在风险。