------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 吴兴强  

---

**1. 硬性门槛审查**

- **学历要求:**  
    - **JD 要求:** 未提供  
    - **候选人情况:** 本科（电子信息工程）  
    - **匹配结果:** 匹配  

- **年龄要求:**  
    - **JD 要求:** 未提供  
    - **候选人情况:** 28岁  
    - **匹配结果:** 匹配  

- **薪资范围:**  
    - **JD 要求:** 未提供  
    - **候选人期望:** 未提供  
    - **匹配结果:** 匹配  

---

**2. 核心职责匹配度评估**

- **JD核心职责要求:**  
    1. 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。  
    2. 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。  
    3. 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。  

- **简历证据分析:**  
    - **职责1（Agent架构设计）：** 简历中未明确提及设计过AI Agent框架或具备任务规划、记忆管理等模块的开发经验。虽然候选人具备较强的工程能力，但缺乏直接证据表明其具备从零构建Agent架构的经验。
    - **职责2（Agent核心功能集成）：** 简历中明确提及主导RAG系统开发，涵盖语义解析、多路召回、动态排序等关键环节，技术实现完整，具备较强的RAG工程经验。但未见关于function calling、代码执行等模块的直接经验描述。
    - **职责3（大模型API统一管理层）：** 简历中提及使用Qwen、GPT-4等模型进行评估和微调，具备多模型调用经验，但未说明是否设计过统一的模型接入管理层，也未提及模型切换、成本控制、性能监控等具体实现。

- **匹配度结论:** 中  

---

**3. 综合评估**

- **优势:**  
    1. 具备扎实的大模型训练与部署经验，熟悉SFT、LoRA、分布式训练、模型量化等关键技术。  
    2. 有主导RAG系统开发的实际项目经验，涵盖召回、排序、多模型融合等关键环节，具备较强的工程实现能力。  

- **风险与不足:**  
    1. 缺乏从零构建AI Agent框架的具体经验，未见任务规划、工具调用、记忆管理等核心模块的开发记录。  
    2. 未展示function calling、代码执行等Agent关键能力的实现经验。  
    3. 未明确说明是否具备统一的大模型API管理层设计经验，缺乏模型调度与监控方面的细节。  

---

**4. 初筛结论**

- **淘汰条件：**  
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。  
    2. 核心职责匹配度 评估结果为 "**低**"。  

- **通过条件：**  
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。  

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人满足所有硬性门槛要求，核心职责匹配度为“中”，具备较强的RAG系统开发经验及大模型工程能力，虽未直接展示Agent框架设计经验，但具备相关技术基础，可进入下一轮面试进一步评估其架构设计能力。