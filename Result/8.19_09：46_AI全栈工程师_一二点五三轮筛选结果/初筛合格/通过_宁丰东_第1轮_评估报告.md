------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 宁丰东  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 本科及以上  
    - **候选人情况:** 吉林大学珠海学院 本科 计算机科学与技术  
    - **匹配结果:** 匹配  
- **年龄要求:**
    - **JD 要求:** 未明确年龄范围  
    - **候选人情况:** 42岁  
    - **匹配结果:** 匹配  
- **薪资范围:**
    - **JD 要求:** 未提供具体薪资范围（需按候选人期望薪资反推JD上限）  
    - **候选人期望:** 30-40K  
    - **匹配结果:** 匹配（因JD未明确薪资上限，视为无冲突）

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。
    - 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。
    - 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。

- **简历证据分析:**
    - **Agent架构与系统设计：**
        - 候选人主导开发了多个AI Agent项目，如AI短视频创意Agent、AI招聘系统、AI视频翻译工具等，均基于LangGraph构建多Agent系统，使用Milvus作为向量数据库，具备任务规划、工具调用、多Agent协作等能力模块。
        - 在AI短视频创作平台中，候选人构建了搜索Agent、调研Agent、分析Agent、评估Agent的多角色团队，结合互联网搜索和评论分析进行创意设计，符合“从零设计并实现Agent架构”的要求。
    - **Agent核心功能实现：**
        - 在AI招聘项目中，候选人基于LangGraph构建RAG系统，接入Qwen-Max模型进行工作流规划和简历评估，使用DashScope语音识别和TTS模型开发面试Agent，体现了function calling、RAG、联网搜索等能力。
        - 在AI视频翻译工具中，候选人使用开源模型和工具链（Whisper v3、Qwen2、MoviePy）实现语音识别、翻译、视频剪辑等任务，展示了对AI工具链集成和自动化处理的掌握。
    - **大模型API统一接入与管理层设计：**
        - 候选人多次接入不同大模型API（Qwen、GPT3.5、DashScope等），并基于LangChain/LangGraph构建统一的Agent工作流，具备模型灵活切换与性能评估经验。
        - 在AI招聘系统中，候选人使用Qwen-Max进行任务调度与评估，显示其具备模型切换与性能监控的实践经验。

- **匹配度结论:** 高  

**3. 综合评估**

- **优势:**
    - 多个从0到1构建AI Agent系统的项目经验，涵盖创意生成、招聘、视频处理等复杂场景。
    - 深入掌握LangChain/LangGraph框架，具备RAG、function calling、多Agent协作等核心技术能力。
    - 有统一接入多模型API的经验，熟悉Qwen、GPT等主流模型的调用与性能调优。
    - 全栈开发能力突出，涵盖Python后端、微服务架构（FastAPI）、DevOps、AI部署等多方面。

- **风险与不足:**
    - 无明显硬性门槛“潜在风险”项。
    - 年龄42岁，略高于常规AI岗位平均年龄，但在架构与技术决策层面具备丰富经验，仍具竞争力。

**4. 初筛结论**

- **淘汰条件：**
    1. ✅ 无硬性门槛“不匹配”项。
    2. ✅ 核心职责匹配度为“高”，不符合淘汰条件。

- **通过条件：**
    1. ✅ 所有硬性门槛均匹配。
    2. ✅ 核心职责匹配度为“高”。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人具备完整的AI Agent系统设计与实现经验，符合JD中所有核心职责要求。学历匹配，年龄与薪资无冲突，核心能力高度契合，具备进入下一轮面试的资格。