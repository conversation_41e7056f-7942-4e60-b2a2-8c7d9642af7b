------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 周同学  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未明确说明
    - **候选人情况:** 硕士（中山大学，理论物理）
    - **匹配结果:** 匹配  
- **年龄要求:**
    - **JD 要求:** 未提供 age_range 字段，视为无年龄限制
    - **候选人情况:** 31岁
    - **匹配结果:** 匹配  
- **薪资范围:**
    - **JD 要求:** 未提供 salary_range 字段，视为无薪资限制
    - **候选人期望:** 18-25K
    - **匹配结果:** 匹配  

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。
    - 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。
    - 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。

- **简历证据分析:**
    - **职责1：从零设计并实现AI Agent框架架构**  
      候选人在“ChatBi对话机器人”项目中提到“agent的几个关键节点：多轮对话、意图识别、function call等”，表明其具备Agent架构中关键模块的实现经验，但未提及从零设计整体框架或任务规划、记忆管理等更完整架构的构建，缺乏系统性设计描述。
    - **职责2：主导Agent核心功能的技术实现与系统集成**  
      在ChatBi项目中明确提及RAG、function calling、多轮对话等技术，并描述了稀疏/稠密检索、排序融合、提示词工程等实现细节，具备实现Agent核心功能的实践经验。
    - **职责3：多大模型API的统一接入与管理层设计**  
      简历中提到“多个模型选型评估：分别测试了sqlcoder, qwen, llama的基础能力”，说明其有多个模型的对比与使用经验，但未体现“统一接入与管理层设计”的架构能力，如模型调度、切换、成本控制、性能监控等关键要素缺失。

- **匹配度结论:** 中  

**3. 综合评估**

- **优势:**
    - 具备Agent核心功能模块（RAG、function calling、多轮对话）的实际开发经验。
    - 熟悉多种大模型的使用与对比，具备一定的模型评估能力。
    - 有项目主导经验（如格力智绘AI绘图平台），体现一定的系统整合与落地能力。

- **风险与不足:**
    - 缺乏从零设计完整Agent架构的经验，任务规划、记忆管理等核心模块未见明确实现。
    - 未展示对多模型API统一接入与管理层的设计能力，无法满足JD中关于模型调度、成本控制等要求。
    - 未体现FastAPI/Flask或异步编程经验，这与核心技能要求存在差距。
    - 未体现对ReAct、Planning等Agent机制的深入理解与设计能力。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → **不符合**
    2. 核心职责匹配度 评估结果为 "**低**"。 → **不符合**
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → **符合**
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → **符合**

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人通过所有硬性门槛审查，核心职责匹配度为“中”，满足通过条件。尽管在架构设计完整性和模型管理层方面存在不足，但其具备Agent关键功能的实现经验，且有主导项目的落地能力，具备进一步评估的潜力。