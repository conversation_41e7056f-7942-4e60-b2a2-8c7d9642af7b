------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 孙茂森  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未明确列出，但根据岗位级别可推断为硕士及以上
    - **候选人情况:** 内蒙古财经大学 工商管理学院 信息管理与信息系统（硕士在读，2025年毕业）
    - **匹配结果:** 匹配

- **年龄要求:**
    - **JD 要求:** 未提供年龄范围
    - **候选人情况:** 未提供年龄信息
    - **匹配结果:** 匹配

- **薪资范围:**
    - **JD 要求:** 未提供薪资范围
    - **候选人期望:** 未提供期望薪资
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。
    - 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。
    - 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。

- **简历证据分析:**
    - **职责1相关证据：**  
      简历中提到“agent开发 - 超级智能助手”项目，明确参与了从任务拆解、Prompt调优、Function Calling评测、多路召回RAG、模型部署等全流程开发，具备从0到1构建Agent系统的经验。  
      > 原文：“基于 Gradio搭建交互系统，设计 Prompt调优工具提升算法与产品团队协作效率”，“通过种子样本扩散 构建任务拆解训练数据样本 10k”，“参与搜索模块的流程构建（多路召回的 rag）重构提示词 提升搜索结果的稳定性准确率”

    - **职责2相关证据：**  
      在Agent项目中，候选人明确参与了Function Calling能力评测、RAG流程构建、提示词优化、模型部署等工作，覆盖了JD中要求的多个核心功能模块。  
      > 原文：“评测多种模型的 Function Calling和边界能力，为后续模型选择调优提供依据”，“重构提示词 提升搜索结果的稳定性准确率”

    - **职责3相关证据：**  
      简历中多次提到部署多个大模型（如Qwen2.5、Llama3.1、Gemma2等）并进行API服务构建，具备多模型统一接入与管理的经验。  
      > 原文：“Qwen2 7b 2 b 微调部署”，“Gemma2 2b 9b 微调部署”，“使用 vllm用部署模型 qwen2.5 -7b 14b 32b 70b”，“部署成服务 部署到公网”

- **匹配度结论:** 高

**3. 综合评估**

- **优势:**  
  - 拥有完整的Agent系统开发经验，覆盖任务规划、RAG、Function Calling、Prompt工程等多个核心模块  
  - 多个大模型部署与API服务构建经验，具备模型灵活切换与性能优化能力  
  - 熟悉FastAPI、LangChain、vLLM等关键技术栈，具备工程化能力

- **风险与不足:**  
  - 硕士学历尚未毕业（2025年6月毕业），存在入职时间不确定性  
  - 缺乏明确的FastAPI/Flask异步编程项目描述（尽管技术栈中提及）  
  - 未体现明确的架构设计文档或抽象设计经验（尽管有实际项目经验）

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 所有硬性门槛均匹配，核心职责匹配度为“高”。尽管候选人硕士尚未毕业，但具备完整的Agent系统开发经验、多模型部署与API服务构建能力，技术栈与岗位高度契合，具备进入下一轮面试的资格。