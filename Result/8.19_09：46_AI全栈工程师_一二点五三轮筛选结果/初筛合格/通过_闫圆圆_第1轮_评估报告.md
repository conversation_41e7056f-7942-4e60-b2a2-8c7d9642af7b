------



**简历初筛评估报告**

**应聘岗位:** AI全栈工程师  
**候选人:** 闫圆圆  

**1. 硬性门槛审查**

- **学历要求:**
    - **JD 要求:** 未明确指定学历要求（默认本科及以上）
    - **候选人情况:** 晋中学院 大数据科学与技术 本科（2023年毕业）
    - **匹配结果:** 匹配
- **年龄要求:**
    - **JD 要求:** 未提供年龄范围
    - **候选人情况:** 25岁
    - **匹配结果:** 匹配
- **薪资范围:**
    - **JD 要求:** 未提供薪资范围
    - **候选人期望:** 简历中未提供期望薪资
    - **匹配结果:** 匹配

**2. 核心职责匹配度评估**

- **JD核心职责要求:**
    - 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。
    - 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。
    - 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。

- **简历证据分析:**
    - **职责1（Agent架构设计）：**
        - 简历提到候选人具备“企业级大模型系统设计与落地经验，擅长基于RAG与Agent技术构建智能化业务解决方案”，并拥有“Multi-Agent系统经验”，熟悉“LangGraph、Dify、Autogen等开发框架”。
        - 在“AIforBI大模型平台构建”项目中，涉及用户意图识别、要素提取、SQL生成、BI生成等流程，体现出一定的Agent流程设计能力。
        - **结论：有初步经验，但未明确展示从零设计完整Agent架构的细节。**

    - **职责2（Agent核心功能集成）：**
        - 明确提到“具备RAG与function calling实现能力”，在“智能导购系统”项目中使用RAG结合稀疏+稠密向量召回、rerank，并基于LLM进行回答生成。
        - 在“AIforBI”项目中展示了SQL生成能力，涉及Prompt模板设计、SQL执行流程，具备一定的function calling经验。
        - **结论：具备RAG与function calling能力，但缺乏代码执行、文件处理、联网搜索等模块的明确描述。**

    - **职责3（多模型API统一接入）：**
        - 简历提到“熟练运用主流开源及闭源大语言模型（如Qwen、ChatGPT、DeepSeek）”，具备“prompt工程设计与调优经验”。
        - 在“智能导购系统”中使用ChatGLM3-6B模型，基于vLLM部署，说明具备模型部署与调用经验。
        - **结论：具备多模型调用经验，但未展示统一管理层设计的证据。**

- **匹配度结论:** 中

**3. 综合评估**

- **优势:**
    - 拥有多个企业级大模型项目落地经验，涵盖RAG、Prompt工程、Function Calling等关键技术。
    - 熟悉主流大模型框架与部署方案，具备良好的工程化能力。
- **风险与不足:**
    - 缺乏完整Agent框架从0到1的设计经验描述。
    - 未展示统一模型管理层的设计与实现细节。
    - 缺乏FastAPI/Flask异步编程经验的明确证据。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 硬性门槛均匹配，核心职责匹配度为“中”，具备RAG、Function Calling、多模型调用等关键能力，具备AI全栈工程师岗位的基本胜任力，建议进入下一轮技术面试。