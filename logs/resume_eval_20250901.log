2025-09-01 09:05:46 - INFO - 🚀 启动智能简历评估系统
2025-09-01 09:05:46 - INFO - 📋 系统功能:
2025-09-01 09:05:46 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 09:05:46 - INFO -   - 多线程并发处理
2025-09-01 09:05:46 - INFO -   - 实时进度监控
2025-09-01 09:05:46 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 09:05:46 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 09:05:46 - INFO -  - JD来源: D:\project\智能招聘系统_version1.0.1_简化\智能招聘系统_version1.0.3_08.31\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 09:05:46 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 09:06:02 - INFO - 🔐 统一登录请求: normal
2025-09-01 09:06:02 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 09:06:07 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:06:07 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:06:07 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:06:22 - ERROR - 获取用户信息失败: 
与页面的连接已断开。
版本: *******
2025-09-01 09:06:22 - WARNING - ⚠️ Boss自动化失败，无法提取用户信息
2025-09-01 09:06:58 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:06:58 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:06:59 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:07:00 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:07:00 - INFO - 等待用户登录...
2025-09-01 09:07:02 - INFO - 用户已登录
2025-09-01 09:07:02 - INFO - 开始提取用户信息
2025-09-01 09:07:02 - INFO - 用户名: 杨锐雄
2025-09-01 09:07:02 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:07:02 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:07:31 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:07:31 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:07:31 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:07:32 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:07:32 - INFO - 等待用户登录...
2025-09-01 09:07:34 - INFO - 用户已登录
2025-09-01 09:07:34 - INFO - 开始提取用户信息
2025-09-01 09:07:34 - INFO - 用户名: 杨锐雄
2025-09-01 09:07:34 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:07:34 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:07:45 - INFO - 未检测到弹窗
2025-09-01 09:07:45 - INFO - 开始提取职位信息...
2025-09-01 09:07:46 - INFO - 成功进入职位管理页面
2025-09-01 09:07:47 - INFO - 已切换到iframe
2025-09-01 09:07:47 - INFO - 一共获取到3个职位
2025-09-01 09:07:47 - INFO - 开始提取第1个职位信息...
2025-09-01 09:07:47 - INFO - 第1次进入iframe
2025-09-01 09:07:47 - INFO - 职位名称: AI全栈工程师
2025-09-01 09:07:49 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 09:07:49 - INFO - 第1个职位信息提取完成
2025-09-01 09:07:49 - INFO - 开始提取第2个职位信息...
2025-09-01 09:07:49 - INFO - 第2次进入iframe
2025-09-01 09:07:50 - INFO - 职位名称: AI产品经理
2025-09-01 09:07:51 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 09:07:51 - INFO - 第2个职位信息提取完成
2025-09-01 09:07:51 - INFO - 开始提取第3个职位信息...
2025-09-01 09:07:52 - INFO - 第3次进入iframe
2025-09-01 09:07:52 - INFO - 职位名称: 人力资源主管
2025-09-01 09:07:54 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 09:07:54 - INFO - 第3个职位信息提取完成
2025-09-01 09:08:11 - INFO - 用户 杨锐雄 删除了JD文件: AI产品经理.md
2025-09-01 09:08:14 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:08:14 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:08:14 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:08:15 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:08:15 - INFO - 等待用户登录...
2025-09-01 09:08:17 - INFO - 用户已登录
2025-09-01 09:08:17 - INFO - 开始提取用户信息
2025-09-01 09:08:17 - INFO - 用户名: 杨锐雄
2025-09-01 09:08:17 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:08:17 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:08:32 - INFO - 未检测到弹窗
2025-09-01 09:08:32 - INFO - 开始提取职位信息...
2025-09-01 09:08:32 - INFO - 成功进入职位管理页面
2025-09-01 09:08:33 - INFO - 已切换到iframe
2025-09-01 09:08:33 - INFO - 一共获取到3个职位
2025-09-01 09:08:33 - INFO - 开始提取第1个职位信息...
2025-09-01 09:08:33 - INFO - 第1次进入iframe
2025-09-01 09:08:33 - INFO - 职位名称: AI全栈工程师
2025-09-01 09:08:35 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 09:08:35 - INFO - 第1个职位信息提取完成
2025-09-01 09:08:35 - INFO - 开始提取第2个职位信息...
2025-09-01 09:08:35 - INFO - 第2次进入iframe
2025-09-01 09:08:36 - INFO - 职位名称: AI产品经理
2025-09-01 09:08:38 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 09:08:38 - INFO - 第2个职位信息提取完成
2025-09-01 09:08:38 - INFO - 开始提取第3个职位信息...
2025-09-01 09:08:38 - INFO - 第3次进入iframe
2025-09-01 09:08:38 - INFO - 职位名称: 人力资源主管
2025-09-01 09:08:40 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 09:08:40 - INFO - 第3个职位信息提取完成
2025-09-01 09:09:34 - INFO - 🔐 统一登录请求: normal
2025-09-01 09:09:34 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 09:09:48 - INFO - 🔐 统一登录请求: normal
2025-09-01 09:09:48 - INFO - ✅ 普通用户 123131231312 登录成功
2025-09-01 09:09:57 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:09:57 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:09:57 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:09:58 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:09:58 - INFO - 等待用户登录...
2025-09-01 09:10:00 - INFO - 用户已登录
2025-09-01 09:10:00 - INFO - 开始提取用户信息
2025-09-01 09:10:00 - INFO - 用户名: 杨锐雄
2025-09-01 09:10:00 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:10:00 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:10:15 - INFO - 未检测到弹窗
2025-09-01 09:10:15 - INFO - 开始提取职位信息...
2025-09-01 09:10:15 - INFO - 成功进入职位管理页面
2025-09-01 09:10:16 - INFO - 已切换到iframe
2025-09-01 09:10:16 - INFO - 一共获取到3个职位
2025-09-01 09:10:16 - INFO - 开始提取第1个职位信息...
2025-09-01 09:10:17 - INFO - 第1次进入iframe
2025-09-01 09:10:17 - INFO - 职位名称: AI全栈工程师
2025-09-01 09:10:18 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 09:10:18 - INFO - 第1个职位信息提取完成
2025-09-01 09:10:18 - INFO - 开始提取第2个职位信息...
2025-09-01 09:10:19 - INFO - 第2次进入iframe
2025-09-01 09:10:19 - INFO - 职位名称: AI产品经理
2025-09-01 09:10:21 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 09:10:21 - INFO - 第2个职位信息提取完成
2025-09-01 09:10:21 - INFO - 开始提取第3个职位信息...
2025-09-01 09:10:21 - INFO - 第3次进入iframe
2025-09-01 09:10:22 - INFO - 职位名称: 人力资源主管
2025-09-01 09:10:24 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 09:10:24 - INFO - 第3个职位信息提取完成
2025-09-01 09:10:39 - INFO - 未检测到弹窗
2025-09-01 09:10:39 - INFO - 开始提取职位信息...
2025-09-01 09:10:39 - INFO - 成功进入职位管理页面
2025-09-01 09:10:40 - INFO - 已切换到iframe
2025-09-01 09:10:40 - INFO - 一共获取到3个职位
2025-09-01 09:10:40 - INFO - 开始提取第1个职位信息...
2025-09-01 09:10:41 - INFO - 第1次进入iframe
2025-09-01 09:10:41 - INFO - 职位名称: AI全栈工程师
2025-09-01 09:10:42 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 09:10:42 - INFO - 第1个职位信息提取完成
2025-09-01 09:10:42 - INFO - 开始提取第2个职位信息...
2025-09-01 09:10:43 - INFO - 第2次进入iframe
2025-09-01 09:10:43 - INFO - 职位名称: AI产品经理
2025-09-01 09:10:45 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 09:10:45 - INFO - 第2个职位信息提取完成
2025-09-01 09:10:45 - INFO - 开始提取第3个职位信息...
2025-09-01 09:10:45 - INFO - 第3次进入iframe
2025-09-01 09:10:46 - INFO - 职位名称: 人力资源主管
2025-09-01 09:10:47 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 09:10:47 - INFO - 第3个职位信息提取完成
2025-09-01 09:10:54 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:10:54 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:10:54 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:10:54 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:10:54 - INFO - 等待用户登录...
2025-09-01 09:10:56 - INFO - 用户已登录
2025-09-01 09:10:56 - INFO - 开始提取用户信息
2025-09-01 09:10:56 - INFO - 用户名: 杨锐雄
2025-09-01 09:10:56 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:10:56 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:11:18 - INFO - 🔐 统一登录请求: boss
2025-09-01 09:11:18 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 09:11:18 - INFO - DrissionPage浏览器初始化成功
2025-09-01 09:11:18 - INFO - 已访问登录页面，请手动登录
2025-09-01 09:11:18 - INFO - 等待用户登录...
2025-09-01 09:11:20 - INFO - 用户已登录
2025-09-01 09:11:20 - INFO - 开始提取用户信息
2025-09-01 09:11:20 - INFO - 用户名: 杨锐雄
2025-09-01 09:11:20 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 09:11:20 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 09:11:31 - INFO - 未检测到弹窗
2025-09-01 09:11:31 - INFO - 开始提取职位信息...
2025-09-01 09:11:32 - INFO - 成功进入职位管理页面
2025-09-01 09:11:33 - INFO - 已切换到iframe
2025-09-01 09:11:33 - INFO - 一共获取到3个职位
2025-09-01 09:11:33 - INFO - 开始提取第1个职位信息...
2025-09-01 09:11:33 - INFO - 第1次进入iframe
2025-09-01 09:11:33 - INFO - 职位名称: AI全栈工程师
2025-09-01 09:11:35 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 09:11:35 - INFO - 第1个职位信息提取完成
2025-09-01 09:11:35 - INFO - 开始提取第2个职位信息...
2025-09-01 09:11:35 - INFO - 第2次进入iframe
2025-09-01 09:11:36 - INFO - 职位名称: AI产品经理
2025-09-01 09:11:37 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': ''}
2025-09-01 09:11:37 - INFO - 第2个职位信息提取完成
2025-09-01 09:11:37 - INFO - 开始提取第3个职位信息...
2025-09-01 09:11:38 - INFO - 第3次进入iframe
2025-09-01 09:11:38 - INFO - 职位名称: 人力资源主管
2025-09-01 09:11:40 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 09:11:40 - INFO - 第3个职位信息提取完成
2025-09-01 09:45:35 - INFO - 🚀 启动智能简历评估系统
2025-09-01 09:45:35 - INFO - 📋 系统功能:
2025-09-01 09:45:35 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 09:45:35 - INFO -   - 多线程并发处理
2025-09-01 09:45:35 - INFO -   - 实时进度监控
2025-09-01 09:45:35 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 09:45:35 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 09:45:35 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 09:45:35 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 09:46:35 - INFO - 🔐 统一登录请求: normal
2025-09-01 09:46:35 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 10:02:10 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:02:10 - INFO - 📋 系统功能:
2025-09-01 10:02:10 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:02:10 - INFO -   - 多线程并发处理
2025-09-01 10:02:10 - INFO -   - 实时进度监控
2025-09-01 10:02:10 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:02:10 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:02:10 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:02:10 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 10:02:16 - INFO - 🔐 统一登录请求: normal
2025-09-01 10:02:16 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 10:02:21 - INFO - 爬取任务已启动: 06a15c6c-4e63-419d-bd9a-03e250ba7ea7, 职位: 人力资源主管
2025-09-01 10:02:21 - INFO - 🚀 开始调用爬虫程序: 06a15c6c-4e63-419d-bd9a-03e250ba7ea7, 职位: 人力资源主管, 目标数量: 20
2025-09-01 10:02:21 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:02:24 - ERROR - 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:02:24 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:04:33 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:04:33 - INFO - 📋 系统功能:
2025-09-01 10:04:33 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:04:33 - INFO -   - 多线程并发处理
2025-09-01 10:04:33 - INFO -   - 实时进度监控
2025-09-01 10:04:33 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:04:33 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:04:33 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:04:33 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 10:04:38 - INFO - 🔐 统一登录请求: normal
2025-09-01 10:04:38 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 10:04:42 - INFO - 爬取任务已启动: c3cb3153-6b50-4098-9640-96a2fdcf012e, 职位: 人力资源主管
2025-09-01 10:04:42 - INFO - 🚀 开始调用爬虫程序: c3cb3153-6b50-4098-9640-96a2fdcf012e, 职位: 人力资源主管, 目标数量: 5
2025-09-01 10:04:42 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:04:45 - ERROR - 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:04:45 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:05:30 - INFO - 爬取任务已启动: 1491661e-5b3a-4e79-a7ec-c4044514d6ae, 职位: 人力资源主管
2025-09-01 10:05:30 - INFO - 🚀 开始调用爬虫程序: 1491661e-5b3a-4e79-a7ec-c4044514d6ae, 职位: 人力资源主管, 目标数量: 5
2025-09-01 10:05:30 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:05:32 - ERROR - 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:05:32 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:09:53 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:09:53 - INFO - 📋 系统功能:
2025-09-01 10:09:53 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:09:53 - INFO -   - 多线程并发处理
2025-09-01 10:09:53 - INFO -   - 实时进度监控
2025-09-01 10:09:53 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:09:53 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:09:53 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:09:53 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 10:11:05 - INFO - 🔐 统一登录请求: normal
2025-09-01 10:11:05 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 10:11:07 - INFO - 爬取任务已启动: dec3aa50-f476-49b3-997c-f2be86686a92, 职位: 人力资源主管
2025-09-01 10:11:07 - INFO - 🚀 开始调用爬虫程序: dec3aa50-f476-49b3-997c-f2be86686a92, 职位: 人力资源主管, 目标数量: 20
2025-09-01 10:11:07 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:11:10 - ERROR - 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:11:10 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:11:30 - INFO - 爬取任务已启动: 68959dcf-7c19-4407-8801-d2c70405e175, 职位: 人力资源主管
2025-09-01 10:11:30 - INFO - 🚀 开始调用爬虫程序: 68959dcf-7c19-4407-8801-d2c70405e175, 职位: 人力资源主管, 目标数量: 20
2025-09-01 10:11:30 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:11:33 - ERROR - 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:11:33 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 岗位匹配失败: Cannot connect to host localhost:8000 ssl:default [Multiple exceptions: [Errno 10061] Connect call failed ('::1', 8000, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 8000)]
2025-09-01 10:13:26 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:13:26 - INFO - 📋 系统功能:
2025-09-01 10:13:26 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:13:26 - INFO -   - 多线程并发处理
2025-09-01 10:13:26 - INFO -   - 实时进度监控
2025-09-01 10:13:26 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:13:26 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:13:26 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:13:26 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 10:27:14 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:27:14 - INFO - 📋 系统功能:
2025-09-01 10:27:14 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:27:14 - INFO -   - 多线程并发处理
2025-09-01 10:27:14 - INFO -   - 实时进度监控
2025-09-01 10:27:14 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:27:14 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:27:14 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:27:14 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 10:29:02 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:29:02 - INFO - 📋 系统功能:
2025-09-01 10:29:02 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:29:02 - INFO -   - 多线程并发处理
2025-09-01 10:29:02 - INFO -   - 实时进度监控
2025-09-01 10:29:02 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:29:02 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:29:02 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:29:02 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 10:29:02 - INFO - 任务管理已初始化
2025-09-01 10:29:10 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:29:10 - INFO - 📋 系统功能:
2025-09-01 10:29:10 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:29:10 - INFO -   - 多线程并发处理
2025-09-01 10:29:10 - INFO -   - 实时进度监控
2025-09-01 10:29:10 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:29:10 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:29:10 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:29:10 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 10:29:10 - INFO - 任务管理已初始化
2025-09-01 10:32:58 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:32:58 - INFO - 📋 系统功能:
2025-09-01 10:32:58 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:32:58 - INFO -   - 多线程并发处理
2025-09-01 10:32:58 - INFO -   - 实时进度监控
2025-09-01 10:32:58 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:32:58 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:32:58 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:32:58 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 10:33:02 - INFO - 🔐 统一登录请求: normal
2025-09-01 10:33:02 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 10:33:03 - INFO - 爬取任务已启动: 3e1fbf05-c531-4cf7-b55b-e76621452042, 职位: 人力资源主管
2025-09-01 10:33:03 - INFO - 🚀 开始调用爬虫程序: 3e1fbf05-c531-4cf7-b55b-e76621452042, 职位: 人力资源主管, 目标数量: 20
2025-09-01 10:33:03 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:33:23 - INFO - 岗位匹配结果: {'success': False, 'message': '未找到岗位推荐信息，请确保页面已正确加载'}
2025-09-01 10:33:23 - ERROR - 调用爬虫程序失败: 岗位匹配失败: 未找到岗位推荐信息，请确保页面已正确加载
2025-09-01 10:34:25 - INFO - 🔐 统一登录请求: boss
2025-09-01 10:34:25 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 10:34:25 - INFO - DrissionPage浏览器初始化成功
2025-09-01 10:34:25 - INFO - 已访问登录页面，请手动登录
2025-09-01 10:34:25 - INFO - 等待用户登录...
2025-09-01 10:34:27 - INFO - 用户已登录
2025-09-01 10:34:27 - INFO - 开始提取用户信息
2025-09-01 10:34:27 - INFO - 用户名: 杨锐雄
2025-09-01 10:34:27 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 10:34:27 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 10:34:34 - INFO - 爬取任务已启动: c2cf54ae-d965-4f33-813c-1ba5b8caf497, 职位: AI全栈工程师
2025-09-01 10:34:34 - INFO - 🚀 开始调用爬虫程序: c2cf54ae-d965-4f33-813c-1ba5b8caf497, 职位: AI全栈工程师, 目标数量: 20
2025-09-01 10:34:34 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:34:42 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': 'AI全栈工程师', 'recommendation': 'AI全栈工程师 _ 珠海 15-20K', 'match_reason': '完全匹配', 'message': '岗位匹配成功！当前推荐: AI全栈工程师 _ 珠海 15-20K'}
2025-09-01 10:34:42 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-09-01 10:34:42 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-09-01 10:34:42 - INFO - ✅ 爬取任务已启动: task_1
2025-09-01 10:34:42 - INFO - 开始监控爬取进度: task_1
2025-09-01 10:34:52 - ERROR - 监控爬取进度异常: 
2025-09-01 10:35:13 - ERROR - 监控爬取进度异常: 
2025-09-01 10:35:34 - ERROR - 监控爬取进度异常: 
2025-09-01 10:35:47 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:35:59 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:36:11 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:36:27 - ERROR - 监控爬取进度异常: 
2025-09-01 10:36:45 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:36:57 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:37:13 - ERROR - 监控爬取进度异常: 
2025-09-01 10:37:31 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:37:43 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:37:59 - ERROR - 监控爬取进度异常: 
2025-09-01 10:38:17 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:38:29 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:38:45 - ERROR - 监控爬取进度异常: 
2025-09-01 10:39:04 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:39:16 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:39:31 - ERROR - 监控爬取进度异常: 
2025-09-01 10:39:50 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:40:02 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:40:17 - ERROR - 监控爬取进度异常: 
2025-09-01 10:40:36 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:40:48 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:41:03 - ERROR - 监控爬取进度异常: 
2025-09-01 10:41:22 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:41:34 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:41:49 - ERROR - 监控爬取进度异常: 
2025-09-01 10:42:08 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:42:20 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:42:35 - ERROR - 监控爬取进度异常: 
2025-09-01 10:42:54 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:43:06 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:43:22 - ERROR - 监控爬取进度异常: 
2025-09-01 10:43:40 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:43:52 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:44:08 - ERROR - 监控爬取进度异常: 
2025-09-01 10:44:26 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:44:38 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:44:54 - ERROR - 监控爬取进度异常: 
2025-09-01 10:45:12 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:45:24 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:45:40 - ERROR - 监控爬取进度异常: 
2025-09-01 10:45:58 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:46:10 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 0.0, 'results_count': 0, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': []}
2025-09-01 10:46:26 - ERROR - 监控爬取进度异常: 
2025-09-01 10:49:01 - INFO - 🚀 启动智能简历评估系统
2025-09-01 10:49:01 - INFO - 📋 系统功能:
2025-09-01 10:49:01 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 10:49:01 - INFO -   - 多线程并发处理
2025-09-01 10:49:01 - INFO -   - 实时进度监控
2025-09-01 10:49:01 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 10:49:01 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 10:49:01 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 10:49:01 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 10:49:08 - INFO - 🔐 统一登录请求: boss
2025-09-01 10:49:08 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 10:49:09 - INFO - DrissionPage浏览器初始化成功
2025-09-01 10:49:09 - INFO - 已访问登录页面，请手动登录
2025-09-01 10:49:09 - INFO - 等待用户登录...
2025-09-01 10:49:11 - INFO - 用户已登录
2025-09-01 10:49:11 - INFO - 开始提取用户信息
2025-09-01 10:49:11 - INFO - 用户名: 杨锐雄
2025-09-01 10:49:11 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 10:49:11 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 10:49:17 - INFO - 爬取任务已启动: 605df213-de8f-46e8-8356-3f162b2832de, 职位: AI产品经理
2025-09-01 10:49:17 - INFO - 🚀 开始调用爬虫程序: 605df213-de8f-46e8-8356-3f162b2832de, 职位: AI产品经理, 目标数量: 20
2025-09-01 10:49:17 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:49:43 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': 'AI产品经理', 'message': '成功切换到岗位: AI产品经理'}
2025-09-01 10:49:43 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-09-01 10:49:44 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-09-01 10:49:44 - INFO - ✅ 爬取任务已启动: task_1
2025-09-01 10:49:44 - INFO - 开始监控爬取进度: task_1
2025-09-01 10:49:54 - ERROR - 监控爬取进度异常: 
2025-09-01 10:50:15 - ERROR - 监控爬取进度异常: 
2025-09-01 10:50:26 - INFO - 🔐 统一登录请求: boss
2025-09-01 10:50:26 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 10:50:26 - INFO - DrissionPage浏览器初始化成功
2025-09-01 10:50:27 - INFO - 已访问登录页面，请手动登录
2025-09-01 10:50:27 - INFO - 等待用户登录...
2025-09-01 10:50:37 - INFO - 用户已登录
2025-09-01 10:50:37 - INFO - 开始提取用户信息
2025-09-01 10:50:37 - INFO - 用户名: 杨锐雄
2025-09-01 10:50:37 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 10:50:37 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 10:50:37 - ERROR - 监控爬取进度异常: 
2025-09-01 10:50:47 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:50:52 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:50:58 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:51:02 - INFO - 爬取任务已启动: f1df43c2-c2b0-4888-a3dc-97c7bb9795d7, 职位: 人力资源主管
2025-09-01 10:51:02 - INFO - 🚀 开始调用爬虫程序: f1df43c2-c2b0-4888-a3dc-97c7bb9795d7, 职位: 人力资源主管, 目标数量: 5
2025-09-01 10:51:02 - INFO - 🔍 检查岗位匹配...
2025-09-01 10:51:14 - ERROR - 监控爬取进度异常: 
2025-09-01 10:51:29 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': '人力资源主管', 'message': '成功切换到岗位: 人力资源主管'}
2025-09-01 10:51:29 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-09-01 10:51:30 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:51:30 - INFO - 爬取任务启动结果: {'task_id': 'task_2', 'message': '收集任务已启动'}
2025-09-01 10:51:30 - INFO - ✅ 爬取任务已启动: task_2
2025-09-01 10:51:30 - INFO - 开始监控爬取进度: task_2
2025-09-01 10:51:40 - ERROR - 监控爬取进度异常: 
2025-09-01 10:51:45 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:01 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:06 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:22 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:26 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:43 - ERROR - 监控爬取进度异常: 
2025-09-01 10:52:47 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:04 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:07 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:24 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:28 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:45 - ERROR - 监控爬取进度异常: 
2025-09-01 10:53:48 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:05 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:09 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:25 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:30 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:46 - ERROR - 监控爬取进度异常: 
2025-09-01 10:54:51 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:07 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:12 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:28 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:33 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:49 - ERROR - 监控爬取进度异常: 
2025-09-01 10:55:54 - ERROR - 监控爬取进度异常: 
2025-09-01 10:56:10 - ERROR - 监控爬取进度异常: 
2025-09-01 10:56:15 - ERROR - 监控爬取进度异常: 
2025-09-01 10:56:24 - INFO - 爬取状态: {'task_id': 'task_2', 'status': 'completed', 'progress': 100, 'results_count': 10, 'target_count': 5, 'current_action': '收集完成', 'current_resumes': [{'filename': '王威_人力资源主管_20250825_205547.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\王威_人力资源主管_20250825_205547.png', 'size': 305581, 'created_time': 1756126548.0, 'candidate_name': '王威'}, {'filename': '郭女士_人力资源主管_20250825_205648.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\郭女士_人力资源主管_20250825_205648.png', 'size': 175575, 'created_time': 1756126608.0, 'candidate_name': '郭女士'}, {'filename': '林小姐_人力资源主管_20250825_205744.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\林小姐_人力资源主管_20250825_205744.png', 'size': 84791, 'created_time': 1756126664.0, 'candidate_name': '林小姐'}, {'filename': '梁滴翠_人力资源主管_20250825_205844.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\梁滴翠_人力资源主管_20250825_205844.png', 'size': 212255, 'created_time': 1756126724.0, 'candidate_name': '梁滴翠'}, {'filename': '王相元_人力资源主管_20250825_205946.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\王相元_人力资源主管_20250825_205946.png', 'size': 230521, 'created_time': 1756126786.0, 'candidate_name': '王相元'}, {'filename': '范女士_人力资源主管_20250901_105221.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\范女士_人力资源主管_20250901_105221.png', 'size': 179147, 'created_time': 1756695141.4254563, 'candidate_name': '范女士'}, {'filename': '刘丹_人力资源主管_20250901_105318.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\刘丹_人力资源主管_20250901_105318.png', 'size': 110696, 'created_time': 1756695198.9682763, 'candidate_name': '刘丹'}, {'filename': '刘春秀_人力资源主管_20250901_105417.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\刘春秀_人力资源主管_20250901_105417.png', 'size': 132205, 'created_time': 1756695257.106364, 'candidate_name': '刘春秀'}, {'filename': '李杏娜_人力资源主管_20250901_105514.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\李杏娜_人力资源主管_20250901_105514.png', 'size': 94396, 'created_time': 1756695314.0349405, 'candidate_name': '李杏娜'}, {'filename': '金女士_人力资源主管_20250901_105611.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\人力资源主管\\金女士_人力资源主管_20250901_105611.png', 'size': 124249, 'created_time': 1756695371.5424595, 'candidate_name': '金女士'}]}
2025-09-01 10:56:24 - INFO - 爬取完成，获取简历列表...
2025-09-01 10:56:25 - INFO - 获取到 10 份简历
2025-09-01 10:56:25 - INFO - 爬取任务完成: f1df43c2-c2b0-4888-a3dc-97c7bb9795d7, 共获取 10 份简历
2025-09-01 10:56:25 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:30 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:35 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:41 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:47 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:52 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:56:57 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:57:02 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'running', 'progress': 60.0, 'results_count': 12, 'target_count': 20, 'current_action': '正在初始化浏览器...', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:57:07 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'completed', 'progress': 100, 'results_count': 12, 'target_count': 20, 'current_action': '收集完成', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}]}
2025-09-01 10:57:07 - INFO - 爬取完成，获取简历列表...
2025-09-01 10:57:07 - INFO - 获取到 12 份简历
2025-09-01 10:57:07 - INFO - 爬取任务完成: 605df213-de8f-46e8-8356-3f162b2832de, 共获取 12 份简历
2025-09-01 11:15:50 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:15:50 - INFO - 📋 系统功能:
2025-09-01 11:15:50 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:15:50 - INFO -   - 多线程并发处理
2025-09-01 11:15:50 - INFO -   - 实时进度监控
2025-09-01 11:15:50 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:15:50 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:15:50 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:15:50 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:15:56 - INFO - 🔐 统一登录请求: boss
2025-09-01 11:15:56 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 11:15:56 - INFO - DrissionPage浏览器初始化成功
2025-09-01 11:15:56 - INFO - 已访问登录页面，请手动登录
2025-09-01 11:15:56 - INFO - 等待用户登录...
2025-09-01 11:16:10 - INFO - 用户已登录
2025-09-01 11:16:10 - INFO - 开始提取用户信息
2025-09-01 11:16:10 - INFO - 用户名: 杨锐雄
2025-09-01 11:16:10 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 11:16:10 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 11:18:53 - INFO - 🔐 统一登录请求: boss
2025-09-01 11:18:53 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 11:18:53 - INFO - DrissionPage浏览器初始化成功
2025-09-01 11:18:53 - INFO - 已访问登录页面，请手动登录
2025-09-01 11:18:53 - INFO - 等待用户登录...
2025-09-01 11:18:55 - INFO - 用户已登录
2025-09-01 11:18:55 - INFO - 开始提取用户信息
2025-09-01 11:18:55 - INFO - 用户名: 杨锐雄
2025-09-01 11:18:55 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 11:18:55 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 11:19:05 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-09-01 11:19:05 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\JD_md转json.md
2025-09-01 11:19:10 - INFO - ✅ 成功将MD格式JD转换为JSON格式并保存到用户目录: user\杨锐雄\jd\AI产品经理.json
2025-09-01 11:19:10 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-09-01 11:19:10 - INFO - ✅ 发现已存在的JSON文件: user\杨锐雄\jd\AI产品经理.json
2025-09-01 11:19:10 - INFO - ✅ JSON文件格式验证通过
2025-09-01 11:19:10 - INFO - 提取的岗位名称: 'AI产品经理'
2025-09-01 11:19:10 - INFO - 最终使用的岗位名称: 'AI产品经理'
2025-09-01 11:19:10 - INFO - ✅ 保存新候选人简历到VL目录: user\杨锐雄\vl\VL_Result_AI产品经理\曾杰_VL识别结果.txt
2025-09-01 11:19:10 - INFO - 开始处理简历: 曾杰
2025-09-01 11:19:10 - INFO - 开始第1轮评估: 曾杰
2025-09-01 11:19:10 - INFO - 岗位名称: AI产品经理
2025-09-01 11:19:10 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 11:19:10 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 11:19:41 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-09-01 11:19:41 - INFO - 检测到明确通过标识: ✅ 通过
2025-09-01 11:19:41 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：19_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_曾杰_第1轮_评估报告.md
2025-09-01 11:19:41 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-09-01 11:19:41 - INFO - 找到优势文本: **优势:**  
  - 多年AI产品设计经验，具备全流程管理能力。  
  - 深入理解AI技术（如YOLO、RAG、Prompt工程），并能结合业务场景落地。  
  - 项目成果显著，多个项目实现成本节约与效率提升。

- 
2025-09-01 11:19:41 - INFO - 找到风险文本: **风险与不足:**  
  - 无“不匹配”项。  
  - 期望薪资未提供，可能在后续沟通中存在不确定性（潜在风险）。  
  - 年龄38岁虽在JD未设限，但在部分企业中可能被视为“偏高”，但非硬性门槛问题。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人学历、年龄、薪资均满足硬性门槛要求，且在AI产品设计、跨部门协作、AI技术与业务融合方面具备丰富经验，核心职责匹配度为“高”。虽然期望薪资未提供，但未构成“不匹配”项，不影响初筛通过。
2025-09-01 11:19:41 - INFO - 提取的优势和风险: 优势=多年AI产品设计经验，具备全流程管理能力。  ；深入理解AI技术（如YOLO、RAG、Prompt工程），并能结合业务场景落地。  ；项目成果显著，多个项目实现成本节约与效率提升。, 风险=无“不匹配”项。  ；期望薪资未提供，可能在后续沟通中存在不确定性（潜在风险）。  
2025-09-01 11:19:41 - INFO - 提取到姓名: 曾杰
2025-09-01 11:19:41 - INFO - 提取到年龄: 38岁
2025-09-01 11:19:41 - INFO - 提取到工作经验: 10年以上
2025-09-01 11:19:41 - INFO - 提取到学历: 硕士
2025-09-01 11:19:41 - INFO - 提取到当前职位: 产品经理
2025-09-01 11:19:41 - INFO - 提取到期望薪资: 未提供
2025-09-01 11:19:41 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-09-01 11:19:41 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=多年AI产品设计经验，具备全流程管理能力。  ；深入理解AI技术（如YOLO、RAG、Prompt工程），并能结合业务场景落地。  ；项目成果显著，多个项目实现成本节约与效率提升。, risks=无“不匹配”项。  ；期望薪资未提供，可能在后续沟通中存在不确定性（潜在风险）。  , basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-09-01 11:19:41 - INFO - 开始第2轮评估: 曾杰
2025-09-01 11:19:41 - INFO - 岗位名称: AI产品经理
2025-09-01 11:19:41 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像分析.md
2025-09-01 11:19:41 - INFO - 第2轮评估使用模型: qwen-plus
2025-09-01 11:20:19 - WARNING - 分数不一致: 总分=83.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-09-01 11:20:19 - WARNING - 候选人曾杰分数不一致: 总分=83.0, 计算总分=0.0
2025-09-01 11:20:19 - INFO - ✅ 第2轮评估完成: 曾杰 | 分数: 83.0
2025-09-01 11:20:19 - INFO - 未检测到明确关键词，默认通过
2025-09-01 11:20:19 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：19_AI产品经理_一二点五三轮筛选结果/画像分析结果\曾杰_第2轮_画像分析.md
2025-09-01 11:20:19 - INFO - 开始第2.5轮评估: 曾杰
2025-09-01 11:20:19 - INFO - 岗位名称: AI产品经理
2025-09-01 11:20:19 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 11:20:19 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 11:20:19 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 11:20:24 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 11:20:27 - INFO - ✅ 第2.5轮评估完成: 曾杰 | 分数: 0.0
2025-09-01 11:20:27 - INFO - 第2.5轮检测到明确通过标识: ✅ 通过
2025-09-01 11:20:27 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：19_AI产品经理_一二点五三轮筛选结果/二筛合格\通过_曾杰_第2.5轮_评估报告.md
2025-09-01 11:20:27 - INFO - 开始第3轮评估: 曾杰
2025-09-01 11:20:27 - INFO - 岗位名称: AI产品经理
2025-09-01 11:20:27 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\终极筛选_打分规则.md
2025-09-01 11:20:27 - INFO - 第3轮评估使用模型: qwen-plus
2025-09-01 11:21:35 - WARNING - 分数不一致: 总分=21.0, 计算总分=90.0 (A:47.0+B:34.0+C:9.0+D:0.0+E:0.0)
2025-09-01 11:21:35 - WARNING - 候选人曾杰分数不一致: 总分=21.0, 计算总分=90.0
2025-09-01 11:21:35 - INFO - ✅ 第3轮评估完成: 曾杰 | 分数: 21.0
2025-09-01 11:21:35 - INFO - 第3轮检测到合格关键词: 通过
2025-09-01 11:21:35 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：19_AI产品经理_一二点五三轮筛选结果/最终通过\21.0分_曾杰_第3轮_评估报告.md
2025-09-01 11:21:35 - INFO - ✅ 曾杰 完成三轮筛选，最终得分: 21.0, 是否通过: True
2025-09-01 11:21:35 - INFO - 候选人 曾杰 最终返回信息: advantages=多年AI产品设计经验，具备全流程管理能力。  ；深入理解AI技术（如YOLO、RAG、Prompt工程），并能结合业务场景落地。  ；项目成果显著，多个项目实现成本节约与效率提升。, risks=无“不匹配”项。  ；期望薪资未提供，可能在后续沟通中存在不确定性（潜在风险）。  , basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '未提供'}
2025-09-01 11:41:52 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:41:52 - INFO - 📋 系统功能:
2025-09-01 11:41:52 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:41:52 - INFO -   - 多线程并发处理
2025-09-01 11:41:52 - INFO -   - 实时进度监控
2025-09-01 11:41:52 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:41:52 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:41:52 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:41:52 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:42:23 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:42:23 - INFO - 📋 系统功能:
2025-09-01 11:42:23 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:42:23 - INFO -   - 多线程并发处理
2025-09-01 11:42:23 - INFO -   - 实时进度监控
2025-09-01 11:42:23 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:42:23 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:42:23 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:42:23 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:47:35 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:47:35 - INFO - 📋 系统功能:
2025-09-01 11:47:35 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:47:35 - INFO -   - 多线程并发处理
2025-09-01 11:47:35 - INFO -   - 实时进度监控
2025-09-01 11:47:35 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:47:35 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:47:35 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:47:35 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 11:47:35 - INFO - 任务管理已初始化
2025-09-01 11:47:42 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:47:42 - INFO - 📋 系统功能:
2025-09-01 11:47:42 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:47:42 - INFO -   - 多线程并发处理
2025-09-01 11:47:42 - INFO -   - 实时进度监控
2025-09-01 11:47:42 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:47:42 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:47:42 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:47:42 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:49:28 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:49:28 - INFO - 📋 系统功能:
2025-09-01 11:49:28 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:49:28 - INFO -   - 多线程并发处理
2025-09-01 11:49:28 - INFO -   - 实时进度监控
2025-09-01 11:49:28 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:49:28 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:49:28 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:49:28 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:49:41 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:49:41 - INFO - 📋 系统功能:
2025-09-01 11:49:41 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:49:41 - INFO -   - 多线程并发处理
2025-09-01 11:49:41 - INFO -   - 实时进度监控
2025-09-01 11:49:41 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:49:41 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:49:41 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:49:41 - INFO - 🔗 爬虫系统地址: http://localhost:8012
2025-09-01 11:49:41 - INFO - 任务管理已初始化
2025-09-01 11:49:45 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:49:45 - INFO - 📋 系统功能:
2025-09-01 11:49:45 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:49:45 - INFO -   - 多线程并发处理
2025-09-01 11:49:45 - INFO -   - 实时进度监控
2025-09-01 11:49:45 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:49:45 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:49:45 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:49:45 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:51:20 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:51:20 - INFO - 📋 系统功能:
2025-09-01 11:51:20 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:51:20 - INFO -   - 多线程并发处理
2025-09-01 11:51:20 - INFO -   - 实时进度监控
2025-09-01 11:51:20 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:51:20 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:51:20 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:51:20 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:53:16 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:53:16 - INFO - 📋 系统功能:
2025-09-01 11:53:16 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:53:16 - INFO -   - 多线程并发处理
2025-09-01 11:53:16 - INFO -   - 实时进度监控
2025-09-01 11:53:16 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:53:16 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:53:16 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:53:16 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:53:50 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:53:50 - INFO - 📋 系统功能:
2025-09-01 11:53:50 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:53:50 - INFO -   - 多线程并发处理
2025-09-01 11:53:50 - INFO -   - 实时进度监控
2025-09-01 11:53:50 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:53:50 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:53:50 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:53:50 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:53:59 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:53:59 - INFO - 📋 系统功能:
2025-09-01 11:53:59 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:53:59 - INFO -   - 多线程并发处理
2025-09-01 11:53:59 - INFO -   - 实时进度监控
2025-09-01 11:53:59 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:53:59 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:53:59 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:53:59 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:54:23 - INFO - 🚀 启动智能简历评估系统
2025-09-01 11:54:23 - INFO - 📋 系统功能:
2025-09-01 11:54:23 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 11:54:23 - INFO -   - 多线程并发处理
2025-09-01 11:54:23 - INFO -   - 实时进度监控
2025-09-01 11:54:23 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 11:54:23 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 11:54:23 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 11:54:23 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 11:54:27 - INFO - 🔐 统一登录请求: boss
2025-09-01 11:54:27 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 11:54:27 - INFO - DrissionPage浏览器初始化成功
2025-09-01 11:54:27 - INFO - 已访问登录页面，请手动登录
2025-09-01 11:54:27 - INFO - 等待用户登录...
2025-09-01 11:54:29 - INFO - 用户已登录
2025-09-01 11:54:29 - INFO - 开始提取用户信息
2025-09-01 11:54:29 - INFO - 用户名: 杨锐雄
2025-09-01 11:54:29 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 11:54:29 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 11:54:29 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:54:50 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-09-01 11:54:50 - INFO - ✅ 发现已存在的JSON文件: user\杨锐雄\jd\AI产品经理.json
2025-09-01 11:54:50 - INFO - ✅ JSON文件格式验证通过
2025-09-01 11:54:50 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-09-01 11:54:50 - INFO - ✅ 发现已存在的JSON文件: user\杨锐雄\jd\AI产品经理.json
2025-09-01 11:54:50 - INFO - ✅ JSON文件格式验证通过
2025-09-01 11:54:50 - INFO - 提取的岗位名称: 'AI产品经理'
2025-09-01 11:54:50 - INFO - 最终使用的岗位名称: 'AI产品经理'
2025-09-01 11:54:50 - INFO - ✅ 保存新候选人简历到VL目录: user\杨锐雄\vl\VL_Result_AI产品经理\陈明_VL识别结果.txt
2025-09-01 11:54:50 - INFO - 开始处理简历: 陈明
2025-09-01 11:54:50 - INFO - 开始第1轮评估: 陈明
2025-09-01 11:54:50 - INFO - 岗位名称: AI产品经理
2025-09-01 11:54:50 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 11:54:50 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 11:55:16 - INFO - ✅ 第1轮评估完成: 陈明 | 分数: 0.0
2025-09-01 11:55:16 - INFO - 检测到明确通过标识: ✅ 通过
2025-09-01 11:55:16 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：54_AI产品经理_一二点五三轮筛选结果/初筛合格\通过_陈明_第1轮_评估报告.md
2025-09-01 11:55:16 - INFO - 开始提取候选人 陈明 的初筛报告信息
2025-09-01 11:55:16 - INFO - 找到优势文本: **优势:**
    - 硕士学历，高于JD学历要求。
    - 拥有多个涉及AI系统设计的项目经验（如AUV、无人机），具备AI技术理解能力。
    - 多次参与完整项目周期，具备产品从需求到落地的全流程经验。
    - 具备良好的跨部门协作与项目管理能力。

- 
2025-09-01 11:55:16 - INFO - 找到风险文本: **风险与不足:**
    - 候选人未明确提供期望薪资，可能在后续沟通中存在不确定性。
    - 候选人过往岗位多为技术岗（算法、电气、自动化工程师），缺乏直接的“产品经理”岗位经历，需评估其是否具备产品战略思维和用户视角。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。 → 无  
    2. 核心职责匹配度 评估结果为 "**低**"。 → 无  

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。 → 满足  
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。 → 满足（评估为“高”）

**筛选结果：** ✅ 通过  
**筛选理由：** 候选人满足学历、年龄、薪资等所有硬性门槛要求，且在核心职责方面具备全流程产品开发经验、跨部门协作能力和AI技术理解能力。尽管其未直接担任过“产品经理”职务，但其技术背景与项目经验高度契合AI产品经理岗位的技术理解与产品落地要求，建议进入下一轮面试以进一步评估其产品思维与战略能力。
2025-09-01 11:55:16 - INFO - 提取的优势和风险: 优势=硕士学历，高于JD学历要求。；拥有多个涉及AI系统设计的项目经验（如AUV、无人机），具备AI技术理解能力。；多次参与完整项目周期，具备产品从需求到落地的全流程经验。, 风险=候选人未明确提供期望薪资，可能在后续沟通中存在不确定性。；候选人过往岗位多为技术岗（算法、电气、自动化工程师），缺乏直接的“产品经理”岗位经历，需评估其是否具备产品战略思维和用户视角。
2025-09-01 11:55:16 - INFO - 提取到姓名: 陈明
2025-09-01 11:55:16 - INFO - 提取到年龄: 48岁
2025-09-01 11:55:16 - INFO - 提取到工作经验: 约14年（2009年11月至今）
2025-09-01 11:55:16 - INFO - 提取到学历: 硕士
2025-09-01 11:55:16 - INFO - 提取到当前职位: 软件工程师V6
2025-09-01 11:55:16 - INFO - 提取到期望薪资: 未明确提供
2025-09-01 11:55:16 - INFO - 从初筛报告提取的基本信息: {'name': '陈明', 'age': '48岁', 'experience': '约14年（2009年11月至今）', 'education': '硕士', 'current_position': '软件工程师V6', 'expected_salary': '未明确提供'}
2025-09-01 11:55:16 - INFO - 候选人 陈明 初筛报告提取完成: advantages=硕士学历，高于JD学历要求。；拥有多个涉及AI系统设计的项目经验（如AUV、无人机），具备AI技术理解能力。；多次参与完整项目周期，具备产品从需求到落地的全流程经验。, risks=候选人未明确提供期望薪资，可能在后续沟通中存在不确定性。；候选人过往岗位多为技术岗（算法、电气、自动化工程师），缺乏直接的“产品经理”岗位经历，需评估其是否具备产品战略思维和用户视角。, basic_info={'name': '陈明', 'age': '48岁', 'experience': '约14年（2009年11月至今）', 'education': '硕士', 'current_position': '软件工程师V6', 'expected_salary': '未明确提供'}
2025-09-01 11:55:16 - INFO - 开始第2轮评估: 陈明
2025-09-01 11:55:16 - INFO - 岗位名称: AI产品经理
2025-09-01 11:55:16 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像分析.md
2025-09-01 11:55:16 - INFO - 第2轮评估使用模型: qwen-plus
2025-09-01 11:55:34 - WARNING - 分数不一致: 总分=85.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-09-01 11:55:34 - WARNING - 候选人陈明分数不一致: 总分=85.0, 计算总分=0.0
2025-09-01 11:55:34 - INFO - ✅ 第2轮评估完成: 陈明 | 分数: 85.0
2025-09-01 11:55:34 - INFO - 未检测到明确关键词，默认通过
2025-09-01 11:55:34 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：54_AI产品经理_一二点五三轮筛选结果/画像分析结果\陈明_第2轮_画像分析.md
2025-09-01 11:55:34 - INFO - 开始第2.5轮评估: 陈明
2025-09-01 11:55:34 - INFO - 岗位名称: AI产品经理
2025-09-01 11:55:34 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 11:55:34 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 11:55:34 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 11:55:37 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 11:55:40 - INFO - ✅ 第2.5轮评估完成: 陈明 | 分数: 0.0
2025-09-01 11:55:40 - INFO - 第2.5轮检测到明确不通过标识: ❌ 不通过
2025-09-01 11:55:40 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_11：54_AI产品经理_一二点五三轮筛选结果/二筛不合格\未通过_陈明_第2.5轮_评估报告.md
2025-09-01 11:55:40 - INFO - ❌ 陈明 未通过第2.5轮筛选，得分: 0.0
2025-09-01 11:56:59 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:02 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:14 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:15 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:24 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:25 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:52 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 11:57:52 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 12:01:05 - INFO - 🚀 启动智能简历评估系统
2025-09-01 12:01:05 - INFO - 📋 系统功能:
2025-09-01 12:01:05 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 12:01:05 - INFO -   - 多线程并发处理
2025-09-01 12:01:05 - INFO -   - 实时进度监控
2025-09-01 12:01:05 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 12:01:05 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 12:01:05 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 12:01:05 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 12:01:17 - INFO - 🔐 统一登录请求: normal
2025-09-01 12:01:17 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 12:01:17 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 12:01:17 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 12:01:31 - INFO - 🔐 统一登录请求: normal
2025-09-01 12:01:31 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 12:01:31 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 12:01:31 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:31:24 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:31:24 - INFO - 📋 系统功能:
2025-09-01 13:31:24 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:31:24 - INFO -   - 多线程并发处理
2025-09-01 13:31:24 - INFO -   - 实时进度监控
2025-09-01 13:31:24 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:31:24 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:31:24 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:31:24 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:31:33 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:31:33 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:31:33 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:31:34 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:31:34 - INFO - 等待用户登录...
2025-09-01 13:31:36 - INFO - 用户已登录
2025-09-01 13:31:36 - INFO - 开始提取用户信息
2025-09-01 13:31:36 - INFO - 用户名: 杨锐雄
2025-09-01 13:31:36 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:31:36 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:31:36 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:48 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:48 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:49 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:49 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:50 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:31:51 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:33:40 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:33:40 - INFO - 📋 系统功能:
2025-09-01 13:33:40 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:33:40 - INFO -   - 多线程并发处理
2025-09-01 13:33:40 - INFO -   - 实时进度监控
2025-09-01 13:33:40 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:33:40 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:33:40 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:33:40 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:33:46 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:33:46 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:33:46 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:33:46 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:33:46 - INFO - 等待用户登录...
2025-09-01 13:33:48 - INFO - 用户已登录
2025-09-01 13:33:48 - INFO - 开始提取用户信息
2025-09-01 13:33:48 - INFO - 用户名: 杨锐雄
2025-09-01 13:33:48 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:33:48 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:33:48 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:33:52 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:33:53 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:33:53 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:33:56 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:34:40 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:34:41 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:34:54 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:34:54 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:34:54 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:34:54 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:34:54 - INFO - 等待用户登录...
2025-09-01 13:34:56 - INFO - 用户已登录
2025-09-01 13:34:56 - INFO - 开始提取用户信息
2025-09-01 13:34:56 - INFO - 用户名: 杨锐雄
2025-09-01 13:34:56 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:34:56 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:34:56 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:36:23 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:36:23 - INFO - 📋 系统功能:
2025-09-01 13:36:23 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:36:23 - INFO -   - 多线程并发处理
2025-09-01 13:36:23 - INFO -   - 实时进度监控
2025-09-01 13:36:23 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:36:23 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:36:23 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:36:23 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:36:32 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:36:32 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:36:32 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:36:32 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:36:32 - INFO - 等待用户登录...
2025-09-01 13:36:34 - INFO - 用户已登录
2025-09-01 13:36:34 - INFO - 开始提取用户信息
2025-09-01 13:36:34 - INFO - 用户名: 杨锐雄
2025-09-01 13:36:34 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:36:34 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:36:34 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:36:59 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:37:04 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:37:05 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:37:08 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:37:09 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:39:09 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:39:09 - INFO - 📋 系统功能:
2025-09-01 13:39:09 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:39:09 - INFO -   - 多线程并发处理
2025-09-01 13:39:09 - INFO -   - 实时进度监控
2025-09-01 13:39:09 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:39:09 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:39:09 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:39:09 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:39:22 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:39:22 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:39:22 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:39:23 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:39:23 - INFO - 等待用户登录...
2025-09-01 13:39:31 - INFO - 用户已登录
2025-09-01 13:39:31 - INFO - 开始提取用户信息
2025-09-01 13:39:31 - INFO - 用户名: 杨锐雄
2025-09-01 13:39:31 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:39:31 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:39:42 - INFO - 未检测到弹窗
2025-09-01 13:39:42 - INFO - 开始提取职位信息...
2025-09-01 13:39:42 - INFO - 成功进入职位管理页面
2025-09-01 13:39:43 - INFO - 已切换到iframe
2025-09-01 13:39:43 - INFO - 一共获取到3个职位
2025-09-01 13:39:43 - INFO - 开始提取第1个职位信息...
2025-09-01 13:39:44 - INFO - 第1次进入iframe
2025-09-01 13:39:44 - INFO - 职位名称: AI全栈工程师
2025-09-01 13:39:45 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 13:39:45 - INFO - 第1个职位信息提取完成
2025-09-01 13:39:45 - INFO - 开始提取第2个职位信息...
2025-09-01 13:39:46 - INFO - 第2次进入iframe
2025-09-01 13:39:46 - INFO - 职位名称: AI产品经理
2025-09-01 13:39:47 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 13:39:47 - INFO - 第2个职位信息提取完成
2025-09-01 13:39:47 - INFO - 开始提取第3个职位信息...
2025-09-01 13:39:48 - INFO - 第3次进入iframe
2025-09-01 13:39:48 - INFO - 职位名称: 人力资源主管
2025-09-01 13:39:49 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 13:39:49 - INFO - 第3个职位信息提取完成
2025-09-01 13:39:49 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:39:49 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:39:58 - INFO - 用户 杨锐雄 删除了JD文件: AI全栈工程师.md
2025-09-01 13:40:07 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:40:07 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:40:07 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:40:07 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:40:14 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:40:14 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:40:14 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:40:14 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:40:14 - INFO - 等待用户登录...
2025-09-01 13:40:16 - INFO - 用户已登录
2025-09-01 13:40:16 - INFO - 开始提取用户信息
2025-09-01 13:40:16 - INFO - 用户名: 杨锐雄
2025-09-01 13:40:16 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:40:16 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:40:16 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:40:16 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:40:24 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:40:24 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:40:24 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:40:24 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:40:24 - INFO - 等待用户登录...
2025-09-01 13:40:26 - INFO - 用户已登录
2025-09-01 13:40:26 - INFO - 开始提取用户信息
2025-09-01 13:40:26 - INFO - 用户名: 杨锐雄
2025-09-01 13:40:26 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:40:26 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:40:26 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:40:26 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:40:42 - INFO - 未检测到弹窗
2025-09-01 13:40:42 - INFO - 开始提取职位信息...
2025-09-01 13:40:43 - INFO - 成功进入职位管理页面
2025-09-01 13:40:44 - INFO - 已切换到iframe
2025-09-01 13:40:44 - INFO - 一共获取到3个职位
2025-09-01 13:40:44 - INFO - 开始提取第1个职位信息...
2025-09-01 13:40:44 - INFO - 第1次进入iframe
2025-09-01 13:40:44 - INFO - 职位名称: AI全栈工程师
2025-09-01 13:40:46 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 13:40:46 - INFO - 第1个职位信息提取完成
2025-09-01 13:40:46 - INFO - 开始提取第2个职位信息...
2025-09-01 13:40:46 - INFO - 第2次进入iframe
2025-09-01 13:40:46 - INFO - 职位名称: AI产品经理
2025-09-01 13:40:48 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 13:40:48 - INFO - 第2个职位信息提取完成
2025-09-01 13:40:48 - INFO - 开始提取第3个职位信息...
2025-09-01 13:40:48 - INFO - 第3次进入iframe
2025-09-01 13:40:48 - INFO - 职位名称: 人力资源主管
2025-09-01 13:40:50 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 13:40:50 - INFO - 第3个职位信息提取完成
2025-09-01 13:41:21 - INFO - 🔐 统一登录请求: boss
2025-09-01 13:41:21 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 13:41:21 - INFO - DrissionPage浏览器初始化成功
2025-09-01 13:41:21 - INFO - 已访问登录页面，请手动登录
2025-09-01 13:41:21 - INFO - 等待用户登录...
2025-09-01 13:41:23 - INFO - 用户已登录
2025-09-01 13:41:23 - INFO - 开始提取用户信息
2025-09-01 13:41:23 - INFO - 用户名: 杨锐雄
2025-09-01 13:41:23 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 13:41:23 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 13:41:23 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:41:23 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:41:27 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-09-01 13:41:27 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\JD_md转json.md
2025-09-01 13:41:36 - INFO - ✅ 成功将MD格式JD转换为JSON格式并保存到用户目录: user\杨锐雄\jd\人力资源主管.json
2025-09-01 13:41:36 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:41:36 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:41:36 - INFO - 开始转换MD格式JD: 人力资源主管.md
2025-09-01 13:41:36 - INFO - ✅ 发现已存在的JSON文件: user\杨锐雄\jd\人力资源主管.json
2025-09-01 13:41:36 - INFO - ✅ JSON文件格式验证通过
2025-09-01 13:41:36 - INFO - 提取的岗位名称: '人力资源主管'
2025-09-01 13:41:36 - INFO - 最终使用的岗位名称: '人力资源主管'
2025-09-01 13:41:36 - INFO - ✅ 保存新候选人简历到VL目录: user\杨锐雄\vl\VL_Result_人力资源主管\曾杰_VL识别结果.txt
2025-09-01 13:41:36 - INFO - 开始处理简历: 曾杰
2025-09-01 13:41:36 - INFO - 开始第1轮评估: 曾杰
2025-09-01 13:41:36 - INFO - 岗位名称: 人力资源主管
2025-09-01 13:41:36 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 13:41:36 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 13:42:06 - INFO - ✅ 第1轮评估完成: 曾杰 | 分数: 0.0
2025-09-01 13:42:06 - INFO - 检测到不合格关键词: 淘汰
2025-09-01 13:42:06 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_13：41_人力资源主管_一二点五三轮筛选结果/初筛不合格\未通过_曾杰_第1轮_评估报告.md
2025-09-01 13:42:06 - INFO - 开始提取候选人 曾杰 的初筛报告信息
2025-09-01 13:42:06 - INFO - 找到优势文本: **优势:**  
    - 硕士学历，符合岗位学历要求。
    - 具备丰富的项目管理经验，熟悉产品设计与技术实施流程。

- 
2025-09-01 13:42:06 - INFO - 找到风险文本: **风险与不足:**  
    - 简历中**完全缺乏与人力资源相关的核心职责经验**，如招聘、培训、绩效、员工关系、薪酬体系等。
    - 所有工作经历集中在产品管理、技术实施与项目管理领域，与JD要求的HR主管岗位存在本质偏差。
    - 简历未提供期望薪资，虽默认匹配，但后续需确认是否在JD范围内。

**4. 初筛结论**

- **淘汰条件：**
    1. 核心职责匹配度评估结果为“低”。

- **通过条件：**
    1. 不满足。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 候选人具备硕士学历且年龄匹配，但**核心职责匹配度评估为“低”**，因其简历中**未体现任何人力资源管理相关的经验或职责**，与岗位要求存在本质不符，不具备胜任该岗位的基础能力。
2025-09-01 13:42:06 - INFO - 提取的优势和风险: 优势=硕士学历，符合岗位学历要求。；具备丰富的项目管理经验，熟悉产品设计与技术实施流程。, 风险=简历中**完全缺乏与人力资源相关的核心职责经验**，如招聘、培训、绩效、员工关系、薪酬体系等。；所有工作经历集中在产品管理、技术实施与项目管理领域，与JD要求的HR主管岗位存在本质偏差。
2025-09-01 13:42:06 - INFO - 提取到姓名: 曾杰
2025-09-01 13:42:06 - INFO - 提取到年龄: 38岁
2025-09-01 13:42:06 - INFO - 提取到工作经验: 10年以上
2025-09-01 13:42:06 - INFO - 提取到学历: 硕士
2025-09-01 13:42:06 - INFO - 提取到当前职位: 高职-腾讯研究院
2025-09-01 13:42:06 - INFO - 提取到期望薪资: 未明确提供（根据JD设定为默认匹配）
2025-09-01 13:42:06 - INFO - 从初筛报告提取的基本信息: {'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '高职-腾讯研究院', 'expected_salary': '未明确提供（根据JD设定为默认匹配）'}
2025-09-01 13:42:06 - INFO - 候选人 曾杰 初筛报告提取完成: advantages=硕士学历，符合岗位学历要求。；具备丰富的项目管理经验，熟悉产品设计与技术实施流程。, risks=简历中**完全缺乏与人力资源相关的核心职责经验**，如招聘、培训、绩效、员工关系、薪酬体系等。；所有工作经历集中在产品管理、技术实施与项目管理领域，与JD要求的HR主管岗位存在本质偏差。, basic_info={'name': '曾杰', 'age': '38岁', 'experience': '10年以上', 'education': '硕士', 'current_position': '高职-腾讯研究院', 'expected_salary': '未明确提供（根据JD设定为默认匹配）'}
2025-09-01 13:42:06 - INFO - ❌ 曾杰 未通过第1轮筛选，得分: 0.0
2025-09-01 13:42:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:09 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:42:31 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 13:43:01 - INFO - 未检测到弹窗
2025-09-01 13:43:01 - INFO - 开始提取职位信息...
2025-09-01 13:43:01 - INFO - 成功进入职位管理页面
2025-09-01 13:43:02 - INFO - 已切换到iframe
2025-09-01 13:43:02 - INFO - 一共获取到3个职位
2025-09-01 13:43:02 - INFO - 开始提取第1个职位信息...
2025-09-01 13:43:02 - INFO - 第1次进入iframe
2025-09-01 13:43:02 - INFO - 职位名称: AI全栈工程师
2025-09-01 13:43:04 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 13:43:04 - INFO - 第1个职位信息提取完成
2025-09-01 13:43:04 - INFO - 开始提取第2个职位信息...
2025-09-01 13:43:04 - INFO - 第2次进入iframe
2025-09-01 13:43:05 - INFO - 职位名称: AI产品经理
2025-09-01 13:43:06 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 13:43:06 - INFO - 第2个职位信息提取完成
2025-09-01 13:43:06 - INFO - 开始提取第3个职位信息...
2025-09-01 13:43:06 - INFO - 第3次进入iframe
2025-09-01 13:43:07 - INFO - 职位名称: 人力资源主管
2025-09-01 13:43:08 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 13:43:08 - INFO - 第3个职位信息提取完成
2025-09-01 13:43:08 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:43:08 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:43:08 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:43:08 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:43:08 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\未知用户\result
2025-09-01 13:43:08 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\未知用户\result
2025-09-01 13:43:09 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:43:09 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:43:09 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:43:09 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:43:29 - INFO - 用户 123 删除了JD文件: 售前_JD.md
2025-09-01 13:43:39 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:43:39 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:43:39 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:43:39 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:43:43 - INFO - 用户 123 删除了JD文件: 人力资源主管.md
2025-09-01 13:48:34 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:48:34 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:48:34 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:48:34 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:48:37 - INFO - 用户 123 删除了JD文件: AI售后_JD.md
2025-09-01 13:54:50 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:54:50 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:54:50 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:54:50 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:55:04 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:55:04 - INFO - 📋 系统功能:
2025-09-01 13:55:04 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:55:04 - INFO -   - 多线程并发处理
2025-09-01 13:55:04 - INFO -   - 实时进度监控
2025-09-01 13:55:04 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:55:04 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:55:04 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:55:04 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:55:10 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:55:10 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:55:10 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:55:10 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:55:23 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:55:23 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:55:23 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:55:23 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:58:59 - INFO - 🚀 启动智能简历评估系统
2025-09-01 13:58:59 - INFO - 📋 系统功能:
2025-09-01 13:58:59 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 13:58:59 - INFO -   - 多线程并发处理
2025-09-01 13:58:59 - INFO -   - 实时进度监控
2025-09-01 13:58:59 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 13:58:59 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 13:58:59 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 13:58:59 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 13:59:25 - INFO - 🔐 统一登录请求: normal
2025-09-01 13:59:25 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 13:59:25 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 13:59:25 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:02:04 - INFO - 🔐 统一登录请求: boss
2025-09-01 14:02:04 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 14:02:05 - INFO - DrissionPage浏览器初始化成功
2025-09-01 14:02:05 - INFO - 已访问登录页面，请手动登录
2025-09-01 14:02:05 - INFO - 等待用户登录...
2025-09-01 14:02:13 - INFO - 用户已登录
2025-09-01 14:02:13 - INFO - 开始提取用户信息
2025-09-01 14:02:13 - INFO - 用户名: 杨锐雄
2025-09-01 14:02:13 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 14:02:13 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 14:02:13 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 14:03:24 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 14:10:58 - INFO - 未检测到弹窗
2025-09-01 14:10:58 - INFO - 开始提取职位信息...
2025-09-01 14:10:58 - INFO - 成功进入职位管理页面
2025-09-01 14:11:00 - INFO - 已切换到iframe
2025-09-01 14:11:00 - INFO - 一共获取到3个职位
2025-09-01 14:11:00 - INFO - 开始提取第1个职位信息...
2025-09-01 14:11:00 - INFO - 第1次进入iframe
2025-09-01 14:11:00 - INFO - 职位名称: AI全栈工程师
2025-09-01 14:11:02 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 14:11:02 - INFO - 第1个职位信息提取完成
2025-09-01 14:11:02 - INFO - 开始提取第2个职位信息...
2025-09-01 14:11:02 - INFO - 第2次进入iframe
2025-09-01 14:11:02 - INFO - 职位名称: AI产品经理
2025-09-01 14:11:04 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 14:11:04 - INFO - 第2个职位信息提取完成
2025-09-01 14:11:04 - INFO - 开始提取第3个职位信息...
2025-09-01 14:11:04 - INFO - 第3次进入iframe
2025-09-01 14:11:04 - INFO - 职位名称: 人力资源主管
2025-09-01 14:11:06 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 14:11:06 - INFO - 第3个职位信息提取完成
2025-09-01 14:11:06 - INFO - 🔐 统一登录请求: normal
2025-09-01 14:11:06 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 14:11:08 - INFO - 🚀 启动智能简历评估系统
2025-09-01 14:11:08 - INFO - 📋 系统功能:
2025-09-01 14:11:08 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 14:11:08 - INFO -   - 多线程并发处理
2025-09-01 14:11:08 - INFO -   - 实时进度监控
2025-09-01 14:11:08 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 14:11:08 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 14:11:08 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 14:11:08 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 14:11:12 - INFO - 🔐 统一登录请求: normal
2025-09-01 14:11:12 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 14:11:12 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:11:12 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:11:27 - INFO - 🔐 统一登录请求: normal
2025-09-01 14:11:27 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 14:11:27 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:11:27 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:16:42 - INFO - 🚀 启动智能简历评估系统
2025-09-01 14:16:42 - INFO - 📋 系统功能:
2025-09-01 14:16:42 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 14:16:42 - INFO -   - 多线程并发处理
2025-09-01 14:16:42 - INFO -   - 实时进度监控
2025-09-01 14:16:42 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 14:16:42 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 14:16:42 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 14:16:42 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 14:16:45 - INFO - 🔐 统一登录请求: normal
2025-09-01 14:16:45 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 14:16:45 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:16:45 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:17:21 - INFO - 🔐 统一登录请求: normal
2025-09-01 14:17:21 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 14:17:21 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:17:21 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:18:11 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:18:11 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:18:13 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:18:13 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:18:14 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:18:14 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:26:30 - INFO - 🚀 启动智能简历评估系统
2025-09-01 14:26:30 - INFO - 📋 系统功能:
2025-09-01 14:26:30 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 14:26:30 - INFO -   - 多线程并发处理
2025-09-01 14:26:30 - INFO -   - 实时进度监控
2025-09-01 14:26:30 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 14:26:30 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 14:26:30 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 14:26:30 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 14:26:33 - INFO - 🔐 统一登录请求: normal
2025-09-01 14:26:33 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 14:26:34 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:26:34 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:29:32 - INFO - 🚀 启动智能简历评估系统
2025-09-01 14:29:32 - INFO - 📋 系统功能:
2025-09-01 14:29:32 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 14:29:32 - INFO -   - 多线程并发处理
2025-09-01 14:29:32 - INFO -   - 实时进度监控
2025-09-01 14:29:32 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 14:29:32 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 14:29:32 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 14:29:32 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 14:29:36 - INFO - 🔐 统一登录请求: normal
2025-09-01 14:29:36 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 14:29:36 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:29:36 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:29:38 - INFO - 用户 123 删除了JD文件: 预算员_JD.md
2025-09-01 14:29:40 - INFO - 用户 123 删除了JD文件: AI工程师_JD.md
2025-09-01 14:29:44 - INFO - 🔐 统一登录请求: boss
2025-09-01 14:29:44 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 14:29:44 - INFO - DrissionPage浏览器初始化成功
2025-09-01 14:29:45 - INFO - 已访问登录页面，请手动登录
2025-09-01 14:29:45 - INFO - 等待用户登录...
2025-09-01 14:29:47 - INFO - 用户已登录
2025-09-01 14:29:47 - INFO - 开始提取用户信息
2025-09-01 14:29:47 - INFO - 用户名: 杨锐雄
2025-09-01 14:29:47 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 14:29:47 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 14:29:47 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 14:30:04 - INFO - 未检测到弹窗
2025-09-01 14:30:04 - INFO - 开始提取职位信息...
2025-09-01 14:30:04 - INFO - 成功进入职位管理页面
2025-09-01 14:30:05 - INFO - 已切换到iframe
2025-09-01 14:30:05 - INFO - 一共获取到3个职位
2025-09-01 14:30:05 - INFO - 开始提取第1个职位信息...
2025-09-01 14:30:06 - INFO - 第1次进入iframe
2025-09-01 14:30:06 - INFO - 职位名称: AI全栈工程师
2025-09-01 14:30:07 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 14:30:07 - INFO - 第1个职位信息提取完成
2025-09-01 14:30:07 - INFO - 开始提取第2个职位信息...
2025-09-01 14:30:08 - INFO - 第2次进入iframe
2025-09-01 14:30:08 - INFO - 职位名称: AI产品经理
2025-09-01 14:30:09 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 14:30:09 - INFO - 第2个职位信息提取完成
2025-09-01 14:30:09 - INFO - 开始提取第3个职位信息...
2025-09-01 14:30:10 - INFO - 第3次进入iframe
2025-09-01 14:30:10 - INFO - 职位名称: 人力资源主管
2025-09-01 14:30:11 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 14:30:11 - INFO - 第3个职位信息提取完成
2025-09-01 14:30:46 - INFO - 未检测到弹窗
2025-09-01 14:30:46 - INFO - 开始提取职位信息...
2025-09-01 14:30:46 - INFO - 成功进入职位管理页面
2025-09-01 14:30:47 - INFO - 已切换到iframe
2025-09-01 14:30:47 - INFO - 一共获取到3个职位
2025-09-01 14:30:47 - INFO - 开始提取第1个职位信息...
2025-09-01 14:30:47 - INFO - 第1次进入iframe
2025-09-01 14:30:47 - INFO - 职位名称: AI全栈工程师
2025-09-01 14:30:49 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 14:30:49 - INFO - 第1个职位信息提取完成
2025-09-01 14:30:49 - INFO - 开始提取第2个职位信息...
2025-09-01 14:30:49 - INFO - 第2次进入iframe
2025-09-01 14:30:49 - INFO - 职位名称: AI产品经理
2025-09-01 14:30:51 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 14:30:51 - INFO - 第2个职位信息提取完成
2025-09-01 14:30:51 - INFO - 开始提取第3个职位信息...
2025-09-01 14:30:51 - INFO - 第3次进入iframe
2025-09-01 14:30:52 - INFO - 职位名称: 人力资源主管
2025-09-01 14:30:53 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 14:30:53 - INFO - 第3个职位信息提取完成
2025-09-01 14:30:53 - INFO - 🔐 统一登录请求: boss
2025-09-01 14:30:53 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 14:30:53 - INFO - DrissionPage浏览器初始化成功
2025-09-01 14:30:53 - INFO - 已访问登录页面，请手动登录
2025-09-01 14:30:53 - INFO - 等待用户登录...
2025-09-01 14:30:55 - INFO - 用户已登录
2025-09-01 14:30:55 - INFO - 开始提取用户信息
2025-09-01 14:30:55 - INFO - 用户名: 杨锐雄
2025-09-01 14:30:55 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 14:30:55 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 14:30:55 - INFO - 🔐 统一登录请求: boss
2025-09-01 14:30:55 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 14:30:55 - INFO - DrissionPage浏览器初始化成功
2025-09-01 14:30:56 - INFO - 已访问登录页面，请手动登录
2025-09-01 14:30:56 - INFO - 等待用户登录...
2025-09-01 14:30:58 - INFO - 用户已登录
2025-09-01 14:30:58 - INFO - 开始提取用户信息
2025-09-01 14:30:58 - INFO - 用户名: 杨锐雄
2025-09-01 14:30:58 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 14:30:58 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 14:30:58 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\未知用户\result
2025-09-01 14:30:58 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\未知用户\result
2025-09-01 14:31:16 - INFO - 🔐 统一登录请求: boss
2025-09-01 14:31:16 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 14:31:16 - INFO - DrissionPage浏览器初始化成功
2025-09-01 14:31:16 - INFO - 已访问登录页面，请手动登录
2025-09-01 14:31:16 - INFO - 等待用户登录...
2025-09-01 14:31:18 - INFO - 用户已登录
2025-09-01 14:31:18 - INFO - 开始提取用户信息
2025-09-01 14:31:18 - INFO - 用户名: 杨锐雄
2025-09-01 14:31:18 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 14:31:18 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 14:31:18 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 14:31:32 - INFO - 未检测到弹窗
2025-09-01 14:31:32 - INFO - 开始提取职位信息...
2025-09-01 14:31:32 - INFO - 成功进入职位管理页面
2025-09-01 14:31:34 - INFO - 已切换到iframe
2025-09-01 14:31:34 - INFO - 一共获取到3个职位
2025-09-01 14:31:34 - INFO - 开始提取第1个职位信息...
2025-09-01 14:31:34 - INFO - 第1次进入iframe
2025-09-01 14:31:34 - INFO - 职位名称: AI全栈工程师
2025-09-01 14:31:36 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 14:31:36 - INFO - 第1个职位信息提取完成
2025-09-01 14:31:36 - INFO - 开始提取第2个职位信息...
2025-09-01 14:31:36 - INFO - 第2次进入iframe
2025-09-01 14:31:36 - INFO - 职位名称: AI产品经理
2025-09-01 14:31:38 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 14:31:38 - INFO - 第2个职位信息提取完成
2025-09-01 14:31:38 - INFO - 开始提取第3个职位信息...
2025-09-01 14:31:38 - INFO - 第3次进入iframe
2025-09-01 14:31:38 - INFO - 职位名称: 人力资源主管
2025-09-01 14:31:40 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 14:31:40 - INFO - 第3个职位信息提取完成
2025-09-01 14:36:18 - INFO - 🚀 启动智能简历评估系统
2025-09-01 14:36:18 - INFO - 📋 系统功能:
2025-09-01 14:36:18 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 14:36:18 - INFO -   - 多线程并发处理
2025-09-01 14:36:18 - INFO -   - 实时进度监控
2025-09-01 14:36:18 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 14:36:18 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 14:36:18 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 14:36:18 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 14:36:22 - INFO - 🔐 统一登录请求: normal
2025-09-01 14:36:22 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 14:36:22 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:36:22 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:36:24 - INFO - 🔐 统一登录请求: boss
2025-09-01 14:36:24 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 14:36:24 - INFO - DrissionPage浏览器初始化成功
2025-09-01 14:36:25 - INFO - 已访问登录页面，请手动登录
2025-09-01 14:36:25 - INFO - 等待用户登录...
2025-09-01 14:36:27 - INFO - 用户已登录
2025-09-01 14:36:27 - INFO - 开始提取用户信息
2025-09-01 14:36:27 - INFO - 用户名: 杨锐雄
2025-09-01 14:36:27 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 14:36:27 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 14:36:27 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 14:36:42 - INFO - 未检测到弹窗
2025-09-01 14:36:42 - INFO - 开始提取职位信息...
2025-09-01 14:36:42 - INFO - 成功进入职位管理页面
2025-09-01 14:36:43 - INFO - 已切换到iframe
2025-09-01 14:36:43 - INFO - 一共获取到3个职位
2025-09-01 14:36:43 - INFO - 开始提取第1个职位信息...
2025-09-01 14:36:43 - INFO - 第1次进入iframe
2025-09-01 14:36:43 - INFO - 职位名称: AI全栈工程师
2025-09-01 14:36:45 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 14:36:45 - INFO - 第1个职位信息提取完成
2025-09-01 14:36:45 - INFO - 开始提取第2个职位信息...
2025-09-01 14:36:45 - INFO - 第2次进入iframe
2025-09-01 14:36:46 - INFO - 职位名称: AI产品经理
2025-09-01 14:36:47 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 14:36:47 - INFO - 第2个职位信息提取完成
2025-09-01 14:36:47 - INFO - 开始提取第3个职位信息...
2025-09-01 14:36:48 - INFO - 第3次进入iframe
2025-09-01 14:36:48 - INFO - 职位名称: 人力资源主管
2025-09-01 14:36:49 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 14:36:49 - INFO - 第3个职位信息提取完成
2025-09-01 14:38:03 - INFO - 🚀 启动智能简历评估系统
2025-09-01 14:38:03 - INFO - 📋 系统功能:
2025-09-01 14:38:03 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 14:38:03 - INFO -   - 多线程并发处理
2025-09-01 14:38:03 - INFO -   - 实时进度监控
2025-09-01 14:38:03 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 14:38:03 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 14:38:03 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 14:38:03 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 14:38:08 - INFO - 🔐 统一登录请求: boss
2025-09-01 14:38:08 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 14:38:08 - INFO - DrissionPage浏览器初始化成功
2025-09-01 14:38:08 - INFO - 已访问登录页面，请手动登录
2025-09-01 14:38:08 - INFO - 等待用户登录...
2025-09-01 14:38:10 - INFO - 用户已登录
2025-09-01 14:38:10 - INFO - 开始提取用户信息
2025-09-01 14:38:10 - INFO - 用户名: 杨锐雄
2025-09-01 14:38:10 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 14:38:10 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 14:38:10 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 14:38:24 - INFO - 未检测到弹窗
2025-09-01 14:38:24 - INFO - 开始提取职位信息...
2025-09-01 14:38:24 - INFO - 成功进入职位管理页面
2025-09-01 14:38:25 - INFO - 已切换到iframe
2025-09-01 14:38:25 - INFO - 一共获取到3个职位
2025-09-01 14:38:25 - INFO - 开始提取第1个职位信息...
2025-09-01 14:38:26 - INFO - 第1次进入iframe
2025-09-01 14:38:26 - INFO - 职位名称: AI全栈工程师
2025-09-01 14:38:27 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 14:38:27 - INFO - 第1个职位信息提取完成
2025-09-01 14:38:27 - INFO - 开始提取第2个职位信息...
2025-09-01 14:38:27 - INFO - 第2次进入iframe
2025-09-01 14:38:28 - INFO - 职位名称: AI产品经理
2025-09-01 14:38:29 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 14:38:29 - INFO - 第2个职位信息提取完成
2025-09-01 14:38:29 - INFO - 开始提取第3个职位信息...
2025-09-01 14:38:30 - INFO - 第3次进入iframe
2025-09-01 14:38:30 - INFO - 职位名称: 人力资源主管
2025-09-01 14:38:31 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 14:38:31 - INFO - 第3个职位信息提取完成
2025-09-01 14:39:18 - INFO - 🚀 启动智能简历评估系统
2025-09-01 14:39:18 - INFO - 📋 系统功能:
2025-09-01 14:39:18 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 14:39:18 - INFO -   - 多线程并发处理
2025-09-01 14:39:18 - INFO -   - 实时进度监控
2025-09-01 14:39:18 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 14:39:18 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 14:39:18 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 14:39:18 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 14:39:22 - INFO - 🔐 统一登录请求: boss
2025-09-01 14:39:22 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 14:39:22 - INFO - DrissionPage浏览器初始化成功
2025-09-01 14:39:22 - INFO - 已访问登录页面，请手动登录
2025-09-01 14:39:22 - INFO - 等待用户登录...
2025-09-01 14:39:24 - INFO - 用户已登录
2025-09-01 14:39:24 - INFO - 开始提取用户信息
2025-09-01 14:39:24 - INFO - 用户名: 杨锐雄
2025-09-01 14:39:24 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 14:39:24 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 14:39:24 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 14:39:36 - INFO - 未检测到弹窗
2025-09-01 14:39:36 - INFO - 开始提取职位信息...
2025-09-01 14:39:37 - INFO - 成功进入职位管理页面
2025-09-01 14:39:38 - INFO - 已切换到iframe
2025-09-01 14:39:38 - INFO - 一共获取到3个职位
2025-09-01 14:39:38 - INFO - 开始提取第1个职位信息...
2025-09-01 14:39:38 - INFO - 第1次进入iframe
2025-09-01 14:39:38 - INFO - 职位名称: AI全栈工程师
2025-09-01 14:39:40 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 14:39:40 - INFO - 第1个职位信息提取完成
2025-09-01 14:39:40 - INFO - 开始提取第2个职位信息...
2025-09-01 14:39:40 - INFO - 第2次进入iframe
2025-09-01 14:39:40 - INFO - 职位名称: AI产品经理
2025-09-01 14:39:42 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 14:39:42 - INFO - 第2个职位信息提取完成
2025-09-01 14:39:42 - INFO - 开始提取第3个职位信息...
2025-09-01 14:39:42 - INFO - 第3次进入iframe
2025-09-01 14:39:42 - INFO - 职位名称: 人力资源主管
2025-09-01 14:39:44 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 14:39:44 - INFO - 第3个职位信息提取完成
2025-09-01 14:40:37 - INFO - 🚀 启动智能简历评估系统
2025-09-01 14:40:37 - INFO - 📋 系统功能:
2025-09-01 14:40:37 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 14:40:37 - INFO -   - 多线程并发处理
2025-09-01 14:40:37 - INFO -   - 实时进度监控
2025-09-01 14:40:37 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 14:40:37 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 14:40:37 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 14:40:37 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 14:40:42 - INFO - 🔐 统一登录请求: normal
2025-09-01 14:40:42 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 14:40:42 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:40:42 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 14:40:44 - INFO - 🔐 统一登录请求: boss
2025-09-01 14:40:44 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 14:40:44 - INFO - DrissionPage浏览器初始化成功
2025-09-01 14:40:45 - INFO - 已访问登录页面，请手动登录
2025-09-01 14:40:45 - INFO - 等待用户登录...
2025-09-01 14:40:47 - INFO - 用户已登录
2025-09-01 14:40:47 - INFO - 开始提取用户信息
2025-09-01 14:40:47 - INFO - 用户名: 杨锐雄
2025-09-01 14:40:47 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 14:40:47 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 14:40:47 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 14:40:59 - INFO - 未检测到弹窗
2025-09-01 14:40:59 - INFO - 开始提取职位信息...
2025-09-01 14:40:59 - INFO - 成功进入职位管理页面
2025-09-01 14:41:01 - INFO - 已切换到iframe
2025-09-01 14:41:01 - INFO - 一共获取到3个职位
2025-09-01 14:41:01 - INFO - 开始提取第1个职位信息...
2025-09-01 14:41:01 - INFO - 第1次进入iframe
2025-09-01 14:41:01 - INFO - 职位名称: AI全栈工程师
2025-09-01 14:41:03 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 14:41:03 - INFO - 第1个职位信息提取完成
2025-09-01 14:41:03 - INFO - 开始提取第2个职位信息...
2025-09-01 14:41:03 - INFO - 第2次进入iframe
2025-09-01 14:41:03 - INFO - 职位名称: AI产品经理
2025-09-01 14:41:05 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 14:41:05 - INFO - 第2个职位信息提取完成
2025-09-01 14:41:05 - INFO - 开始提取第3个职位信息...
2025-09-01 14:41:05 - INFO - 第3次进入iframe
2025-09-01 14:41:05 - INFO - 职位名称: 人力资源主管
2025-09-01 14:41:07 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 14:41:07 - INFO - 第3个职位信息提取完成
2025-09-01 14:46:09 - INFO - 用户 杨锐雄 删除了JD文件: AI全栈工程师.md
2025-09-01 14:46:21 - INFO - 未检测到弹窗
2025-09-01 14:46:21 - INFO - 开始提取职位信息...
2025-09-01 14:46:21 - INFO - 成功进入职位管理页面
2025-09-01 14:46:22 - INFO - 已切换到iframe
2025-09-01 14:46:22 - INFO - 一共获取到3个职位
2025-09-01 14:46:22 - INFO - 开始提取第1个职位信息...
2025-09-01 14:46:23 - INFO - 第1次进入iframe
2025-09-01 14:46:23 - INFO - 职位名称: AI全栈工程师
2025-09-01 14:46:24 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 14:46:24 - INFO - 第1个职位信息提取完成
2025-09-01 14:46:24 - INFO - 开始提取第2个职位信息...
2025-09-01 14:46:25 - INFO - 第2次进入iframe
2025-09-01 14:46:25 - INFO - 职位名称: AI产品经理
2025-09-01 14:46:27 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 14:46:27 - INFO - 第2个职位信息提取完成
2025-09-01 14:46:27 - INFO - 开始提取第3个职位信息...
2025-09-01 14:46:27 - INFO - 第3次进入iframe
2025-09-01 14:46:27 - INFO - 职位名称: 人力资源主管
2025-09-01 14:46:29 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 14:46:29 - INFO - 第3个职位信息提取完成
2025-09-01 15:19:58 - INFO - 🚀 启动智能简历评估系统
2025-09-01 15:19:58 - INFO - 📋 系统功能:
2025-09-01 15:19:58 - INFO -   - 支持TXT、PDF、图片格式简历
2025-09-01 15:19:58 - INFO -   - 多线程并发处理
2025-09-01 15:19:58 - INFO -   - 实时进度监控
2025-09-01 15:19:58 - INFO -   - 集成Boss直聘爬虫系统
2025-09-01 15:19:58 - INFO - 📱 前端访问地址: http://localhost:8008
2025-09-01 15:19:58 - INFO -  - JD来源: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\default\jd\人力资源主管_JD.md
2025-09-01 15:19:58 - INFO - 🔗 爬虫系统地址: http://localhost:8000
2025-09-01 15:20:02 - INFO - 🔐 统一登录请求: normal
2025-09-01 15:20:02 - INFO - ✅ 普通用户 123 登录成功
2025-09-01 15:20:02 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:02 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:03 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:03 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:04 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:04 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:06 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:06 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:06 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:07 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:07 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:07 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:07 - WARNING - Result文件夹不存在: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\123\result
2025-09-01 15:20:12 - INFO - 🔐 统一登录请求: boss
2025-09-01 15:20:12 - INFO - 正在初始化DrissionPage浏览器...
2025-09-01 15:20:12 - INFO - DrissionPage浏览器初始化成功
2025-09-01 15:20:12 - INFO - 已访问登录页面，请手动登录
2025-09-01 15:20:12 - INFO - 等待用户登录...
2025-09-01 15:20:14 - INFO - 用户已登录
2025-09-01 15:20:14 - INFO - 开始提取用户信息
2025-09-01 15:20:14 - INFO - 用户名: 杨锐雄
2025-09-01 15:20:14 - INFO - 头像URL: https://img.bosszhipin.com/boss/avatar/avatar_15.png
2025-09-01 15:20:14 - INFO - ✅ Boss用户登录成功，用户信息: 杨锐雄
2025-09-01 15:20:14 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 15:20:47 - INFO - 用户 杨锐雄 删除了JD文件: AI全栈工程师.md
2025-09-01 15:20:59 - INFO - 未检测到弹窗
2025-09-01 15:20:59 - INFO - 开始提取职位信息...
2025-09-01 15:20:59 - INFO - 成功进入职位管理页面
2025-09-01 15:21:01 - INFO - 已切换到iframe
2025-09-01 15:21:01 - INFO - 一共获取到3个职位
2025-09-01 15:21:01 - INFO - 开始提取第1个职位信息...
2025-09-01 15:21:01 - INFO - 第1次进入iframe
2025-09-01 15:21:01 - INFO - 职位名称: AI全栈工程师
2025-09-01 15:21:03 - INFO - 岗位信息: {'职位索引': 1, '职位名称': 'AI全栈工程师', '工作地点': '珠海', '工作经验': '1-3年', '学历要求': '本科', '薪资范围': '15-20K', '招聘类型': '全职', '职位详情': '岗位职责:\n1. 负责AI全栈工程相关工作\n2. 与团队合作，确保项目按时完成\n3. 持续优化工作流程，提升工作效率\n\n任职要求:\n1. 具备出色的沟通能力，能够有效协调项目资源\n2. 能够快速适应新环境，融入多功能团队\n3. 对AI技术有深入理解，能够不断学习和应用新知识'}
2025-09-01 15:21:03 - INFO - 第1个职位信息提取完成
2025-09-01 15:21:03 - INFO - 开始提取第2个职位信息...
2025-09-01 15:21:03 - INFO - 第2次进入iframe
2025-09-01 15:21:03 - INFO - 职位名称: AI产品经理
2025-09-01 15:21:05 - INFO - 岗位信息: {'职位索引': 2, '职位名称': 'AI产品经理', '工作地点': '珠海', '工作经验': '3-5年', '学历要求': '本科', '薪资范围': '14-20K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 负责AI技术在图帕斯户外产品中的应用规划，推动智能户外装备的创新与发展；\n2. 深入理解户外用户需求，结合AI技术提出创新解决方案，提升产品智能化水平；\n3. 协调跨部门资源，确保AI功能从概念到上线的全流程高效执行；\n4. 持续跟踪市场趋势和技术发展，为产品迭代提供科学依据；\n5. 与研发、设计、市场团队紧密合作，优化产品功能，增强用户体验。\n\n任职要求：\n1. 对AI技术及其应用场景有深刻理解，具备相关项目经验者优先；\n2. 熟悉户外用品市场，了解用户痛点和需求，能够转化为产品特性；\n3. 优秀的跨部门沟通与协调能力，能够推动多方合作达成目标；\n4. 强烈的责任心和执行力，能够在快节奏环境中解决问题；\n5. 具备良好的数据分析能力，能够通过数据驱动产品决策。'}
2025-09-01 15:21:05 - INFO - 第2个职位信息提取完成
2025-09-01 15:21:05 - INFO - 开始提取第3个职位信息...
2025-09-01 15:21:05 - INFO - 第3次进入iframe
2025-09-01 15:21:05 - INFO - 职位名称: 人力资源主管
2025-09-01 15:21:07 - INFO - 岗位信息: {'职位索引': 3, '职位名称': '人力资源主管', '工作地点': '珠海', '工作经验': '5-10年', '学历要求': '本科', '薪资范围': '6-7K', '招聘类型': '全职', '职位详情': '岗位职责：\n1. 统筹公司人力资源管理工作，确保人力资源政策的有效执行；\n2. 负责招聘、培训、绩效评估及员工关系等全员工生命周期的管理；\n3. 设计和优化薪酬福利体系，提升员工满意度和公司竞争力；\n4. 参与制定人力资源战略规划，支持公司业务发展及组织变革；\n5. 监控部门运作效率，提出改进建议，优化工作流程。\n\n任职要求：\n1. 人力资源管理或相关专业本科及以上学历；\n2. 5年以上人力资源相关工作经验，有主管或以上职位经验；\n3. 熟悉人力资源各大模块，具备优秀的人才招聘、培训及绩效管理能力；\n4. 具备良好的沟通协调能力和团队领导力，能够处理复杂的人事问题；\n5. 对人力资源法律法规有深入了解，能够为公司提供合规的人力资源解决方案。'}
2025-09-01 15:21:07 - INFO - 第3个职位信息提取完成
2025-09-01 15:21:38 - INFO - 查找历史记录路径: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\result
2025-09-01 15:21:46 - INFO - 爬取任务已启动: 73eae677-0493-4cdc-bfca-7223ed74129e, 职位: AI产品经理
2025-09-01 15:21:46 - INFO - 🚀 开始调用爬虫程序: 73eae677-0493-4cdc-bfca-7223ed74129e, 职位: AI产品经理, 目标数量: 1
2025-09-01 15:21:46 - INFO - 🔍 检查岗位匹配...
2025-09-01 15:22:13 - INFO - 岗位匹配结果: {'success': True, 'matched': True, 'current_job': 'AI产品经理', 'message': '成功切换到岗位: AI产品经理'}
2025-09-01 15:22:13 - INFO - ✅ 岗位匹配成功，开始爬取简历...
2025-09-01 15:22:13 - INFO - 爬取任务启动结果: {'task_id': 'task_1', 'message': '收集任务已启动'}
2025-09-01 15:22:13 - INFO - ✅ 爬取任务已启动: task_1
2025-09-01 15:22:13 - INFO - 开始监控爬取进度: task_1
2025-09-01 15:22:23 - ERROR - 监控爬取进度异常: 
2025-09-01 15:22:44 - ERROR - 监控爬取进度异常: 
2025-09-01 15:22:54 - INFO - 爬取状态: {'task_id': 'task_1', 'status': 'completed', 'progress': 100, 'results_count': 13, 'target_count': 1, 'current_action': '收集完成', 'current_resumes': [{'filename': '刘俊锋_AI产品经理_20250825_203837.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\刘俊锋_AI产品经理_20250825_203837.png', 'size': 89702, 'created_time': 1756125486.0, 'candidate_name': '刘俊锋'}, {'filename': '姚博_AI产品经理_20250827_161710.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250827_161710.png', 'size': 257381, 'created_time': 1756282632.0, 'candidate_name': '姚博'}, {'filename': '林丽丽_AI产品经理_20250827_161811.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林丽丽_AI产品经理_20250827_161811.png', 'size': 217050, 'created_time': 1756282692.0, 'candidate_name': '林丽丽'}, {'filename': '杨志海_AI产品经理_20250827_161912.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\杨志海_AI产品经理_20250827_161912.png', 'size': 179759, 'created_time': 1756282754.0, 'candidate_name': '杨志海'}, {'filename': '丁嘉亮_AI产品经理_20250827_162012.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丁嘉亮_AI产品经理_20250827_162012.png', 'size': 212004, 'created_time': 1756282814.0, 'candidate_name': '丁嘉亮'}, {'filename': '潘玉玲_AI产品经理_20250827_162151.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\潘玉玲_AI产品经理_20250827_162151.png', 'size': 1196651, 'created_time': 1756282912.0, 'candidate_name': '潘玉玲'}, {'filename': '丘伟航_AI产品经理_20250827_162258.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\丘伟航_AI产品经理_20250827_162258.png', 'size': 363723, 'created_time': 1756282980.0, 'candidate_name': '丘伟航'}, {'filename': '谭文杰_AI产品经理_20250827_162357.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\谭文杰_AI产品经理_20250827_162357.png', 'size': 173111, 'created_time': 1756283040.0, 'candidate_name': '谭文杰'}, {'filename': '叶利广_AI产品经理_20250827_162457.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250827_162457.png', 'size': 181559, 'created_time': 1756283098.0, 'candidate_name': '叶利广'}, {'filename': '张轩颖_AI产品经理_20250827_162556.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\张轩颖_AI产品经理_20250827_162556.png', 'size': 191507, 'created_time': 1756283158.0, 'candidate_name': '张轩颖'}, {'filename': '林先生_AI产品经理_20250827_162700.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\林先生_AI产品经理_20250827_162700.png', 'size': 278584, 'created_time': 1756283222.0, 'candidate_name': '林先生'}, {'filename': '姚博_AI产品经理_20250901_105002.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\姚博_AI产品经理_20250901_105002.png', 'size': 81500, 'created_time': 1756695002.581857, 'candidate_name': '姚博'}, {'filename': '叶利广_AI产品经理_20250901_152235.png', 'file_path': 'D:\\project\\智能直聘系统_v2.0.0\\智能招聘系统_version1.0.2 - 简化\\high_rpa_boss\\resumes\\AI产品经理\\叶利广_AI产品经理_20250901_152235.png', 'size': 174150, 'created_time': 1756711355.1884563, 'candidate_name': '叶利广'}]}
2025-09-01 15:22:54 - INFO - 爬取完成，获取简历列表...
2025-09-01 15:22:54 - INFO - 获取到 13 份简历
2025-09-01 15:22:54 - INFO - 爬取任务完成: 73eae677-0493-4cdc-bfca-7223ed74129e, 共获取 13 份简历
2025-09-01 15:22:56 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-09-01 15:22:56 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\JD_md转json.md
2025-09-01 15:22:58 - INFO - ✅ 成功将MD格式JD转换为JSON格式并保存到用户目录: user\杨锐雄\jd\AI产品经理.json
2025-09-01 15:24:48 - INFO - VL识别成功，识别内容长度: 22552
2025-09-01 15:24:48 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/叶利广_VL识别结果.txt
2025-09-01 15:24:57 - INFO - VL识别成功，识别内容长度: 1225
2025-09-01 15:24:57 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/姚博_VL识别结果.txt
2025-09-01 15:25:22 - INFO - VL识别成功，识别内容长度: 3547
2025-09-01 15:25:22 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/林先生_VL识别结果.txt
2025-09-01 15:25:46 - INFO - VL识别成功，识别内容长度: 3612
2025-09-01 15:25:46 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/张轩颖_VL识别结果.txt
2025-09-01 15:25:59 - INFO - VL识别成功，识别内容长度: 2287
2025-09-01 15:25:59 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/叶利广_VL识别结果.txt
2025-09-01 15:27:54 - INFO - VL识别成功，识别内容长度: 19549
2025-09-01 15:27:54 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/谭文杰_VL识别结果.txt
2025-09-01 15:28:11 - INFO - VL识别成功，识别内容长度: 2424
2025-09-01 15:28:11 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/丘伟航_VL识别结果.txt
2025-09-01 15:28:18 - INFO - VL识别成功，识别内容长度: 858
2025-09-01 15:28:18 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/潘玉玲_VL识别结果.txt
2025-09-01 15:28:48 - INFO - VL识别成功，识别内容长度: 3874
2025-09-01 15:28:48 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/丁嘉亮_VL识别结果.txt
2025-09-01 15:30:24 - INFO - VL识别成功，识别内容长度: 16503
2025-09-01 15:30:24 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/杨志海_VL识别结果.txt
2025-09-01 15:30:51 - INFO - VL识别成功，识别内容长度: 4873
2025-09-01 15:30:51 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/林丽丽_VL识别结果.txt
2025-09-01 15:31:16 - INFO - VL识别成功，识别内容长度: 3630
2025-09-01 15:31:16 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/姚博_VL识别结果.txt
2025-09-01 15:31:23 - INFO - VL识别成功，识别内容长度: 760
2025-09-01 15:31:23 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_人力资源主管/刘俊锋_VL识别结果.txt
2025-09-01 15:31:23 - INFO - 开始转换MD格式JD: AI产品经理.md
2025-09-01 15:31:23 - INFO - ✅ 发现已存在的JSON文件: user\杨锐雄\jd\AI产品经理.json
2025-09-01 15:31:23 - INFO - ✅ JSON文件格式验证通过
2025-09-01 15:31:23 - INFO - 提取的岗位名称: '未提及'
2025-09-01 15:31:23 - INFO - 最终使用的岗位名称: '未提及'
2025-09-01 15:33:17 - INFO - VL识别成功，识别内容长度: 22318
2025-09-01 15:33:17 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/叶利广_VL识别结果.txt
2025-09-01 15:33:17 - INFO - 开始处理简历: 叶利广
2025-09-01 15:33:17 - INFO - 开始第1轮评估: 叶利广
2025-09-01 15:33:17 - INFO - 岗位名称: 未提及
2025-09-01 15:33:17 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:33:17 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:33:52 - INFO - ✅ 第1轮评估完成: 叶利广 | 分数: 0.0
2025-09-01 15:33:52 - INFO - 检测到明确通过标识: ✅ 通过
2025-09-01 15:33:52 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛合格\通过_叶利广_第1轮_评估报告.md
2025-09-01 15:33:52 - INFO - 开始提取候选人 叶利广 的初筛报告信息
2025-09-01 15:33:52 - INFO - 找到优势文本: **优势:**
    1. 具备扎实的 AI 产品开发经验，熟悉从需求分析到上线的全流程管理。
    2. 拥有 AIGC、大模型、交互设计等前沿技术背景，硕士研究方向与实际工作高度结合。
    3. 具备创新与落地能力，拥有多个专利申请与成功项目经验。

- 
2025-09-01 15:33:52 - INFO - 找到优势文本: 优势:**
    1. 具备扎实的 AI 产品开发经验，熟悉从需求分析到上线的全流程管理。
    2. 拥有 AIGC、大模型、交互设计等前沿技术背景，硕士研究方向与实际工作高度结合。
    3. 具备创新与落地能力，拥有多个专利申请与成功项目经验。

- **
2025-09-01 15:33:52 - INFO - 找到风险文本: **风险与不足:**
    - 由于 JD 未提供具体岗位名称、核心职责、薪资范围、学历及年龄要求等关键信息，无法进行完整匹配评估。
    - 缺乏对岗位具体技能要求的对照，无法判断其硬技能是否完全匹配。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** 【✅ 通过】
**筛选理由：** 硬性门槛字段（学历、年龄、薪资）均未提供明确要求，候选人信息完整且符合一般高端岗位要求。由于 JD 信息不完整，核心职责与技能要求缺失，无法进一步淘汰。建议补充完整 JD 后进行二次评估。
2025-09-01 15:33:52 - INFO - 提取的优势和风险: 优势=**, 风险=由于 JD 未提供具体岗位名称、核心职责、薪资范围、学历及年龄要求等关键信息，无法进行完整匹配评估。；缺乏对岗位具体技能要求的对照，无法判断其硬技能是否完全匹配。
2025-09-01 15:33:52 - INFO - 提取到姓名: 叶利广
2025-09-01 15:33:52 - INFO - 提取到年龄: 26岁
2025-09-01 15:33:52 - INFO - 提取到工作经验: 3年
2025-09-01 15:33:52 - INFO - 提取到学历: 硕士
2025-09-01 15:33:52 - INFO - 提取到当前职位: AI产品经理
2025-09-01 15:33:52 - INFO - 提取到期望薪资: 未提及
2025-09-01 15:33:52 - INFO - 从初筛报告提取的基本信息: {'name': '叶利广', 'age': '26岁', 'experience': '3年', 'education': '硕士', 'current_position': 'AI产品经理', 'expected_salary': '未提及'}
2025-09-01 15:33:52 - INFO - 候选人 叶利广 初筛报告提取完成: advantages=**, risks=由于 JD 未提供具体岗位名称、核心职责、薪资范围、学历及年龄要求等关键信息，无法进行完整匹配评估。；缺乏对岗位具体技能要求的对照，无法判断其硬技能是否完全匹配。, basic_info={'name': '叶利广', 'age': '26岁', 'experience': '3年', 'education': '硕士', 'current_position': 'AI产品经理', 'expected_salary': '未提及'}
2025-09-01 15:33:52 - INFO - 开始第2轮评估: 叶利广
2025-09-01 15:33:52 - INFO - 岗位名称: 未提及
2025-09-01 15:33:52 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像分析.md
2025-09-01 15:33:52 - INFO - 第2轮评估使用模型: qwen-plus
2025-09-01 15:34:20 - WARNING - 分数不一致: 总分=85.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-09-01 15:34:20 - WARNING - 候选人叶利广分数不一致: 总分=85.0, 计算总分=0.0
2025-09-01 15:34:20 - INFO - ✅ 第2轮评估完成: 叶利广 | 分数: 85.0
2025-09-01 15:34:20 - INFO - 未检测到明确关键词，默认通过
2025-09-01 15:34:20 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/画像分析结果\叶利广_第2轮_画像分析.md
2025-09-01 15:34:20 - INFO - 开始第2.5轮评估: 叶利广
2025-09-01 15:34:20 - INFO - 岗位名称: 未提及
2025-09-01 15:34:20 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:34:20 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:34:20 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:34:22 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:34:26 - INFO - ✅ 第2.5轮评估完成: 叶利广 | 分数: 0.0
2025-09-01 15:34:26 - INFO - 第2.5轮检测到明确不通过标识: ❌ 不通过
2025-09-01 15:34:26 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/二筛不合格\未通过_叶利广_第2.5轮_评估报告.md
2025-09-01 15:34:26 - INFO - ❌ 叶利广 未通过第2.5轮筛选，得分: 0.0
2025-09-01 15:34:38 - INFO - VL识别成功，识别内容长度: 1225
2025-09-01 15:34:38 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/姚博_VL识别结果.txt
2025-09-01 15:34:38 - INFO - 开始处理简历: 姚博
2025-09-01 15:34:38 - INFO - 开始第1轮评估: 姚博
2025-09-01 15:34:38 - INFO - 岗位名称: 未提及
2025-09-01 15:34:38 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:34:38 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:34:59 - INFO - ✅ 第1轮评估完成: 姚博 | 分数: 0.0
2025-09-01 15:34:59 - INFO - 检测到不合格关键词: 不匹配
2025-09-01 15:34:59 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛不合格\未通过_姚博_第1轮_评估报告.md
2025-09-01 15:34:59 - INFO - 开始提取候选人 姚博 的初筛报告信息
2025-09-01 15:34:59 - INFO - 找到优势文本: **优势:** 
    - 本科学历，3年工作经验，具备金融与AI结合领域的实操经验。
    - 曾参与AI Agent模型落地、Dify应用开发、数据标注及产品协同工作，具备一定的AI产品理解能力。
    - 具备良好的沟通协调能力，有团队协作意识，适应力较强。
  
- 
2025-09-01 15:34:59 - INFO - 找到风险文本: **风险与不足:** 
    - 由于JD未提供明确的岗位信息、职责和技能要求，无法判断其是否具备核心技能或经验。
    - 当前岗位为“数据标注经理”，与“AI产品经理”的岗位存在职能差异，需进一步评估其产品能力是否达标。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 由于岗位JD未提供任何明确的职责、技能或门槛信息，无法完成核心职责匹配度评估，也无法判断其是否具备AI产品经理岗位的核心能力。在信息严重缺失的情况下，无法支持其进入下一轮面试。建议补充完整JD后重新评估。
2025-09-01 15:34:59 - INFO - 提取的优势和风险: 优势=本科学历，3年工作经验，具备金融与AI结合领域的实操经验。；曾参与AI Agent模型落地、Dify应用开发、数据标注及产品协同工作，具备一定的AI产品理解能力。；具备良好的沟通协调能力，有团队协作意识，适应力较强。, 风险=由于JD未提供明确的岗位信息、职责和技能要求，无法判断其是否具备核心技能或经验。；当前岗位为“数据标注经理”，与“AI产品经理”的岗位存在职能差异，需进一步评估其产品能力是否达标。
2025-09-01 15:34:59 - INFO - 提取到姓名: 姚博
2025-09-01 15:34:59 - INFO - 提取到年龄: 26岁
2025-09-01 15:34:59 - INFO - 提取到工作经验: 3年
2025-09-01 15:34:59 - INFO - 提取到学历: 本科
2025-09-01 15:34:59 - INFO - 提取到当前职位: 数据标注经理
2025-09-01 15:34:59 - INFO - 提取到期望薪资: 15-18K
2025-09-01 15:34:59 - INFO - 从初筛报告提取的基本信息: {'name': '姚博', 'age': '26岁', 'experience': '3年', 'education': '本科', 'current_position': '数据标注经理', 'expected_salary': '15-18K'}
2025-09-01 15:34:59 - INFO - 候选人 姚博 初筛报告提取完成: advantages=本科学历，3年工作经验，具备金融与AI结合领域的实操经验。；曾参与AI Agent模型落地、Dify应用开发、数据标注及产品协同工作，具备一定的AI产品理解能力。；具备良好的沟通协调能力，有团队协作意识，适应力较强。, risks=由于JD未提供明确的岗位信息、职责和技能要求，无法判断其是否具备核心技能或经验。；当前岗位为“数据标注经理”，与“AI产品经理”的岗位存在职能差异，需进一步评估其产品能力是否达标。, basic_info={'name': '姚博', 'age': '26岁', 'experience': '3年', 'education': '本科', 'current_position': '数据标注经理', 'expected_salary': '15-18K'}
2025-09-01 15:34:59 - INFO - ❌ 姚博 未通过第1轮筛选，得分: 0.0
2025-09-01 15:36:54 - INFO - VL识别成功，识别内容长度: 16406
2025-09-01 15:36:54 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/林先生_VL识别结果.txt
2025-09-01 15:36:54 - INFO - 开始处理简历: 林先生
2025-09-01 15:36:54 - INFO - 开始第1轮评估: 林先生
2025-09-01 15:36:54 - INFO - 岗位名称: 未提及
2025-09-01 15:36:54 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:36:54 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:37:30 - INFO - ✅ 第1轮评估完成: 林先生 | 分数: 0.0
2025-09-01 15:37:30 - INFO - 检测到明确通过标识: ✅ 通过
2025-09-01 15:37:30 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛合格\通过_林先生_第1轮_评估报告.md
2025-09-01 15:37:30 - INFO - 开始提取候选人 林先生 的初筛报告信息
2025-09-01 15:37:30 - INFO - 找到优势文本: **优势:**
    - 拥有5年左右产品经理相关工作经验
    - 项目经验丰富，涵盖多个常见互联网产品功能模块的设计与实现
    - 具备从需求设计到产品上线的全流程参与经验

- 
2025-09-01 15:37:30 - INFO - 找到风险文本: **风险与不足:**
    - 岗位JD未提供具体职责与技能要求，无法进行精准匹配评估
    - 无法判断其经验是否覆盖JD中潜在的关键能力要求

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** 【✅ 通过】  
**筛选理由：** 硬性门槛审查中所有项目均为“匹配”，但由于岗位JD未提供具体职责与技能要求，核心职责匹配度无法准确评估。考虑到候选人具备完整的产品经理工作经历和丰富的项目经验，初步判断其具备产品经理岗位的基本任职资格，建议进入下一轮评估环节。
2025-09-01 15:37:30 - INFO - 提取的优势和风险: 优势=拥有5年左右产品经理相关工作经验；项目经验丰富，涵盖多个常见互联网产品功能模块的设计与实现；具备从需求设计到产品上线的全流程参与经验, 风险=岗位JD未提供具体职责与技能要求，无法进行精准匹配评估；无法判断其经验是否覆盖JD中潜在的关键能力要求
2025-09-01 15:37:30 - INFO - 提取到姓名: 林先生
2025-09-01 15:37:30 - INFO - 提取到年龄: 31岁
2025-09-01 15:37:30 - INFO - 提取到工作经验: 约5年产品经验（2019.01-2022.06）
2025-09-01 15:37:30 - INFO - 提取到学历: 本科
2025-09-01 15:37:30 - INFO - 提取到当前职位: 产品经理
2025-09-01 15:37:30 - INFO - 提取到期望薪资: 面议
2025-09-01 15:37:30 - INFO - 从初筛报告提取的基本信息: {'name': '林先生', 'age': '31岁', 'experience': '约5年产品经验（2019.01-2022.06）', 'education': '本科', 'current_position': '产品经理', 'expected_salary': '面议'}
2025-09-01 15:37:30 - INFO - 候选人 林先生 初筛报告提取完成: advantages=拥有5年左右产品经理相关工作经验；项目经验丰富，涵盖多个常见互联网产品功能模块的设计与实现；具备从需求设计到产品上线的全流程参与经验, risks=岗位JD未提供具体职责与技能要求，无法进行精准匹配评估；无法判断其经验是否覆盖JD中潜在的关键能力要求, basic_info={'name': '林先生', 'age': '31岁', 'experience': '约5年产品经验（2019.01-2022.06）', 'education': '本科', 'current_position': '产品经理', 'expected_salary': '面议'}
2025-09-01 15:37:30 - INFO - 开始第2轮评估: 林先生
2025-09-01 15:37:30 - INFO - 岗位名称: 未提及
2025-09-01 15:37:30 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像分析.md
2025-09-01 15:37:30 - INFO - 第2轮评估使用模型: qwen-plus
2025-09-01 15:37:53 - WARNING - 分数不一致: 总分=82.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-09-01 15:37:53 - WARNING - 候选人林先生分数不一致: 总分=82.0, 计算总分=0.0
2025-09-01 15:37:53 - INFO - ✅ 第2轮评估完成: 林先生 | 分数: 82.0
2025-09-01 15:37:53 - INFO - 未检测到明确关键词，默认通过
2025-09-01 15:37:53 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/画像分析结果\林先生_第2轮_画像分析.md
2025-09-01 15:37:53 - INFO - 开始第2.5轮评估: 林先生
2025-09-01 15:37:53 - INFO - 岗位名称: 未提及
2025-09-01 15:37:53 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:37:53 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:37:53 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:37:57 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:38:01 - INFO - ✅ 第2.5轮评估完成: 林先生 | 分数: 0.0
2025-09-01 15:38:01 - INFO - 第2.5轮检测到明确不通过标识: ❌ 不通过
2025-09-01 15:38:01 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/二筛不合格\未通过_林先生_第2.5轮_评估报告.md
2025-09-01 15:38:01 - INFO - ❌ 林先生 未通过第2.5轮筛选，得分: 0.0
2025-09-01 15:38:25 - INFO - VL识别成功，识别内容长度: 3609
2025-09-01 15:38:25 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/张轩颖_VL识别结果.txt
2025-09-01 15:38:25 - INFO - 开始处理简历: 张轩颖
2025-09-01 15:38:25 - INFO - 开始第1轮评估: 张轩颖
2025-09-01 15:38:25 - INFO - 岗位名称: 未提及
2025-09-01 15:38:25 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:38:25 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:38:55 - INFO - ✅ 第1轮评估完成: 张轩颖 | 分数: 0.0
2025-09-01 15:38:55 - INFO - 检测到明确通过标识: ✅ 通过
2025-09-01 15:38:55 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛合格\通过_张轩颖_第1轮_评估报告.md
2025-09-01 15:38:55 - INFO - 开始提取候选人 张轩颖 的初筛报告信息
2025-09-01 15:38:55 - INFO - 找到优势文本: **优势:**
    1. 拥有3年产品相关经验，涵盖产品经理、用户研究、产品助理等多个岗位，具备完整的产品能力模型。
    2. 曾参与中国移动5G新通话、和留言等大型项目，具备跨部门协作、资源整合、用户增长等综合能力。
    3. 硕士学历背景，本科期间多次获得设计类奖项，具备良好的设计思维与产品审美。

- 
2025-09-01 15:38:55 - INFO - 找到优势文本: 优势:**
    1. 拥有3年产品相关经验，涵盖产品经理、用户研究、产品助理等多个岗位，具备完整的产品能力模型。
    2. 曾参与中国移动5G新通话、和留言等大型项目，具备跨部门协作、资源整合、用户增长等综合能力。
    3. 硕士学历背景，本科期间多次获得设计类奖项，具备良好的设计思维与产品审美。

- **
2025-09-01 15:38:55 - INFO - 找到风险文本: **风险与不足:**
    - 无硬性门槛“潜在风险”项。
    - 由于JD中未提供核心职责与技能要求，无法进一步验证其是否具备特定岗位所需的**技术栈或行业经验**。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 未满足任意淘汰条件。
- **通过条件：**
    1. 硬性门槛审查中无“不匹配”项。
    2. 核心职责匹配度评估为“高”。

**筛选结果：✅ 通过**  
**筛选理由：** 候选人学历为硕士，年龄27岁，期望薪资未提供，均满足硬性门槛。尽管JD中未提供核心职责，但候选人具备丰富的产品经验，涵盖需求分析、项目管理、用户研究、竞品分析等多个方面，具备进入下一轮面试的资格。建议在下一轮面试中进一步明确岗位职责，评估其是否具备特定岗位所需的技能与经验。
2025-09-01 15:38:55 - INFO - 提取的优势和风险: 优势=**, 风险=无硬性门槛“潜在风险”项。；由于JD中未提供核心职责与技能要求，无法进一步验证其是否具备特定岗位所需的**技术栈或行业经验**。
2025-09-01 15:38:55 - INFO - 提取到姓名: 张轩颖
2025-09-01 15:38:55 - INFO - 提取到年龄: 27岁
2025-09-01 15:38:55 - INFO - 提取到工作经验: 3年（2022.03 - 2024.11）
2025-09-01 15:38:55 - INFO - 提取到学历: 硕士
2025-09-01 15:38:55 - INFO - 提取到当前职位: 兼职
2025-09-01 15:38:55 - INFO - 提取到期望薪资: 未提及
2025-09-01 15:38:55 - INFO - 从初筛报告提取的基本信息: {'name': '张轩颖', 'age': '27岁', 'experience': '3年（2022.03 - 2024.11）', 'education': '硕士', 'current_position': '兼职', 'expected_salary': '未提及'}
2025-09-01 15:38:55 - INFO - 候选人 张轩颖 初筛报告提取完成: advantages=**, risks=无硬性门槛“潜在风险”项。；由于JD中未提供核心职责与技能要求，无法进一步验证其是否具备特定岗位所需的**技术栈或行业经验**。, basic_info={'name': '张轩颖', 'age': '27岁', 'experience': '3年（2022.03 - 2024.11）', 'education': '硕士', 'current_position': '兼职', 'expected_salary': '未提及'}
2025-09-01 15:38:55 - INFO - 开始第2轮评估: 张轩颖
2025-09-01 15:38:55 - INFO - 岗位名称: 未提及
2025-09-01 15:38:55 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像分析.md
2025-09-01 15:38:55 - INFO - 第2轮评估使用模型: qwen-plus
2025-09-01 15:39:29 - WARNING - 分数不一致: 总分=85.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-09-01 15:39:29 - WARNING - 候选人张轩颖分数不一致: 总分=85.0, 计算总分=0.0
2025-09-01 15:39:29 - INFO - ✅ 第2轮评估完成: 张轩颖 | 分数: 85.0
2025-09-01 15:39:29 - INFO - 未检测到明确关键词，默认通过
2025-09-01 15:39:29 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/画像分析结果\张轩颖_第2轮_画像分析.md
2025-09-01 15:39:29 - INFO - 开始第2.5轮评估: 张轩颖
2025-09-01 15:39:29 - INFO - 岗位名称: 未提及
2025-09-01 15:39:29 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:39:29 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:39:29 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:39:32 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:39:34 - INFO - ✅ 第2.5轮评估完成: 张轩颖 | 分数: 0.0
2025-09-01 15:39:34 - INFO - 第2.5轮检测到明确不通过标识: ❌ 不通过
2025-09-01 15:39:34 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/二筛不合格\未通过_张轩颖_第2.5轮_评估报告.md
2025-09-01 15:39:34 - INFO - ❌ 张轩颖 未通过第2.5轮筛选，得分: 0.0
2025-09-01 15:39:50 - INFO - VL识别成功，识别内容长度: 2356
2025-09-01 15:39:50 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/叶利广_VL识别结果.txt
2025-09-01 15:39:50 - INFO - 开始处理简历: 叶利广
2025-09-01 15:39:50 - INFO - 开始第1轮评估: 叶利广
2025-09-01 15:39:50 - INFO - 岗位名称: 未提及
2025-09-01 15:39:50 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:39:50 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:40:11 - INFO - ✅ 第1轮评估完成: 叶利广 | 分数: 0.0
2025-09-01 15:40:11 - INFO - 检测到不合格关键词: 不匹配
2025-09-01 15:40:11 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛不合格\未通过_叶利广_第1轮_评估报告.md
2025-09-01 15:40:11 - INFO - 开始提取候选人 叶利广 的初筛报告信息
2025-09-01 15:40:11 - INFO - 找到优势文本: **优势:** 
    - 硕士学历，具备扎实的交互与设计背景。
    - 具备AI产品经理经验，参与从0到1的虚拟陪伴产品开发，并有实际落地项目。
    - 掌握AIGC相关工具（Midjourney、Stable Diffusion等），具备一定的技术实现能力。
    - 拥有多个专利，体现创新能力。

- 
2025-09-01 15:40:11 - INFO - 找到风险文本: **风险与不足:** 
    - JD中未提供明确的岗位职责与技能要求，无法判断候选人是否具备相关核心能力。
    - 期望薪资未与JD对比，无法判断是否存在潜在风险。

---

**4. 初筛结论**

- **淘汰条件：**
    1. 不适用（未满足硬性门槛条件）
    2. 不适用（未进行核心职责匹配评估）

- **通过条件：**
    1. 硬性门槛审查中无“不匹配”项 ✅
    2. 核心职责匹配度未评估 ❌

**筛选结果：** ❌ 淘汰  
**筛选理由：** 由于岗位JD未提供具体的核心职责和技能要求，无法评估候选人是否具备岗位所需的关键能力。因此，基于现有信息无法确认其是否具备进入下一轮面试的资格。建议补充完整JD后重新评估。
2025-09-01 15:40:11 - INFO - 提取的优势和风险: 优势=硕士学历，具备扎实的交互与设计背景。；具备AI产品经理经验，参与从0到1的虚拟陪伴产品开发，并有实际落地项目。；掌握AIGC相关工具（Midjourney、Stable Diffusion等），具备一定的技术实现能力。, 风险=JD中未提供明确的岗位职责与技能要求，无法判断候选人是否具备相关核心能力。；期望薪资未与JD对比，无法判断是否存在潜在风险。
2025-09-01 15:40:11 - INFO - 提取到姓名: 叶利广
2025-09-01 15:40:11 - INFO - 提取到年龄: 26岁
2025-09-01 15:40:11 - INFO - 提取到工作经验: 3年
2025-09-01 15:40:11 - INFO - 提取到学历: 硕士
2025-09-01 15:40:11 - INFO - 提取到当前职位: 产品经理
2025-09-01 15:40:11 - INFO - 提取到期望薪资: 15-25K
2025-09-01 15:40:11 - INFO - 从初筛报告提取的基本信息: {'name': '叶利广', 'age': '26岁', 'experience': '3年', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '15-25K'}
2025-09-01 15:40:11 - INFO - 候选人 叶利广 初筛报告提取完成: advantages=硕士学历，具备扎实的交互与设计背景。；具备AI产品经理经验，参与从0到1的虚拟陪伴产品开发，并有实际落地项目。；掌握AIGC相关工具（Midjourney、Stable Diffusion等），具备一定的技术实现能力。, risks=JD中未提供明确的岗位职责与技能要求，无法判断候选人是否具备相关核心能力。；期望薪资未与JD对比，无法判断是否存在潜在风险。, basic_info={'name': '叶利广', 'age': '26岁', 'experience': '3年', 'education': '硕士', 'current_position': '产品经理', 'expected_salary': '15-25K'}
2025-09-01 15:40:11 - INFO - ❌ 叶利广 未通过第1轮筛选，得分: 0.0
2025-09-01 15:42:03 - INFO - VL识别成功，识别内容长度: 19549
2025-09-01 15:42:03 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/谭文杰_VL识别结果.txt
2025-09-01 15:42:03 - INFO - 开始处理简历: 谭文杰
2025-09-01 15:42:03 - INFO - 开始第1轮评估: 谭文杰
2025-09-01 15:42:03 - INFO - 岗位名称: 未提及
2025-09-01 15:42:03 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:42:03 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:42:23 - INFO - ✅ 第1轮评估完成: 谭文杰 | 分数: 0.0
2025-09-01 15:42:23 - INFO - 检测到不合格关键词: 不匹配
2025-09-01 15:42:23 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛不合格\未通过_谭文杰_第1轮_评估报告.md
2025-09-01 15:42:23 - INFO - 开始提取候选人 谭文杰 的初筛报告信息
2025-09-01 15:42:23 - INFO - 找到优势文本: **优势:**  
    - 拥有10年以上工作经验，具备从0到1的产品设计经验  
    - 熟悉AI相关技术（NLP、生成式AI、数字人）  
    - 具备UI设计、交互设计、动画特效等多方面技能  
    - 掌握英语、日语、粤语三语沟通能力，具备海外项目经验  

- 
2025-09-01 15:42:23 - INFO - 找到风险文本: **风险与不足:**  
    - JD中未提供任何硬性门槛和核心职责信息，无法判断是否符合岗位需求  
    - 学历为大专，若岗位有更高学历要求将不匹配  
    - 年龄39岁，若岗位有较年轻的年龄限制可能构成潜在风险  
    - 期望薪资未提供，无法判断与JD是否匹配  

---

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** 【未确定】  
**筛选理由：** 因岗位JD未提供任何核心职责、技能要求或硬性门槛信息，无法判断候选人是否真正匹配岗位需求。建议补充完整JD内容后再行评估。
2025-09-01 15:42:23 - INFO - 提取的优势和风险: 优势=拥有10年以上工作经验，具备从0到1的产品设计经验  ；熟悉AI相关技术（NLP、生成式AI、数字人）  ；具备UI设计、交互设计、动画特效等多方面技能  , 风险=JD中未提供任何硬性门槛和核心职责信息，无法判断是否符合岗位需求  ；学历为大专，若岗位有更高学历要求将不匹配  
2025-09-01 15:42:23 - INFO - 提取到姓名: 谭文杰
2025-09-01 15:42:23 - INFO - 提取到年龄: 39岁
2025-09-01 15:42:23 - INFO - 提取到工作经验: 10年以上
2025-09-01 15:42:23 - INFO - 提取到学历: 大专
2025-09-01 15:42:23 - INFO - 提取到当前职位: 离职-随时到岗
2025-09-01 15:42:23 - INFO - 提取到期望薪资: 未提及
2025-09-01 15:42:23 - INFO - 从初筛报告提取的基本信息: {'name': '谭文杰', 'age': '39岁', 'experience': '10年以上', 'education': '大专', 'current_position': '离职-随时到岗', 'expected_salary': '未提及'}
2025-09-01 15:42:23 - INFO - 候选人 谭文杰 初筛报告提取完成: advantages=拥有10年以上工作经验，具备从0到1的产品设计经验  ；熟悉AI相关技术（NLP、生成式AI、数字人）  ；具备UI设计、交互设计、动画特效等多方面技能  , risks=JD中未提供任何硬性门槛和核心职责信息，无法判断是否符合岗位需求  ；学历为大专，若岗位有更高学历要求将不匹配  , basic_info={'name': '谭文杰', 'age': '39岁', 'experience': '10年以上', 'education': '大专', 'current_position': '离职-随时到岗', 'expected_salary': '未提及'}
2025-09-01 15:42:23 - INFO - ❌ 谭文杰 未通过第1轮筛选，得分: 0.0
2025-09-01 15:43:57 - INFO - VL识别成功，识别内容长度: 28811
2025-09-01 15:43:57 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/丘伟航_VL识别结果.txt
2025-09-01 15:43:57 - INFO - 开始处理简历: 丘伟航
2025-09-01 15:43:57 - INFO - 开始第1轮评估: 丘伟航
2025-09-01 15:43:57 - INFO - 岗位名称: 未提及
2025-09-01 15:43:57 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:43:57 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:44:20 - INFO - ✅ 第1轮评估完成: 丘伟航 | 分数: 0.0
2025-09-01 15:44:20 - INFO - 检测到不合格关键词: 不匹配
2025-09-01 15:44:20 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛不合格\未通过_丘伟航_第1轮_评估报告.md
2025-09-01 15:44:20 - INFO - 开始提取候选人 丘伟航 的初筛报告信息
2025-09-01 15:44:20 - INFO - 找到优势文本: **优势:**
    - 多年在广东省检验检疫局有限公司从事产品管理、技术开发、测试等多角色经验，具备复合型能力。
    - 技术能力扎实，掌握主流开发、部署、运维工具链，适合技术导向岗位。
- 
2025-09-01 15:44:20 - INFO - 找到风险文本: **风险与不足:**
    - JD未提供任何具体要求，无法进行针对性评估。
    - 无明确岗位名称、薪资、学历、年龄要求，无法判断候选人是否符合硬性标准。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "不匹配"。
    2. 核心职责匹配度 评估结果为 "低"。
- **通过条件：**
    1. 硬性门槛审查 结果中没有 "不匹配" 项。
    2. 核心职责匹配度 评估结果为 "中" 或 "高"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 由于岗位JD未提供具体岗位名称、核心职责、薪资、学历及年龄要求，导致无法进行有效的匹配评估。在缺乏明确评估标准的前提下，无法判断候选人是否符合岗位要求，因此按规则淘汰。
2025-09-01 15:44:20 - INFO - 提取的优势和风险: 优势=多年在广东省检验检疫局有限公司从事产品管理、技术开发、测试等多角色经验，具备复合型能力。；技术能力扎实，掌握主流开发、部署、运维工具链，适合技术导向岗位。, 风险=JD未提供任何具体要求，无法进行针对性评估。；无明确岗位名称、薪资、学历、年龄要求，无法判断候选人是否符合硬性标准。
2025-09-01 15:44:20 - INFO - 提取到姓名: 丘伟凯
2025-09-01 15:44:20 - INFO - 提取到年龄: 34岁
2025-09-01 15:44:20 - INFO - 提取到工作经验: 约4年（2020.03 - 至今）
2025-09-01 15:44:20 - INFO - 提取到学历: 北京邮电大学经济学院 计算机科学与技术 学士（2018-2021）
2025-09-01 15:44:20 - INFO - 提取到当前职位: 产品经理（2021.11 - 至今）
2025-09-01 15:44:20 - INFO - 提取到期望薪资: 未提及
2025-09-01 15:44:20 - INFO - 从初筛报告提取的基本信息: {'name': '丘伟凯', 'age': '34岁', 'experience': '约4年（2020.03 - 至今）', 'education': '北京邮电大学经济学院 计算机科学与技术 学士（2018-2021）', 'current_position': '产品经理（2021.11 - 至今）', 'expected_salary': '未提及'}
2025-09-01 15:44:20 - INFO - 候选人 丘伟航 初筛报告提取完成: advantages=多年在广东省检验检疫局有限公司从事产品管理、技术开发、测试等多角色经验，具备复合型能力。；技术能力扎实，掌握主流开发、部署、运维工具链，适合技术导向岗位。, risks=JD未提供任何具体要求，无法进行针对性评估。；无明确岗位名称、薪资、学历、年龄要求，无法判断候选人是否符合硬性标准。, basic_info={'name': '丘伟凯', 'age': '34岁', 'experience': '约4年（2020.03 - 至今）', 'education': '北京邮电大学经济学院 计算机科学与技术 学士（2018-2021）', 'current_position': '产品经理（2021.11 - 至今）', 'expected_salary': '未提及'}
2025-09-01 15:44:20 - INFO - ❌ 丘伟航 未通过第1轮筛选，得分: 0.0
2025-09-01 15:44:28 - INFO - VL识别成功，识别内容长度: 858
2025-09-01 15:44:28 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/潘玉玲_VL识别结果.txt
2025-09-01 15:44:28 - INFO - 开始处理简历: 潘玉玲
2025-09-01 15:44:28 - INFO - 开始第1轮评估: 潘玉玲
2025-09-01 15:44:28 - INFO - 岗位名称: 未提及
2025-09-01 15:44:28 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:44:28 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:44:50 - INFO - ✅ 第1轮评估完成: 潘玉玲 | 分数: 0.0
2025-09-01 15:44:50 - INFO - 检测到明确通过标识: ✅ 通过
2025-09-01 15:44:50 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛合格\通过_潘玉玲_第1轮_评估报告.md
2025-09-01 15:44:50 - INFO - 开始提取候选人 潘玉玲 的初筛报告信息
2025-09-01 15:44:50 - INFO - 找到优势文本: **优势:** 
    - 拥有北京大学计算机科学与技术专业本科学历，学术背景良好。
    - 在阿里巴巴集团担任高级产品经理，具有丰富的产品管理和项目推进经验。
    - 技术背景扎实，掌握多种编程语言和数据库管理技能，具备良好的技术沟通能力。
    - 持有PMP认证，具备专业项目管理能力。

- 
2025-09-01 15:44:50 - INFO - 找到风险文本: **风险与不足:** 
    - 岗位JD未提供具体信息，无法判断是否存在潜在风险。
    - 未提供期望薪资与当前年龄、薪资范围的比对数据。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** 【✅ 通过】
**筛选理由：** 候选人学历、年龄、薪资等硬性门槛均未超出限制（因JD未提供具体数值），因此均判定为“匹配”。但由于JD未提供核心职责和技能要求，核心职责匹配度无法评估。根据当前规则，未发现淘汰条件，故判定为“通过”。建议在获取完整JD后进行详细评估。
2025-09-01 15:44:50 - INFO - 提取的优势和风险: 优势=拥有北京大学计算机科学与技术专业本科学历，学术背景良好。；在阿里巴巴集团担任高级产品经理，具有丰富的产品管理和项目推进经验。；技术背景扎实，掌握多种编程语言和数据库管理技能，具备良好的技术沟通能力。, 风险=岗位JD未提供具体信息，无法判断是否存在潜在风险。；未提供期望薪资与当前年龄、薪资范围的比对数据。
2025-09-01 15:44:50 - INFO - 提取到姓名: 李华
2025-09-01 15:44:50 - INFO - 提取到年龄: 34岁
2025-09-01 15:44:50 - INFO - 提取到工作经验: 约8年（2014年毕业，2016年起至今在阿里工作）
2025-09-01 15:44:50 - INFO - 提取到学历: 北京大学本科（计算机科学与技术）
2025-09-01 15:44:50 - INFO - 提取到当前职位: 阿里巴巴集团高级产品经理
2025-09-01 15:44:50 - INFO - 提取到期望薪资: 未提及
2025-09-01 15:44:50 - INFO - 从初筛报告提取的基本信息: {'name': '李华', 'age': '34岁', 'experience': '约8年（2014年毕业，2016年起至今在阿里工作）', 'education': '北京大学本科（计算机科学与技术）', 'current_position': '阿里巴巴集团高级产品经理', 'expected_salary': '未提及'}
2025-09-01 15:44:50 - INFO - 候选人 潘玉玲 初筛报告提取完成: advantages=拥有北京大学计算机科学与技术专业本科学历，学术背景良好。；在阿里巴巴集团担任高级产品经理，具有丰富的产品管理和项目推进经验。；技术背景扎实，掌握多种编程语言和数据库管理技能，具备良好的技术沟通能力。, risks=岗位JD未提供具体信息，无法判断是否存在潜在风险。；未提供期望薪资与当前年龄、薪资范围的比对数据。, basic_info={'name': '李华', 'age': '34岁', 'experience': '约8年（2014年毕业，2016年起至今在阿里工作）', 'education': '北京大学本科（计算机科学与技术）', 'current_position': '阿里巴巴集团高级产品经理', 'expected_salary': '未提及'}
2025-09-01 15:44:50 - INFO - 开始第2轮评估: 潘玉玲
2025-09-01 15:44:50 - INFO - 岗位名称: 未提及
2025-09-01 15:44:50 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像分析.md
2025-09-01 15:44:50 - INFO - 第2轮评估使用模型: qwen-plus
2025-09-01 15:45:10 - WARNING - 分数不一致: 总分=82.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-09-01 15:45:10 - WARNING - 候选人潘玉玲分数不一致: 总分=82.0, 计算总分=0.0
2025-09-01 15:45:10 - INFO - ✅ 第2轮评估完成: 潘玉玲 | 分数: 82.0
2025-09-01 15:45:10 - INFO - 未检测到明确关键词，默认通过
2025-09-01 15:45:10 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/画像分析结果\潘玉玲_第2轮_画像分析.md
2025-09-01 15:45:10 - INFO - 开始第2.5轮评估: 潘玉玲
2025-09-01 15:45:10 - INFO - 岗位名称: 未提及
2025-09-01 15:45:10 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:45:10 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:45:10 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:45:13 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:45:16 - INFO - ✅ 第2.5轮评估完成: 潘玉玲 | 分数: 0.0
2025-09-01 15:45:16 - INFO - 第2.5轮检测到明确不通过标识: ❌ 不通过
2025-09-01 15:45:16 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/二筛不合格\未通过_潘玉玲_第2.5轮_评估报告.md
2025-09-01 15:45:16 - INFO - ❌ 潘玉玲 未通过第2.5轮筛选，得分: 0.0
2025-09-01 15:45:34 - INFO - VL识别成功，识别内容长度: 2332
2025-09-01 15:45:34 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/丁嘉亮_VL识别结果.txt
2025-09-01 15:45:34 - INFO - 开始处理简历: 丁嘉亮
2025-09-01 15:45:34 - INFO - 开始第1轮评估: 丁嘉亮
2025-09-01 15:45:34 - INFO - 岗位名称: 未提及
2025-09-01 15:45:34 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:45:34 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:45:51 - INFO - ✅ 第1轮评估完成: 丁嘉亮 | 分数: 0.0
2025-09-01 15:45:51 - INFO - 检测到不合格关键词: 淘汰
2025-09-01 15:45:51 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛不合格\未通过_丁嘉亮_第1轮_评估报告.md
2025-09-01 15:45:51 - INFO - 开始提取候选人 丁嘉亮 的初筛报告信息
2025-09-01 15:45:51 - INFO - 找到优势文本: **优势:**
    - 硕士学历，专业对口，具备扎实的AI理论基础。
    - 有多个AI项目实践经验，涵盖大模型应用、Prompt工程、Agent开发、模型微调、质检系统研发等。
    - 具备科研能力，有IEEE会议论文发表经历。
    - 技术栈全面，掌握主流深度学习框架和算法优化技术。
- 
2025-09-01 15:45:51 - INFO - 找到风险文本: **风险与不足:**
    - JD中未提供硬性门槛信息（学历、年龄、薪资），因此无法识别潜在风险项。
    - 由于JD中未列出核心职责和技能要求，无法评估候选人与岗位职责的匹配程度。

**4. 初筛结论**

- **淘汰条件：**
    - 不适用（无硬性门槛信息）
- **通过条件：**
    - 暂无法判断，需补充JD信息。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 岗位描述（JD）未提供核心职责、技能要求及硬性门槛信息，导致无法进行有效评估。根据评估准则，**缺少JD信息将无法判断候选人是否符合岗位要求，因此无法通过初筛**。建议补充完整JD后重新评估。
2025-09-01 15:45:51 - INFO - 提取的优势和风险: 优势=硕士学历，专业对口，具备扎实的AI理论基础。；有多个AI项目实践经验，涵盖大模型应用、Prompt工程、Agent开发、模型微调、质检系统研发等。；具备科研能力，有IEEE会议论文发表经历。, 风险=JD中未提供硬性门槛信息（学历、年龄、薪资），因此无法识别潜在风险项。；由于JD中未列出核心职责和技能要求，无法评估候选人与岗位职责的匹配程度。
2025-09-01 15:45:51 - INFO - 提取到姓名: 丁嘉亮
2025-09-01 15:45:51 - INFO - 提取到年龄: 24岁
2025-09-01 15:45:51 - INFO - 提取到工作经验: 约1年（2024.11-2025.02 + 2025.06-2025.08）
2025-09-01 15:45:51 - INFO - 提取到学历: 硕士（人工智能与数码媒体）
2025-09-01 15:45:51 - INFO - 提取到当前职位: 大模型算法科研员
2025-09-01 15:45:51 - INFO - 提取到期望薪资: 未提及
2025-09-01 15:45:51 - INFO - 从初筛报告提取的基本信息: {'name': '丁嘉亮', 'age': '24岁', 'experience': '约1年（2024.11-2025.02 + 2025.06-2025.08）', 'education': '硕士（人工智能与数码媒体）', 'current_position': '大模型算法科研员', 'expected_salary': '未提及'}
2025-09-01 15:45:51 - INFO - 候选人 丁嘉亮 初筛报告提取完成: advantages=硕士学历，专业对口，具备扎实的AI理论基础。；有多个AI项目实践经验，涵盖大模型应用、Prompt工程、Agent开发、模型微调、质检系统研发等。；具备科研能力，有IEEE会议论文发表经历。, risks=JD中未提供硬性门槛信息（学历、年龄、薪资），因此无法识别潜在风险项。；由于JD中未列出核心职责和技能要求，无法评估候选人与岗位职责的匹配程度。, basic_info={'name': '丁嘉亮', 'age': '24岁', 'experience': '约1年（2024.11-2025.02 + 2025.06-2025.08）', 'education': '硕士（人工智能与数码媒体）', 'current_position': '大模型算法科研员', 'expected_salary': '未提及'}
2025-09-01 15:45:51 - INFO - ❌ 丁嘉亮 未通过第1轮筛选，得分: 0.0
2025-09-01 15:46:03 - INFO - VL识别成功，识别内容长度: 1723
2025-09-01 15:46:03 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/杨志海_VL识别结果.txt
2025-09-01 15:46:03 - INFO - 开始处理简历: 杨志海
2025-09-01 15:46:03 - INFO - 开始第1轮评估: 杨志海
2025-09-01 15:46:03 - INFO - 岗位名称: 未提及
2025-09-01 15:46:03 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:46:03 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:46:21 - INFO - ✅ 第1轮评估完成: 杨志海 | 分数: 0.0
2025-09-01 15:46:21 - INFO - 检测到明确通过标识: ✅ 通过
2025-09-01 15:46:21 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛合格\通过_杨志海_第1轮_评估报告.md
2025-09-01 15:46:21 - INFO - 开始提取候选人 杨志海 的初筛报告信息
2025-09-01 15:46:21 - INFO - 找到优势文本: **优势:** 
    - 有多年完整的产品经理从业经历，覆盖多个行业。
    - 具备AI产品经验，能结合具体业务场景进行产品设计。
    - 有从0到1建设SaaS系统的能力，熟悉产品全生命周期管理。
    - 技能工具链完整，包括Axure、Jira、Notion等主流产品工具。

- 
2025-09-01 15:46:21 - INFO - 找到风险文本: **风险与不足:** 
    - JD未提供明确的岗位职责和技能要求，导致无法进行职责匹配度分析。
    - 无明确的岗位方向（如是否为AI产品经理、SaaS产品经理等），需在后续面试中进一步确认岗位契合度。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** 【✅ 通过】  
**筛选理由：** 简历显示候选人具备匹配产品经理岗位的学历、年龄和丰富的产品经验。由于JD未提供具体职责和技能要求，核心职责匹配度无法评估，但硬性门槛均满足，具备进入下一轮面试的资格。建议在下一轮面试中进一步明确岗位方向及职责要求。
2025-09-01 15:46:21 - INFO - 提取的优势和风险: 优势=有多年完整的产品经理从业经历，覆盖多个行业。；具备AI产品经验，能结合具体业务场景进行产品设计。；有从0到1建设SaaS系统的能力，熟悉产品全生命周期管理。, 风险=JD未提供明确的岗位职责和技能要求，导致无法进行职责匹配度分析。；无明确的岗位方向（如是否为AI产品经理、SaaS产品经理等），需在后续面试中进一步确认岗位契合度。
2025-09-01 15:46:21 - INFO - 提取到姓名: 杨志海
2025-09-01 15:46:21 - INFO - 提取到年龄: 31岁
2025-09-01 15:46:21 - INFO - 提取到工作经验: 约5年产品经理经验（2019.04至今）
2025-09-01 15:46:21 - INFO - 提取到学历: 本科
2025-09-01 15:46:21 - INFO - 提取到当前职位: AI产品经理
2025-09-01 15:46:21 - INFO - 提取到期望薪资: 未提及
2025-09-01 15:46:21 - INFO - 从初筛报告提取的基本信息: {'name': '杨志海', 'age': '31岁', 'experience': '约5年产品经理经验（2019.04至今）', 'education': '本科', 'current_position': 'AI产品经理', 'expected_salary': '未提及'}
2025-09-01 15:46:21 - INFO - 候选人 杨志海 初筛报告提取完成: advantages=有多年完整的产品经理从业经历，覆盖多个行业。；具备AI产品经验，能结合具体业务场景进行产品设计。；有从0到1建设SaaS系统的能力，熟悉产品全生命周期管理。, risks=JD未提供明确的岗位职责和技能要求，导致无法进行职责匹配度分析。；无明确的岗位方向（如是否为AI产品经理、SaaS产品经理等），需在后续面试中进一步确认岗位契合度。, basic_info={'name': '杨志海', 'age': '31岁', 'experience': '约5年产品经理经验（2019.04至今）', 'education': '本科', 'current_position': 'AI产品经理', 'expected_salary': '未提及'}
2025-09-01 15:46:21 - INFO - 开始第2轮评估: 杨志海
2025-09-01 15:46:21 - INFO - 岗位名称: 未提及
2025-09-01 15:46:21 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像分析.md
2025-09-01 15:46:21 - INFO - 第2轮评估使用模型: qwen-plus
2025-09-01 15:47:31 - WARNING - 分数不一致: 总分=85.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-09-01 15:47:31 - WARNING - 候选人杨志海分数不一致: 总分=85.0, 计算总分=0.0
2025-09-01 15:47:31 - INFO - ✅ 第2轮评估完成: 杨志海 | 分数: 85.0
2025-09-01 15:47:31 - INFO - 未检测到明确关键词，默认通过
2025-09-01 15:47:31 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/画像分析结果\杨志海_第2轮_画像分析.md
2025-09-01 15:47:31 - INFO - 开始第2.5轮评估: 杨志海
2025-09-01 15:47:31 - INFO - 岗位名称: 未提及
2025-09-01 15:47:31 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:47:31 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:47:31 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:47:34 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:47:36 - INFO - ✅ 第2.5轮评估完成: 杨志海 | 分数: 0.0
2025-09-01 15:47:36 - INFO - 第2.5轮检测到明确不通过标识: ❌ 不通过
2025-09-01 15:47:36 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/二筛不合格\未通过_杨志海_第2.5轮_评估报告.md
2025-09-01 15:47:36 - INFO - ❌ 杨志海 未通过第2.5轮筛选，得分: 0.0
2025-09-01 15:48:09 - INFO - VL识别成功，识别内容长度: 4815
2025-09-01 15:48:09 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/林丽丽_VL识别结果.txt
2025-09-01 15:48:09 - INFO - 开始处理简历: 林丽丽
2025-09-01 15:48:09 - INFO - 开始第1轮评估: 林丽丽
2025-09-01 15:48:09 - INFO - 岗位名称: 未提及
2025-09-01 15:48:09 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:48:09 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:48:32 - INFO - ✅ 第1轮评估完成: 林丽丽 | 分数: 0.0
2025-09-01 15:48:32 - INFO - 检测到明确通过标识: ✅ 通过
2025-09-01 15:48:32 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛合格\通过_林丽丽_第1轮_评估报告.md
2025-09-01 15:48:32 - INFO - 开始提取候选人 林丽丽 的初筛报告信息
2025-09-01 15:48:32 - INFO - 找到优势文本: **优势:**  
    - 本科学历，专业对口，具备扎实的教育背景，多次获得奖学金及荣誉称号；
    - 具备3年产品经理经验，曾主导多个从0到1的产品建设及迭代，涉及数据产品、云平台、专利管理系统等领域；
    - 有丰富的市场调研、产品设计、项目管理、用户增长等方面的工作经验；
    - 曾主导多个大型项目，累计金额超千万，具备较强的产品商业化能力；
    - 多次获得优秀员工奖，业绩突出，具备良好的沟通与团队协作能力。

- 
2025-09-01 15:48:32 - INFO - 找到风险文本: **风险与不足:**  
    - 岗位JD未提供具体的核心职责与技能要求，无法进行职责匹配分析；
    - 期望薪资为“面议”，但JD未提供薪资范围，无法准确判断匹配度；
    - 学历和年龄虽匹配，但因JD未设定门槛，无法判断是否为“风险项”。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** 【✅ 通过】  
**筛选理由：** 硬性门槛字段（学历、年龄、薪资）均未在JD中明确要求，候选人信息与之不冲突，故无“不匹配”项。尽管核心职责无法评估，但候选人具备完整的产品经理履历与扎实的项目经验，具备进入下一轮面试的基础条件。建议在下一轮面试中明确岗位具体职责与技能要求，进行进一步评估。
2025-09-01 15:48:32 - INFO - 提取的优势和风险: 优势=- 具备3年产品经理经验，曾主导多个从0到1的产品建设及迭代，涉及数据产品、云平台、专利管理系统等领域；- 有丰富的市场调研、产品设计、项目管理、用户增长等方面的工作经验；- 曾主导多个大型项目，累计金额超千万，具备较强的产品商业化能力, 风险=- 期望薪资为“面议”，但JD未提供薪资范围，无法准确判断匹配度；- 学历和年龄虽匹配，但因JD未设定门槛，无法判断是否为“风险项”。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** 【✅ 通过】  
**筛选理由：** 硬性门槛字段（学历、年龄、薪资）均未在JD中明确要求，候选人信息与之不冲突，故无“不匹配”项。尽管核心职责无法评估，但候选人具备完整的产品经理履历与扎实的项目经验，具备进入下一轮面试的基础条件。建议在下一轮面试中明确岗位具体职责与技能要求，进行进一步评估。
2025-09-01 15:48:32 - INFO - 提取到姓名: 林丽丽
2025-09-01 15:48:32 - INFO - 提取到年龄: 27岁
2025-09-01 15:48:32 - INFO - 提取到工作经验: 3年
2025-09-01 15:48:32 - INFO - 提取到学历: 本科
2025-09-01 15:48:32 - INFO - 提取到当前职位: 产品经理 / 产品负责人
2025-09-01 15:48:32 - INFO - 提取到期望薪资: 面议
2025-09-01 15:48:32 - INFO - 从初筛报告提取的基本信息: {'name': '林丽丽', 'age': '27岁', 'experience': '3年', 'education': '本科', 'current_position': '产品经理 / 产品负责人', 'expected_salary': '面议'}
2025-09-01 15:48:32 - INFO - 候选人 林丽丽 初筛报告提取完成: advantages=- 具备3年产品经理经验，曾主导多个从0到1的产品建设及迭代，涉及数据产品、云平台、专利管理系统等领域；- 有丰富的市场调研、产品设计、项目管理、用户增长等方面的工作经验；- 曾主导多个大型项目，累计金额超千万，具备较强的产品商业化能力, risks=- 期望薪资为“面议”，但JD未提供薪资范围，无法准确判断匹配度；- 学历和年龄虽匹配，但因JD未设定门槛，无法判断是否为“风险项”。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。

- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** 【✅ 通过】  
**筛选理由：** 硬性门槛字段（学历、年龄、薪资）均未在JD中明确要求，候选人信息与之不冲突，故无“不匹配”项。尽管核心职责无法评估，但候选人具备完整的产品经理履历与扎实的项目经验，具备进入下一轮面试的基础条件。建议在下一轮面试中明确岗位具体职责与技能要求，进行进一步评估。, basic_info={'name': '林丽丽', 'age': '27岁', 'experience': '3年', 'education': '本科', 'current_position': '产品经理 / 产品负责人', 'expected_salary': '面议'}
2025-09-01 15:48:32 - INFO - 开始第2轮评估: 林丽丽
2025-09-01 15:48:32 - INFO - 岗位名称: 未提及
2025-09-01 15:48:32 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像分析.md
2025-09-01 15:48:32 - INFO - 第2轮评估使用模型: qwen-plus
2025-09-01 15:48:46 - WARNING - 分数不一致: 总分=87.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-09-01 15:48:46 - WARNING - 候选人林丽丽分数不一致: 总分=87.0, 计算总分=0.0
2025-09-01 15:48:46 - INFO - ✅ 第2轮评估完成: 林丽丽 | 分数: 87.0
2025-09-01 15:48:46 - INFO - 未检测到明确关键词，默认通过
2025-09-01 15:48:46 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/画像分析结果\林丽丽_第2轮_画像分析.md
2025-09-01 15:48:46 - INFO - 开始第2.5轮评估: 林丽丽
2025-09-01 15:48:46 - INFO - 岗位名称: 未提及
2025-09-01 15:48:46 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:48:46 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:48:46 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:48:49 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:48:51 - INFO - ✅ 第2.5轮评估完成: 林丽丽 | 分数: 0.0
2025-09-01 15:48:51 - INFO - 第2.5轮检测到明确不通过标识: ❌ 不通过
2025-09-01 15:48:51 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/二筛不合格\未通过_林丽丽_第2.5轮_评估报告.md
2025-09-01 15:48:51 - INFO - ❌ 林丽丽 未通过第2.5轮筛选，得分: 0.0
2025-09-01 15:49:02 - INFO - VL识别成功，识别内容长度: 1213
2025-09-01 15:49:02 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/姚博_VL识别结果.txt
2025-09-01 15:49:02 - INFO - 开始处理简历: 姚博
2025-09-01 15:49:02 - INFO - 开始第1轮评估: 姚博
2025-09-01 15:49:02 - INFO - 岗位名称: 未提及
2025-09-01 15:49:02 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:49:02 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:49:18 - INFO - ✅ 第1轮评估完成: 姚博 | 分数: 0.0
2025-09-01 15:49:18 - INFO - 检测到明确通过标识: ✅ 通过
2025-09-01 15:49:18 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛合格\通过_姚博_第1轮_评估报告.md
2025-09-01 15:49:18 - INFO - 开始提取候选人 姚博 的初筛报告信息
2025-09-01 15:49:18 - INFO - 找到优势文本: **优势:**
    - 拥有蚂蚁集团金融科技公司的工作经验，具备产品经理和项目经理的双重背景。
    - 技能方面涵盖产品设计工具（Framer X）、编程语言（Java, Python等）、数据分析（SQL）及金融行业知识（智能投顾、理财咨询）。
    - 具备敏捷开发经验（RAS敏捷法）和项目管理能力。

- 
2025-09-01 15:49:18 - INFO - 找到风险文本: **风险与不足:**
    - 无任何硬性门槛风险。
    - JD未提供核心职责和技能要求，无法评估职责匹配度。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ✅ 通过  
**筛选理由：** 所有硬性门槛项均匹配，无不匹配项。但由于JD未提供核心职责和技能要求，无法进一步评估职责匹配度，因此默认通过初筛。建议后续补充JD信息后进行详细评估。
2025-09-01 15:49:18 - INFO - 提取的优势和风险: 优势=拥有蚂蚁集团金融科技公司的工作经验，具备产品经理和项目经理的双重背景。；技能方面涵盖产品设计工具（Framer X）、编程语言（Java, Python等）、数据分析（SQL）及金融行业知识（智能投顾、理财咨询）。；具备敏捷开发经验（RAS敏捷法）和项目管理能力。, 风险=无任何硬性门槛风险。；JD未提供核心职责和技能要求，无法评估职责匹配度。
2025-09-01 15:49:18 - INFO - 提取到姓名: 姚博
2025-09-01 15:49:18 - INFO - 提取到年龄: 未提及
2025-09-01 15:49:18 - INFO - 提取到工作经验: 具备蚂蚁集团金融科技有限公司的产品经理和数据标注工程师经验，以及项目经理经验，时间跨度从2022.11至今。
2025-09-01 15:49:18 - INFO - 提取到学历: 北京理工大学珠海学院本科（通信工程专业，2016-2022）
2025-09-01 15:49:18 - INFO - 提取到当前职位: 数据标注工程师 & 项目经理
2025-09-01 15:49:18 - INFO - 提取到期望薪资: 15-18K
2025-09-01 15:49:18 - INFO - 从初筛报告提取的基本信息: {'name': '姚博', 'age': '未提及', 'experience': '具备蚂蚁集团金融科技有限公司的产品经理和数据标注工程师经验，以及项目经理经验，时间跨度从2022.11至今。', 'education': '北京理工大学珠海学院本科（通信工程专业，2016-2022）', 'current_position': '数据标注工程师 & 项目经理', 'expected_salary': '15-18K'}
2025-09-01 15:49:18 - INFO - 候选人 姚博 初筛报告提取完成: advantages=拥有蚂蚁集团金融科技公司的工作经验，具备产品经理和项目经理的双重背景。；技能方面涵盖产品设计工具（Framer X）、编程语言（Java, Python等）、数据分析（SQL）及金融行业知识（智能投顾、理财咨询）。；具备敏捷开发经验（RAS敏捷法）和项目管理能力。, risks=无任何硬性门槛风险。；JD未提供核心职责和技能要求，无法评估职责匹配度。, basic_info={'name': '姚博', 'age': '未提及', 'experience': '具备蚂蚁集团金融科技有限公司的产品经理和数据标注工程师经验，以及项目经理经验，时间跨度从2022.11至今。', 'education': '北京理工大学珠海学院本科（通信工程专业，2016-2022）', 'current_position': '数据标注工程师 & 项目经理', 'expected_salary': '15-18K'}
2025-09-01 15:49:18 - INFO - 开始第2轮评估: 姚博
2025-09-01 15:49:18 - INFO - 岗位名称: 未提及
2025-09-01 15:49:18 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像分析.md
2025-09-01 15:49:18 - INFO - 第2轮评估使用模型: qwen-plus
2025-09-01 15:49:28 - WARNING - 分数不一致: 总分=73.0, 计算总分=0.0 (A:0.0+B:0.0+C:0.0+D:0.0+E:0.0)
2025-09-01 15:49:28 - WARNING - 候选人姚博分数不一致: 总分=73.0, 计算总分=0.0
2025-09-01 15:49:28 - INFO - ✅ 第2轮评估完成: 姚博 | 分数: 73.0
2025-09-01 15:49:28 - INFO - 未检测到明确关键词，默认通过
2025-09-01 15:49:28 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/画像分析结果\姚博_第2轮_画像分析.md
2025-09-01 15:49:28 - INFO - 开始第2.5轮评估: 姚博
2025-09-01 15:49:28 - INFO - 岗位名称: 未提及
2025-09-01 15:49:28 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:49:28 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\专业画像匹配.md
2025-09-01 15:49:28 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:49:31 - INFO - 第2.5轮评估使用模型: qwen-plus
2025-09-01 15:49:35 - INFO - ✅ 第2.5轮评估完成: 姚博 | 分数: 0.0
2025-09-01 15:49:35 - INFO - 第2.5轮检测到明确不通过标识: ❌ 不通过
2025-09-01 15:49:35 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/二筛不合格\未通过_姚博_第2.5轮_评估报告.md
2025-09-01 15:49:35 - INFO - ❌ 姚博 未通过第2.5轮筛选，得分: 0.0
2025-09-01 15:49:43 - INFO - VL识别成功，识别内容长度: 768
2025-09-01 15:49:43 - INFO - ✅ 保存VL识别结果: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\文本简历\VL_Result_未提及/刘俊锋_VL识别结果.txt
2025-09-01 15:49:43 - INFO - 开始处理简历: 刘俊锋
2025-09-01 15:49:43 - INFO - 开始第1轮评估: 刘俊锋
2025-09-01 15:49:43 - INFO - 岗位名称: 未提及
2025-09-01 15:49:43 - INFO - ✅ 使用评估提示词: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\Prompt\最新准则组_8.16\初筛1_自由版.md
2025-09-01 15:49:43 - INFO - 第1轮评估使用模型: qwen-plus
2025-09-01 15:49:59 - INFO - ✅ 第1轮评估完成: 刘俊锋 | 分数: 0.0
2025-09-01 15:49:59 - INFO - 检测到不合格关键词: 不匹配
2025-09-01 15:49:59 - INFO - ✅ 保存评估报告: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\user\杨锐雄\Result\9.1_15：31_未提及_一二点五三轮筛选结果/初筛不合格\未通过_刘俊锋_第1轮_评估报告.md
2025-09-01 15:49:59 - INFO - 开始提取候选人 刘俊锋 的初筛报告信息
2025-09-01 15:49:59 - INFO - 找到优势文本: **优势:**  
    1. 拥有10年以上视觉设计相关经验，曾担任设计总监/经理，具备丰富的项目主导经验。  
    2. 技术栈全面，涵盖2D/3D设计、UI、GIS、前端优化、视频剪辑等多个方向，适应性强。

- 
2025-09-01 15:49:59 - INFO - 找到优势文本: 优势:**  
    1. 拥有10年以上视觉设计相关经验，曾担任设计总监/经理，具备丰富的项目主导经验。  
    2. 技术栈全面，涵盖2D/3D设计、UI、GIS、前端优化、视频剪辑等多个方向，适应性强。

- **
2025-09-01 15:49:59 - INFO - 找到风险文本: **风险与不足:**  
    - 由于JD未提供具体职责和技能要求，无法判断职责匹配度。  
    - 期望职位为“兼职视觉设计师”，若企业需求为全职岗位，则可能存在岗位类型不匹配。

**4. 初筛结论**

- **淘汰条件：**
    1. 硬性门槛审查 中有任意一项结果为 "**不匹配**"。
    2. 核心职责匹配度 评估结果为 "**低**"。
- **通过条件：**
    1. 硬性门槛审查 结果中**没有** "**不匹配**" 项。
    2. 核心职责匹配度 评估结果为 "**中**" 或 "**高**"。

**筛选结果：** ❌ 淘汰  
**筛选理由：** 因岗位描述（JD）未提供核心职责与技能要求，无法进行核心职责匹配度评估，导致无法判断其与岗位的实际匹配程度。此外，候选人期望为“兼职”岗位，若该岗位仅限全职，则存在岗位类型不匹配的风险。在信息不完整的情况下，建议不予通过或要求补充JD内容后再评估。
2025-09-01 15:49:59 - INFO - 提取的优势和风险: 优势=**, 风险=由于JD未提供具体职责和技能要求，无法判断职责匹配度。  ；期望职位为“兼职视觉设计师”，若企业需求为全职岗位，则可能存在岗位类型不匹配。
2025-09-01 15:49:59 - INFO - 提取到姓名: 陈燕虹
2025-09-01 15:49:59 - INFO - 提取到年龄: 36岁
2025-09-01 15:49:59 - INFO - 提取到工作经验: 10年以上
2025-09-01 15:49:59 - INFO - 提取到学历: 本科
2025-09-01 15:49:59 - INFO - 提取到当前职位: 在职
2025-09-01 15:49:59 - INFO - 提取到期望薪资: 未提及
2025-09-01 15:49:59 - INFO - 从初筛报告提取的基本信息: {'name': '陈燕虹', 'age': '36岁', 'experience': '10年以上', 'education': '本科', 'current_position': '在职', 'expected_salary': '未提及'}
2025-09-01 15:49:59 - INFO - 候选人 刘俊锋 初筛报告提取完成: advantages=**, risks=由于JD未提供具体职责和技能要求，无法判断职责匹配度。  ；期望职位为“兼职视觉设计师”，若企业需求为全职岗位，则可能存在岗位类型不匹配。, basic_info={'name': '陈燕虹', 'age': '36岁', 'experience': '10年以上', 'education': '本科', 'current_position': '在职', 'expected_salary': '未提及'}
2025-09-01 15:49:59 - INFO - ❌ 刘俊锋 未通过第1轮筛选，得分: 0.0
