2025-09-01 10:33:04 - browser_controller - INFO - 正在初始化浏览器...
2025-09-01 10:33:06 - browser_controller - INFO - 浏览器初始化完成，等待页面加载...
2025-09-01 10:34:34 - browser_controller - INFO - 正在初始化浏览器...
2025-09-01 10:34:34 - browser_controller - INFO - 浏览器初始化完成，等待页面加载...
2025-09-01 10:34:53 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:34:53 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:34:53 - browser_controller - INFO - 获取到用户姓名: 叶如杰
2025-09-01 10:34:53 - browser_controller - INFO - 点击简历卡片完成
2025-09-01 10:34:57 - browser_controller - INFO - 通过索引获取iframe1成功
2025-09-01 10:35:15 - browser_controller - WARNING - 无法获取iframe2，使用iframe1
2025-09-01 10:35:26 - browser_controller - WARNING - 未找到关闭按钮
2025-09-01 10:35:36 - browser_controller - WARNING - 未找到简历canvas
2025-09-01 10:35:47 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:35:47 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:35:59 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:35:59 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:36:11 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:36:11 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:36:22 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:36:33 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:36:33 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:36:45 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:36:45 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:36:57 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:36:57 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:37:08 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:37:19 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:37:19 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:37:31 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:37:31 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:37:43 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:37:43 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:37:54 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:38:05 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:38:05 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:38:17 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:38:17 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:38:29 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:38:29 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:38:40 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:38:51 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:38:51 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:39:04 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:39:04 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:39:16 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:39:16 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:39:27 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:39:38 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:39:38 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:39:50 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:39:50 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:40:02 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:40:02 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:40:13 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:40:24 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:40:24 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:40:36 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:40:36 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:40:48 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:40:48 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:40:59 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:41:10 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:41:10 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:41:22 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:41:22 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:41:34 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:41:34 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:41:45 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:41:56 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:41:56 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:42:08 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:42:08 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:42:20 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:42:20 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:42:31 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:42:42 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:42:42 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:42:54 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:42:54 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:43:06 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:43:06 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:43:17 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:43:28 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:43:28 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:43:40 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:43:40 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:43:52 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:43:52 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:44:03 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:44:14 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:44:14 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:44:26 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:44:26 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:44:38 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:44:38 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:44:49 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:45:00 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:45:00 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:45:12 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:45:12 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:45:24 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:45:24 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:45:35 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:45:46 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:45:46 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:45:58 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:45:58 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:46:10 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:46:10 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:46:21 - browser_controller - WARNING - 未找到用户姓名元素
2025-09-01 10:46:28 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:31 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:31 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:32 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:34 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:36 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:37 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:38 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:40 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:42 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:43 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:44 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:46 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:48 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:49 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:50 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:52 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:54 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:46:55 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:49:17 - browser_controller - INFO - 正在初始化浏览器...
2025-09-01 10:49:18 - browser_controller - INFO - 浏览器初始化完成，等待页面加载...
2025-09-01 10:49:55 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:49:55 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:49:55 - browser_controller - INFO - 获取到用户姓名: 姚博
2025-09-01 10:49:55 - browser_controller - INFO - 点击简历卡片完成
2025-09-01 10:49:59 - browser_controller - INFO - 通过索引获取iframe1成功
2025-09-01 10:49:59 - browser_controller - INFO - 获取iframe2成功
2025-09-01 10:50:00 - browser_controller - INFO - 找到关闭按钮
2025-09-01 10:50:00 - browser_controller - INFO - 找到简历canvas
2025-09-01 10:50:04 - browser_controller - ERROR - 关闭简历弹窗失败: 
该元素没有位置及大小。
版本: 4.1.1.2
2025-09-01 10:50:15 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:50:15 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:50:16 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:18 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:20 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:21 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:22 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:24 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:26 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:27 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:28 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:30 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:32 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:33 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:34 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:36 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:38 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:39 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:40 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:42 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:44 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:45 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:46 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:48 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:50 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:51 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:52 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:54 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:56 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:57 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:50:58 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:51:00 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:51:02 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:51:03 - browser_controller - INFO - 正在初始化浏览器...
2025-09-01 10:51:03 - browser_controller - INFO - 浏览器初始化完成，等待页面加载...
2025-09-01 10:51:29 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:51:30 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:51:41 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:51:41 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:51:41 - browser_controller - INFO - 获取到用户姓名: 范女士
2025-09-01 10:51:41 - browser_controller - INFO - 点击简历卡片完成
2025-09-01 10:51:45 - browser_controller - INFO - 通过索引获取iframe1成功
2025-09-01 10:51:45 - browser_controller - INFO - 获取iframe2成功
2025-09-01 10:51:46 - browser_controller - INFO - 找到关闭按钮
2025-09-01 10:51:46 - browser_controller - INFO - 找到简历canvas
2025-09-01 10:52:22 - browser_controller - INFO - 关闭简历弹窗成功
2025-09-01 10:52:33 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:52:33 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:52:44 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:52:44 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:52:45 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:52:56 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:52:56 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:52:57 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:53:08 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:53:08 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:53:08 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:53:09 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:53:09 - browser_controller - INFO - 获取到用户姓名: 刘丹
2025-09-01 10:53:10 - browser_controller - INFO - 点击简历卡片完成
2025-09-01 10:53:14 - browser_controller - INFO - 通过索引获取iframe1成功
2025-09-01 10:53:14 - browser_controller - INFO - 获取iframe2成功
2025-09-01 10:53:15 - browser_controller - INFO - 找到关闭按钮
2025-09-01 10:53:15 - browser_controller - INFO - 找到简历canvas
2025-09-01 10:53:20 - browser_controller - INFO - 关闭简历弹窗成功
2025-09-01 10:53:31 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:53:31 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:53:42 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:53:42 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:53:43 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:53:54 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:53:54 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:53:55 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:54:06 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:54:06 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:54:06 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:54:07 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:54:07 - browser_controller - INFO - 获取到用户姓名: 刘春秀
2025-09-01 10:54:07 - browser_controller - INFO - 点击简历卡片完成
2025-09-01 10:54:11 - browser_controller - INFO - 通过索引获取iframe1成功
2025-09-01 10:54:11 - browser_controller - INFO - 获取iframe2成功
2025-09-01 10:54:12 - browser_controller - INFO - 找到关闭按钮
2025-09-01 10:54:12 - browser_controller - INFO - 找到简历canvas
2025-09-01 10:54:18 - browser_controller - INFO - 关闭简历弹窗成功
2025-09-01 10:54:29 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:54:29 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:54:40 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:54:40 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:54:41 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:54:52 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:54:52 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:54:53 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:55:04 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:55:04 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:55:04 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:55:05 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:55:05 - browser_controller - INFO - 获取到用户姓名: 李杏娜
2025-09-01 10:55:05 - browser_controller - INFO - 点击简历卡片完成
2025-09-01 10:55:09 - browser_controller - INFO - 通过索引获取iframe1成功
2025-09-01 10:55:09 - browser_controller - INFO - 获取iframe2成功
2025-09-01 10:55:10 - browser_controller - INFO - 找到关闭按钮
2025-09-01 10:55:10 - browser_controller - INFO - 找到简历canvas
2025-09-01 10:55:15 - browser_controller - INFO - 关闭简历弹窗成功
2025-09-01 10:55:26 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:55:26 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:55:37 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:55:37 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:55:38 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:55:49 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:55:49 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:55:50 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:01 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:56:01 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:56:01 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:02 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:02 - browser_controller - INFO - 获取到用户姓名: 金女士
2025-09-01 10:56:02 - browser_controller - INFO - 点击简历卡片完成
2025-09-01 10:56:06 - browser_controller - INFO - 通过索引获取iframe1成功
2025-09-01 10:56:06 - browser_controller - INFO - 获取iframe2成功
2025-09-01 10:56:07 - browser_controller - INFO - 找到关闭按钮
2025-09-01 10:56:07 - browser_controller - INFO - 找到简历canvas
2025-09-01 10:56:12 - browser_controller - INFO - 关闭简历弹窗成功
2025-09-01 10:56:23 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 10:56:23 - browser_controller - INFO - 未检测到弹窗
2025-09-01 10:56:24 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:26 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:27 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:28 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:30 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:32 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:33 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:34 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:37 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:39 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:40 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:41 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:43 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:45 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:46 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:47 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:49 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:51 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:52 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:53 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:55 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:57 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:58 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:56:59 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:57:01 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:57:03 - browser_controller - ERROR - 关闭弹窗失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 10:57:04 - browser_controller - ERROR - 获取用户姓名失败: 
与页面的连接已断开。
版本: 4.1.1.2
2025-09-01 15:21:46 - browser_controller - INFO - 正在初始化浏览器...
2025-09-01 15:21:47 - browser_controller - INFO - 浏览器初始化完成，等待页面加载...
2025-09-01 15:22:24 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 15:22:24 - browser_controller - INFO - 未检测到弹窗
2025-09-01 15:22:24 - browser_controller - INFO - 获取到用户姓名: 叶利广
2025-09-01 15:22:24 - browser_controller - INFO - 点击简历卡片完成
2025-09-01 15:22:28 - browser_controller - INFO - 通过索引获取iframe1成功
2025-09-01 15:22:28 - browser_controller - INFO - 获取iframe2成功
2025-09-01 15:22:29 - browser_controller - INFO - 找到关闭按钮
2025-09-01 15:22:29 - browser_controller - INFO - 找到简历canvas
2025-09-01 15:22:36 - browser_controller - INFO - 关闭简历弹窗成功
2025-09-01 15:22:47 - browser_controller - INFO - <NoneElement method=ele(), locator=x://img[contains(@class, "bosszp-bg")], index=1, timeout=10>
2025-09-01 15:22:47 - browser_controller - INFO - 未检测到弹窗
