2025-09-01 10:33:04 - resume_collector - INFO - 开始检查岗位匹配，目标岗位: 人力资源主管
2025-09-01 10:33:04 - resume_collector - INFO - 正在初始化浏览器...
2025-09-01 10:33:08 - resume_collector - INFO - 浏览器初始化成功，等待页面加载...
2025-09-01 10:33:13 - resume_collector - INFO - 使用DrissionPage XPath获取岗位信息: x://*[@id="headerWrap"]/div/div/div[2]/div[1]
2025-09-01 10:33:23 - resume_collector - WARNING - 未找到岗位推荐元素
2025-09-01 10:33:23 - resume_collector - ERROR - 所有XPath选择器都无法获取岗位推荐信息
2025-09-01 10:34:34 - resume_collector - INFO - 开始检查岗位匹配，目标岗位: AI全栈工程师
2025-09-01 10:34:34 - resume_collector - INFO - 正在初始化浏览器...
2025-09-01 10:34:36 - resume_collector - INFO - 浏览器初始化成功，等待页面加载...
2025-09-01 10:34:41 - resume_collector - INFO - 使用DrissionPage XPath获取岗位信息: x://*[@id="headerWrap"]/div/div/div[2]/div[1]
2025-09-01 10:34:41 - resume_collector - INFO - 成功获取岗位信息: 'AI全栈工程师 _ 珠海 15-20K'
2025-09-01 10:34:41 - resume_collector - INFO - 获取到岗位推荐信息: 'AI全栈工程师 _ 珠海 15-20K'
2025-09-01 10:34:41 - resume_collector - INFO - 使用下划线分割提取的岗位名称: 'AI全栈工程师'
2025-09-01 10:34:41 - resume_collector - INFO - 匹配比较: 目标岗位='ai全栈工程师' vs 当前岗位='ai全栈工程师'
2025-09-01 10:34:41 - resume_collector - INFO - 匹配结果: True, 原因: 完全匹配
2025-09-01 10:34:42 - resume_collector - INFO - 岗位匹配成功: 'AI全栈工程师' 匹配 'AI全栈工程师' (完全匹配)
2025-09-01 10:34:42 - resume_collector - INFO - 岗位匹配成功，保留浏览器用于采集
2025-09-01 10:34:42 - resume_collector - INFO - 岗位匹配成功，保存浏览器实例供采集使用
2025-09-01 10:34:42 - resume_collector - INFO - 检测到浏览器已初始化标志，使用保存的浏览器实例
2025-09-01 10:34:42 - resume_collector - INFO - 开始执行收集任务: task_1
2025-09-01 10:34:42 - resume_collector - INFO - === 简历自动采集系统 ===
2025-09-01 10:34:42 - resume_collector - INFO - 将采集 20 份简历
2025-09-01 10:34:42 - resume_collector - INFO - 职位: AI全栈工程师
2025-09-01 10:34:42 - resume_collector - INFO - 检测到浏览器已初始化标志，跳过浏览器初始化，直接开始采集...
2025-09-01 10:34:42 - resume_collector - INFO - 创建简历文件夹: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\resumes\AI全栈工程师
2025-09-01 10:34:42 - resume_collector - INFO - 创建临时截图文件夹: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\temp_screenshots\AI全栈工程师
2025-09-01 10:34:42 - resume_collector - INFO - 开始采集前检查并关闭弹窗...
2025-09-01 10:34:53 - resume_collector - INFO - 开始处理第 1 份简历
2025-09-01 10:34:53 - resume_collector - INFO - === 开始处理第 1 个简历 ===
2025-09-01 10:35:36 - resume_collector - WARNING - 未找到canvas元素
2025-09-01 10:35:36 - resume_collector - WARNING - 第 1 份简历采集失败: 未找到canvas元素
2025-09-01 10:35:36 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:36:12 - resume_collector - INFO - 开始处理第 2 份简历
2025-09-01 10:36:12 - resume_collector - INFO - === 开始处理第 2 个简历 ===
2025-09-01 10:36:22 - resume_collector - WARNING - 第 2 份简历采集失败: 获取用户姓名失败
2025-09-01 10:36:22 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:36:58 - resume_collector - INFO - 开始处理第 3 份简历
2025-09-01 10:36:58 - resume_collector - INFO - === 开始处理第 3 个简历 ===
2025-09-01 10:37:08 - resume_collector - WARNING - 第 3 份简历采集失败: 获取用户姓名失败
2025-09-01 10:37:08 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:37:44 - resume_collector - INFO - 开始处理第 4 份简历
2025-09-01 10:37:44 - resume_collector - INFO - === 开始处理第 4 个简历 ===
2025-09-01 10:37:54 - resume_collector - WARNING - 第 4 份简历采集失败: 获取用户姓名失败
2025-09-01 10:37:54 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:38:30 - resume_collector - INFO - 开始处理第 5 份简历
2025-09-01 10:38:30 - resume_collector - INFO - === 开始处理第 5 个简历 ===
2025-09-01 10:38:40 - resume_collector - WARNING - 第 5 份简历采集失败: 获取用户姓名失败
2025-09-01 10:38:40 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:39:17 - resume_collector - INFO - 开始处理第 6 份简历
2025-09-01 10:39:17 - resume_collector - INFO - === 开始处理第 6 个简历 ===
2025-09-01 10:39:27 - resume_collector - WARNING - 第 6 份简历采集失败: 获取用户姓名失败
2025-09-01 10:39:27 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:40:03 - resume_collector - INFO - 开始处理第 7 份简历
2025-09-01 10:40:03 - resume_collector - INFO - === 开始处理第 7 个简历 ===
2025-09-01 10:40:13 - resume_collector - WARNING - 第 7 份简历采集失败: 获取用户姓名失败
2025-09-01 10:40:13 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:40:49 - resume_collector - INFO - 开始处理第 8 份简历
2025-09-01 10:40:49 - resume_collector - INFO - === 开始处理第 8 个简历 ===
2025-09-01 10:40:59 - resume_collector - WARNING - 第 8 份简历采集失败: 获取用户姓名失败
2025-09-01 10:40:59 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:41:35 - resume_collector - INFO - 开始处理第 9 份简历
2025-09-01 10:41:35 - resume_collector - INFO - === 开始处理第 9 个简历 ===
2025-09-01 10:41:45 - resume_collector - WARNING - 第 9 份简历采集失败: 获取用户姓名失败
2025-09-01 10:41:45 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:42:21 - resume_collector - INFO - 开始处理第 10 份简历
2025-09-01 10:42:21 - resume_collector - INFO - === 开始处理第 10 个简历 ===
2025-09-01 10:42:31 - resume_collector - WARNING - 第 10 份简历采集失败: 获取用户姓名失败
2025-09-01 10:42:31 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:43:07 - resume_collector - INFO - 开始处理第 11 份简历
2025-09-01 10:43:07 - resume_collector - INFO - === 开始处理第 11 个简历 ===
2025-09-01 10:43:17 - resume_collector - WARNING - 第 11 份简历采集失败: 获取用户姓名失败
2025-09-01 10:43:17 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:43:53 - resume_collector - INFO - 开始处理第 12 份简历
2025-09-01 10:43:53 - resume_collector - INFO - === 开始处理第 12 个简历 ===
2025-09-01 10:44:03 - resume_collector - WARNING - 第 12 份简历采集失败: 获取用户姓名失败
2025-09-01 10:44:03 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:44:39 - resume_collector - INFO - 开始处理第 13 份简历
2025-09-01 10:44:39 - resume_collector - INFO - === 开始处理第 13 个简历 ===
2025-09-01 10:44:49 - resume_collector - WARNING - 第 13 份简历采集失败: 获取用户姓名失败
2025-09-01 10:44:49 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:45:25 - resume_collector - INFO - 开始处理第 14 份简历
2025-09-01 10:45:25 - resume_collector - INFO - === 开始处理第 14 个简历 ===
2025-09-01 10:45:35 - resume_collector - WARNING - 第 14 份简历采集失败: 获取用户姓名失败
2025-09-01 10:45:35 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:46:11 - resume_collector - INFO - 开始处理第 15 份简历
2025-09-01 10:46:11 - resume_collector - INFO - === 开始处理第 15 个简历 ===
2025-09-01 10:46:21 - resume_collector - WARNING - 第 15 份简历采集失败: 获取用户姓名失败
2025-09-01 10:46:21 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:46:31 - resume_collector - INFO - 等待被取消，收到停止信号
2025-09-01 10:46:31 - resume_collector - INFO - 开始处理第 16 份简历
2025-09-01 10:46:31 - resume_collector - INFO - === 开始处理第 16 个简历 ===
2025-09-01 10:46:31 - resume_collector - WARNING - 第 16 份简历采集失败: 获取用户姓名失败
2025-09-01 10:46:31 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:46:37 - resume_collector - INFO - 开始处理第 17 份简历
2025-09-01 10:46:37 - resume_collector - INFO - === 开始处理第 17 个简历 ===
2025-09-01 10:46:37 - resume_collector - WARNING - 第 17 份简历采集失败: 获取用户姓名失败
2025-09-01 10:46:37 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:46:43 - resume_collector - INFO - 开始处理第 18 份简历
2025-09-01 10:46:43 - resume_collector - INFO - === 开始处理第 18 个简历 ===
2025-09-01 10:46:43 - resume_collector - WARNING - 第 18 份简历采集失败: 获取用户姓名失败
2025-09-01 10:46:43 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:46:49 - resume_collector - INFO - 开始处理第 19 份简历
2025-09-01 10:46:49 - resume_collector - INFO - === 开始处理第 19 个简历 ===
2025-09-01 10:46:49 - resume_collector - WARNING - 第 19 份简历采集失败: 获取用户姓名失败
2025-09-01 10:46:49 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:46:55 - resume_collector - INFO - 开始处理第 20 份简历
2025-09-01 10:46:55 - resume_collector - INFO - === 开始处理第 20 个简历 ===
2025-09-01 10:46:55 - resume_collector - WARNING - 第 20 份简历采集失败: 获取用户姓名失败
2025-09-01 10:46:55 - resume_collector - INFO - === 采集完成 ===
2025-09-01 10:46:55 - resume_collector - INFO - 总共计划采集: 20 份
2025-09-01 10:46:55 - resume_collector - INFO - 成功采集: 0 份
2025-09-01 10:46:55 - resume_collector - INFO - 失败: 20 份
2025-09-01 10:46:55 - resume_collector - INFO - 已删除临时文件夹: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\temp_screenshots\AI全栈工程师
2025-09-01 10:46:55 - resume_collector - INFO - 收集任务完成: task_1, 成功收集 0 份简历
2025-09-01 10:49:17 - resume_collector - INFO - 开始检查岗位匹配，目标岗位: AI产品经理
2025-09-01 10:49:17 - resume_collector - INFO - 正在初始化浏览器...
2025-09-01 10:49:20 - resume_collector - INFO - 浏览器初始化成功，等待页面加载...
2025-09-01 10:49:25 - resume_collector - INFO - 使用DrissionPage XPath获取岗位信息: x://*[@id="headerWrap"]/div/div/div[2]/div[1]
2025-09-01 10:49:25 - resume_collector - INFO - 成功获取岗位信息: 'AI全栈工程师 _ 珠海 15-20K'
2025-09-01 10:49:25 - resume_collector - INFO - 获取到岗位推荐信息: 'AI全栈工程师 _ 珠海 15-20K'
2025-09-01 10:49:25 - resume_collector - INFO - 使用下划线分割提取的岗位名称: 'AI全栈工程师'
2025-09-01 10:49:25 - resume_collector - INFO - 匹配比较: 目标岗位='ai产品经理' vs 当前岗位='ai全栈工程师'
2025-09-01 10:49:25 - resume_collector - INFO - 匹配结果: False, 原因: 
2025-09-01 10:49:25 - resume_collector - WARNING - 岗位不匹配: 'AI全栈工程师' 不匹配 'AI产品经理'
2025-09-01 10:49:25 - resume_collector - INFO - 开始自动搜索匹配的岗位...
2025-09-01 10:49:25 - resume_collector - INFO - 开始搜索岗位: AI产品经理
2025-09-01 10:49:25 - resume_collector - INFO - 成功点击岗位下拉框
2025-09-01 10:49:26 - resume_collector - INFO - 成功输入岗位名称: AI产品经理
2025-09-01 10:49:38 - resume_collector - INFO - 找到匹配选项: AI产品经理 _ 珠海 14-20K
2025-09-01 10:49:38 - resume_collector - INFO - 成功选择岗位: AI产品经理 _ 珠海 14-20K
2025-09-01 10:49:41 - resume_collector - INFO - 重新验证岗位匹配...
2025-09-01 10:49:43 - resume_collector - INFO - 重新验证 - 获取到岗位推荐信息: 'AI产品经理 _ 珠海 14-20K'
2025-09-01 10:49:43 - resume_collector - INFO - 重新验证结果: True, 当前岗位: AI产品经理
2025-09-01 10:49:43 - resume_collector - INFO - 岗位匹配成功，保留浏览器用于采集
2025-09-01 10:49:43 - resume_collector - INFO - 岗位匹配成功，保存浏览器实例供采集使用
2025-09-01 10:49:44 - resume_collector - INFO - 检测到浏览器已初始化标志，使用保存的浏览器实例
2025-09-01 10:49:44 - resume_collector - INFO - 开始执行收集任务: task_1
2025-09-01 10:49:44 - resume_collector - INFO - === 简历自动采集系统 ===
2025-09-01 10:49:44 - resume_collector - INFO - 将采集 20 份简历
2025-09-01 10:49:44 - resume_collector - INFO - 职位: AI产品经理
2025-09-01 10:49:44 - resume_collector - INFO - 检测到浏览器已初始化标志，跳过浏览器初始化，直接开始采集...
2025-09-01 10:49:44 - resume_collector - INFO - 创建临时截图文件夹: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\temp_screenshots\AI产品经理
2025-09-01 10:49:44 - resume_collector - INFO - 开始采集前检查并关闭弹窗...
2025-09-01 10:49:55 - resume_collector - INFO - 开始处理第 1 份简历
2025-09-01 10:49:55 - resume_collector - INFO - === 开始处理第 1 个简历 ===
2025-09-01 10:50:00 - resume_collector - INFO - 找到canvas元素，开始滚动截图...
2025-09-01 10:50:02 - resume_collector - INFO - 简历图片已保存到: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\resumes\AI产品经理\姚博_AI产品经理_20250901_105002.png
2025-09-01 10:50:15 - resume_collector - INFO - === 第 1 个简历处理完成 ===
2025-09-01 10:50:15 - resume_collector - INFO - 成功采集第 1 份简历: 姚博
2025-09-01 10:50:15 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:50:21 - resume_collector - INFO - 开始处理第 2 份简历
2025-09-01 10:50:21 - resume_collector - INFO - === 开始处理第 2 个简历 ===
2025-09-01 10:50:21 - resume_collector - WARNING - 第 2 份简历采集失败: 获取用户姓名失败
2025-09-01 10:50:21 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:50:27 - resume_collector - INFO - 开始处理第 3 份简历
2025-09-01 10:50:27 - resume_collector - INFO - === 开始处理第 3 个简历 ===
2025-09-01 10:50:27 - resume_collector - WARNING - 第 3 份简历采集失败: 获取用户姓名失败
2025-09-01 10:50:27 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:50:33 - resume_collector - INFO - 开始处理第 4 份简历
2025-09-01 10:50:33 - resume_collector - INFO - === 开始处理第 4 个简历 ===
2025-09-01 10:50:33 - resume_collector - WARNING - 第 4 份简历采集失败: 获取用户姓名失败
2025-09-01 10:50:33 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:50:39 - resume_collector - INFO - 开始处理第 5 份简历
2025-09-01 10:50:39 - resume_collector - INFO - === 开始处理第 5 个简历 ===
2025-09-01 10:50:39 - resume_collector - WARNING - 第 5 份简历采集失败: 获取用户姓名失败
2025-09-01 10:50:39 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:50:45 - resume_collector - INFO - 开始处理第 6 份简历
2025-09-01 10:50:45 - resume_collector - INFO - === 开始处理第 6 个简历 ===
2025-09-01 10:50:45 - resume_collector - WARNING - 第 6 份简历采集失败: 获取用户姓名失败
2025-09-01 10:50:45 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:50:51 - resume_collector - INFO - 开始处理第 7 份简历
2025-09-01 10:50:51 - resume_collector - INFO - === 开始处理第 7 个简历 ===
2025-09-01 10:50:51 - resume_collector - WARNING - 第 7 份简历采集失败: 获取用户姓名失败
2025-09-01 10:50:51 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:50:57 - resume_collector - INFO - 开始处理第 8 份简历
2025-09-01 10:50:57 - resume_collector - INFO - === 开始处理第 8 个简历 ===
2025-09-01 10:50:57 - resume_collector - WARNING - 第 8 份简历采集失败: 获取用户姓名失败
2025-09-01 10:50:57 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:51:03 - resume_collector - INFO - 开始检查岗位匹配，目标岗位: 人力资源主管
2025-09-01 10:51:03 - resume_collector - INFO - 正在初始化浏览器...
2025-09-01 10:51:05 - resume_collector - INFO - 浏览器初始化成功，等待页面加载...
2025-09-01 10:51:10 - resume_collector - INFO - 使用DrissionPage XPath获取岗位信息: x://*[@id="headerWrap"]/div/div/div[2]/div[1]
2025-09-01 10:51:10 - resume_collector - INFO - 成功获取岗位信息: 'AI全栈工程师 _ 珠海 15-20K'
2025-09-01 10:51:10 - resume_collector - INFO - 获取到岗位推荐信息: 'AI全栈工程师 _ 珠海 15-20K'
2025-09-01 10:51:10 - resume_collector - INFO - 使用下划线分割提取的岗位名称: 'AI全栈工程师'
2025-09-01 10:51:10 - resume_collector - INFO - 匹配比较: 目标岗位='人力资源主管' vs 当前岗位='ai全栈工程师'
2025-09-01 10:51:10 - resume_collector - INFO - 匹配结果: False, 原因: 
2025-09-01 10:51:10 - resume_collector - WARNING - 岗位不匹配: 'AI全栈工程师' 不匹配 '人力资源主管'
2025-09-01 10:51:10 - resume_collector - INFO - 开始自动搜索匹配的岗位...
2025-09-01 10:51:10 - resume_collector - INFO - 开始搜索岗位: 人力资源主管
2025-09-01 10:51:11 - resume_collector - INFO - 成功点击岗位下拉框
2025-09-01 10:51:12 - resume_collector - INFO - 成功输入岗位名称: 人力资源主管
2025-09-01 10:51:24 - resume_collector - INFO - 找到匹配选项: 人力资源主管 _ 珠海 6-7K
2025-09-01 10:51:24 - resume_collector - INFO - 成功选择岗位: 人力资源主管 _ 珠海 6-7K
2025-09-01 10:51:27 - resume_collector - INFO - 重新验证岗位匹配...
2025-09-01 10:51:29 - resume_collector - INFO - 重新验证 - 获取到岗位推荐信息: '人力资源主管 _ 珠海 6-7K'
2025-09-01 10:51:29 - resume_collector - INFO - 重新验证结果: True, 当前岗位: 人力资源主管
2025-09-01 10:51:29 - resume_collector - INFO - 岗位匹配成功，保留浏览器用于采集
2025-09-01 10:51:29 - resume_collector - INFO - 岗位匹配成功，保存浏览器实例供采集使用
2025-09-01 10:51:29 - resume_collector - INFO - 开始处理第 9 份简历
2025-09-01 10:51:29 - resume_collector - INFO - === 开始处理第 9 个简历 ===
2025-09-01 10:51:29 - resume_collector - WARNING - 第 9 份简历采集失败: 获取用户姓名失败
2025-09-01 10:51:29 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:51:30 - resume_collector - INFO - 检测到浏览器已初始化标志，使用保存的浏览器实例
2025-09-01 10:51:30 - resume_collector - INFO - 开始执行收集任务: task_2
2025-09-01 10:51:30 - resume_collector - INFO - === 简历自动采集系统 ===
2025-09-01 10:51:30 - resume_collector - INFO - 将采集 5 份简历
2025-09-01 10:51:30 - resume_collector - INFO - 职位: 人力资源主管
2025-09-01 10:51:30 - resume_collector - INFO - 检测到浏览器已初始化标志，跳过浏览器初始化，直接开始采集...
2025-09-01 10:51:30 - resume_collector - INFO - 创建临时截图文件夹: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\temp_screenshots\人力资源主管
2025-09-01 10:51:30 - resume_collector - INFO - 开始采集前检查并关闭弹窗...
2025-09-01 10:51:41 - resume_collector - INFO - 开始处理第 1 份简历
2025-09-01 10:51:41 - resume_collector - INFO - === 开始处理第 1 个简历 ===
2025-09-01 10:51:46 - resume_collector - INFO - 找到canvas元素，开始滚动截图...
2025-09-01 10:52:21 - resume_collector - INFO - 简历图片已保存到: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\resumes\人力资源主管\范女士_人力资源主管_20250901_105221.png
2025-09-01 10:52:33 - resume_collector - INFO - === 第 1 个简历处理完成 ===
2025-09-01 10:52:33 - resume_collector - INFO - 成功采集第 1 份简历: 范女士
2025-09-01 10:52:33 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:53:08 - resume_collector - INFO - 开始处理第 10 份简历
2025-09-01 10:53:08 - resume_collector - INFO - === 开始处理第 10 个简历 ===
2025-09-01 10:53:08 - resume_collector - WARNING - 第 10 份简历采集失败: 获取用户姓名失败
2025-09-01 10:53:08 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:53:09 - resume_collector - INFO - 开始处理第 2 份简历
2025-09-01 10:53:09 - resume_collector - INFO - === 开始处理第 2 个简历 ===
2025-09-01 10:53:15 - resume_collector - INFO - 找到canvas元素，开始滚动截图...
2025-09-01 10:53:18 - resume_collector - INFO - 简历图片已保存到: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\resumes\人力资源主管\刘丹_人力资源主管_20250901_105318.png
2025-09-01 10:53:31 - resume_collector - INFO - === 第 2 个简历处理完成 ===
2025-09-01 10:53:31 - resume_collector - INFO - 成功采集第 2 份简历: 刘丹
2025-09-01 10:53:31 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:54:06 - resume_collector - INFO - 开始处理第 11 份简历
2025-09-01 10:54:06 - resume_collector - INFO - === 开始处理第 11 个简历 ===
2025-09-01 10:54:06 - resume_collector - WARNING - 第 11 份简历采集失败: 获取用户姓名失败
2025-09-01 10:54:06 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:54:07 - resume_collector - INFO - 开始处理第 3 份简历
2025-09-01 10:54:07 - resume_collector - INFO - === 开始处理第 3 个简历 ===
2025-09-01 10:54:12 - resume_collector - INFO - 找到canvas元素，开始滚动截图...
2025-09-01 10:54:17 - resume_collector - INFO - 简历图片已保存到: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\resumes\人力资源主管\刘春秀_人力资源主管_20250901_105417.png
2025-09-01 10:54:29 - resume_collector - INFO - === 第 3 个简历处理完成 ===
2025-09-01 10:54:29 - resume_collector - INFO - 成功采集第 3 份简历: 刘春秀
2025-09-01 10:54:29 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:55:04 - resume_collector - INFO - 开始处理第 12 份简历
2025-09-01 10:55:04 - resume_collector - INFO - === 开始处理第 12 个简历 ===
2025-09-01 10:55:04 - resume_collector - WARNING - 第 12 份简历采集失败: 获取用户姓名失败
2025-09-01 10:55:04 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:55:05 - resume_collector - INFO - 开始处理第 4 份简历
2025-09-01 10:55:05 - resume_collector - INFO - === 开始处理第 4 个简历 ===
2025-09-01 10:55:10 - resume_collector - INFO - 找到canvas元素，开始滚动截图...
2025-09-01 10:55:14 - resume_collector - INFO - 简历图片已保存到: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\resumes\人力资源主管\李杏娜_人力资源主管_20250901_105514.png
2025-09-01 10:55:26 - resume_collector - INFO - === 第 4 个简历处理完成 ===
2025-09-01 10:55:26 - resume_collector - INFO - 成功采集第 4 份简历: 李杏娜
2025-09-01 10:55:26 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:56:01 - resume_collector - INFO - 开始处理第 13 份简历
2025-09-01 10:56:01 - resume_collector - INFO - === 开始处理第 13 个简历 ===
2025-09-01 10:56:01 - resume_collector - WARNING - 第 13 份简历采集失败: 获取用户姓名失败
2025-09-01 10:56:01 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:56:02 - resume_collector - INFO - 开始处理第 5 份简历
2025-09-01 10:56:02 - resume_collector - INFO - === 开始处理第 5 个简历 ===
2025-09-01 10:56:07 - resume_collector - INFO - 找到canvas元素，开始滚动截图...
2025-09-01 10:56:11 - resume_collector - INFO - 简历图片已保存到: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\resumes\人力资源主管\金女士_人力资源主管_20250901_105611.png
2025-09-01 10:56:23 - resume_collector - INFO - === 第 5 个简历处理完成 ===
2025-09-01 10:56:23 - resume_collector - INFO - 成功采集第 5 份简历: 金女士
2025-09-01 10:56:23 - resume_collector - INFO - === 采集完成 ===
2025-09-01 10:56:23 - resume_collector - INFO - 总共计划采集: 5 份
2025-09-01 10:56:23 - resume_collector - INFO - 成功采集: 5 份
2025-09-01 10:56:23 - resume_collector - INFO - 失败: 0 份
2025-09-01 10:56:23 - resume_collector - INFO - 已删除临时文件夹: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\temp_screenshots\人力资源主管
2025-09-01 10:56:23 - resume_collector - INFO - 收集任务完成: task_2, 成功收集 5 份简历
2025-09-01 10:56:26 - resume_collector - INFO - 原始路径: resumes/人力资源主管/金女士_人力资源主管_20250901_105611.png
2025-09-01 10:56:26 - resume_collector - INFO - 解码后路径: resumes\人力资源主管\金女士_人力资源主管_20250901_105611.png
2025-09-01 10:56:26 - resume_collector - INFO - 标准化路径: resumes\人力资源主管\金女士_人力资源主管_20250901_105611.png
2025-09-01 10:56:26 - resume_collector - INFO - 原始路径: resumes/人力资源主管/李杏娜_人力资源主管_20250901_105514.png
2025-09-01 10:56:26 - resume_collector - INFO - 解码后路径: resumes\人力资源主管\李杏娜_人力资源主管_20250901_105514.png
2025-09-01 10:56:26 - resume_collector - INFO - 标准化路径: resumes\人力资源主管\李杏娜_人力资源主管_20250901_105514.png
2025-09-01 10:56:26 - resume_collector - INFO - 原始路径: resumes/人力资源主管/刘春秀_人力资源主管_20250901_105417.png
2025-09-01 10:56:26 - resume_collector - INFO - 解码后路径: resumes\人力资源主管\刘春秀_人力资源主管_20250901_105417.png
2025-09-01 10:56:26 - resume_collector - INFO - 标准化路径: resumes\人力资源主管\刘春秀_人力资源主管_20250901_105417.png
2025-09-01 10:56:26 - resume_collector - INFO - 原始路径: resumes/人力资源主管/刘丹_人力资源主管_20250901_105318.png
2025-09-01 10:56:26 - resume_collector - INFO - 解码后路径: resumes\人力资源主管\刘丹_人力资源主管_20250901_105318.png
2025-09-01 10:56:26 - resume_collector - INFO - 标准化路径: resumes\人力资源主管\刘丹_人力资源主管_20250901_105318.png
2025-09-01 10:56:26 - resume_collector - INFO - 原始路径: resumes/人力资源主管/范女士_人力资源主管_20250901_105221.png
2025-09-01 10:56:26 - resume_collector - INFO - 解码后路径: resumes\人力资源主管\范女士_人力资源主管_20250901_105221.png
2025-09-01 10:56:26 - resume_collector - INFO - 标准化路径: resumes\人力资源主管\范女士_人力资源主管_20250901_105221.png
2025-09-01 10:56:26 - resume_collector - INFO - 原始路径: resumes/人力资源主管/王相元_人力资源主管_20250825_205946.png
2025-09-01 10:56:26 - resume_collector - INFO - 解码后路径: resumes\人力资源主管\王相元_人力资源主管_20250825_205946.png
2025-09-01 10:56:26 - resume_collector - INFO - 标准化路径: resumes\人力资源主管\王相元_人力资源主管_20250825_205946.png
2025-09-01 10:56:26 - resume_collector - INFO - 原始路径: resumes/人力资源主管/梁滴翠_人力资源主管_20250825_205844.png
2025-09-01 10:56:26 - resume_collector - INFO - 解码后路径: resumes\人力资源主管\梁滴翠_人力资源主管_20250825_205844.png
2025-09-01 10:56:26 - resume_collector - INFO - 标准化路径: resumes\人力资源主管\梁滴翠_人力资源主管_20250825_205844.png
2025-09-01 10:56:26 - resume_collector - INFO - 原始路径: resumes/人力资源主管/林小姐_人力资源主管_20250825_205744.png
2025-09-01 10:56:26 - resume_collector - INFO - 解码后路径: resumes\人力资源主管\林小姐_人力资源主管_20250825_205744.png
2025-09-01 10:56:26 - resume_collector - INFO - 标准化路径: resumes\人力资源主管\林小姐_人力资源主管_20250825_205744.png
2025-09-01 10:56:26 - resume_collector - INFO - 原始路径: resumes/人力资源主管/郭女士_人力资源主管_20250825_205648.png
2025-09-01 10:56:26 - resume_collector - INFO - 解码后路径: resumes\人力资源主管\郭女士_人力资源主管_20250825_205648.png
2025-09-01 10:56:26 - resume_collector - INFO - 标准化路径: resumes\人力资源主管\郭女士_人力资源主管_20250825_205648.png
2025-09-01 10:56:26 - resume_collector - INFO - 原始路径: resumes/人力资源主管/王威_人力资源主管_20250825_205547.png
2025-09-01 10:56:26 - resume_collector - INFO - 解码后路径: resumes\人力资源主管\王威_人力资源主管_20250825_205547.png
2025-09-01 10:56:26 - resume_collector - INFO - 标准化路径: resumes\人力资源主管\王威_人力资源主管_20250825_205547.png
2025-09-01 10:56:27 - resume_collector - INFO - 开始处理第 14 份简历
2025-09-01 10:56:27 - resume_collector - INFO - === 开始处理第 14 个简历 ===
2025-09-01 10:56:27 - resume_collector - WARNING - 第 14 份简历采集失败: 获取用户姓名失败
2025-09-01 10:56:27 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:56:33 - resume_collector - INFO - 开始处理第 15 份简历
2025-09-01 10:56:33 - resume_collector - INFO - === 开始处理第 15 个简历 ===
2025-09-01 10:56:33 - resume_collector - WARNING - 第 15 份简历采集失败: 获取用户姓名失败
2025-09-01 10:56:33 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:56:40 - resume_collector - INFO - 开始处理第 16 份简历
2025-09-01 10:56:40 - resume_collector - INFO - === 开始处理第 16 个简历 ===
2025-09-01 10:56:40 - resume_collector - WARNING - 第 16 份简历采集失败: 获取用户姓名失败
2025-09-01 10:56:40 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:56:46 - resume_collector - INFO - 开始处理第 17 份简历
2025-09-01 10:56:46 - resume_collector - INFO - === 开始处理第 17 个简历 ===
2025-09-01 10:56:46 - resume_collector - WARNING - 第 17 份简历采集失败: 获取用户姓名失败
2025-09-01 10:56:46 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:56:52 - resume_collector - INFO - 开始处理第 18 份简历
2025-09-01 10:56:52 - resume_collector - INFO - === 开始处理第 18 个简历 ===
2025-09-01 10:56:52 - resume_collector - WARNING - 第 18 份简历采集失败: 获取用户姓名失败
2025-09-01 10:56:52 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:56:58 - resume_collector - INFO - 开始处理第 19 份简历
2025-09-01 10:56:58 - resume_collector - INFO - === 开始处理第 19 个简历 ===
2025-09-01 10:56:58 - resume_collector - WARNING - 第 19 份简历采集失败: 获取用户姓名失败
2025-09-01 10:56:58 - resume_collector - INFO - 等待处理下一份简历...
2025-09-01 10:57:04 - resume_collector - INFO - 开始处理第 20 份简历
2025-09-01 10:57:04 - resume_collector - INFO - === 开始处理第 20 个简历 ===
2025-09-01 10:57:04 - resume_collector - WARNING - 第 20 份简历采集失败: 获取用户姓名失败
2025-09-01 10:57:04 - resume_collector - INFO - === 采集完成 ===
2025-09-01 10:57:04 - resume_collector - INFO - 总共计划采集: 20 份
2025-09-01 10:57:04 - resume_collector - INFO - 成功采集: 1 份
2025-09-01 10:57:04 - resume_collector - INFO - 失败: 19 份
2025-09-01 10:57:04 - resume_collector - INFO - 已删除临时文件夹: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\temp_screenshots\AI产品经理
2025-09-01 10:57:04 - resume_collector - INFO - 收集任务完成: task_1, 成功收集 1 份简历
2025-09-01 15:21:46 - resume_collector - INFO - 开始检查岗位匹配，目标岗位: AI产品经理
2025-09-01 15:21:46 - resume_collector - INFO - 正在初始化浏览器...
2025-09-01 15:21:49 - resume_collector - INFO - 浏览器初始化成功，等待页面加载...
2025-09-01 15:21:54 - resume_collector - INFO - 使用DrissionPage XPath获取岗位信息: x://*[@id="headerWrap"]/div/div/div[2]/div[1]
2025-09-01 15:21:54 - resume_collector - INFO - 成功获取岗位信息: 'AI全栈工程师 _ 珠海 15-20K'
2025-09-01 15:21:54 - resume_collector - INFO - 获取到岗位推荐信息: 'AI全栈工程师 _ 珠海 15-20K'
2025-09-01 15:21:54 - resume_collector - INFO - 使用下划线分割提取的岗位名称: 'AI全栈工程师'
2025-09-01 15:21:54 - resume_collector - INFO - 匹配比较: 目标岗位='ai产品经理' vs 当前岗位='ai全栈工程师'
2025-09-01 15:21:54 - resume_collector - INFO - 匹配结果: False, 原因: 
2025-09-01 15:21:54 - resume_collector - WARNING - 岗位不匹配: 'AI全栈工程师' 不匹配 'AI产品经理'
2025-09-01 15:21:54 - resume_collector - INFO - 开始自动搜索匹配的岗位...
2025-09-01 15:21:54 - resume_collector - INFO - 开始搜索岗位: AI产品经理
2025-09-01 15:21:54 - resume_collector - INFO - 成功点击岗位下拉框
2025-09-01 15:21:55 - resume_collector - INFO - 成功输入岗位名称: AI产品经理
2025-09-01 15:22:07 - resume_collector - INFO - 找到匹配选项: AI产品经理 _ 珠海 14-20K
2025-09-01 15:22:07 - resume_collector - INFO - 成功选择岗位: AI产品经理 _ 珠海 14-20K
2025-09-01 15:22:11 - resume_collector - INFO - 重新验证岗位匹配...
2025-09-01 15:22:13 - resume_collector - INFO - 重新验证 - 获取到岗位推荐信息: 'AI产品经理 _ 珠海 14-20K'
2025-09-01 15:22:13 - resume_collector - INFO - 重新验证结果: True, 当前岗位: AI产品经理
2025-09-01 15:22:13 - resume_collector - INFO - 岗位匹配成功，保留浏览器用于采集
2025-09-01 15:22:13 - resume_collector - INFO - 岗位匹配成功，保存浏览器实例供采集使用
2025-09-01 15:22:13 - resume_collector - INFO - 检测到浏览器已初始化标志，使用保存的浏览器实例
2025-09-01 15:22:13 - resume_collector - INFO - 开始执行收集任务: task_1
2025-09-01 15:22:13 - resume_collector - INFO - === 简历自动采集系统 ===
2025-09-01 15:22:13 - resume_collector - INFO - 将采集 1 份简历
2025-09-01 15:22:13 - resume_collector - INFO - 职位: AI产品经理
2025-09-01 15:22:13 - resume_collector - INFO - 检测到浏览器已初始化标志，跳过浏览器初始化，直接开始采集...
2025-09-01 15:22:13 - resume_collector - INFO - 创建临时截图文件夹: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\temp_screenshots\AI产品经理
2025-09-01 15:22:13 - resume_collector - INFO - 开始采集前检查并关闭弹窗...
2025-09-01 15:22:24 - resume_collector - INFO - 开始处理第 1 份简历
2025-09-01 15:22:24 - resume_collector - INFO - === 开始处理第 1 个简历 ===
2025-09-01 15:22:29 - resume_collector - INFO - 找到canvas元素，开始滚动截图...
2025-09-01 15:22:35 - resume_collector - INFO - 简历图片已保存到: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\resumes\AI产品经理\叶利广_AI产品经理_20250901_152235.png
2025-09-01 15:22:47 - resume_collector - INFO - === 第 1 个简历处理完成 ===
2025-09-01 15:22:47 - resume_collector - INFO - 成功采集第 1 份简历: 叶利广
2025-09-01 15:22:47 - resume_collector - INFO - === 采集完成 ===
2025-09-01 15:22:47 - resume_collector - INFO - 总共计划采集: 1 份
2025-09-01 15:22:47 - resume_collector - INFO - 成功采集: 1 份
2025-09-01 15:22:47 - resume_collector - INFO - 失败: 0 份
2025-09-01 15:22:47 - resume_collector - INFO - 已删除临时文件夹: D:\project\智能直聘系统_v2.0.0\智能招聘系统_version1.0.2 - 简化\high_rpa_boss\temp_screenshots\AI产品经理
2025-09-01 15:22:47 - resume_collector - INFO - 收集任务完成: task_1, 成功收集 1 份简历
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/叶利广_AI产品经理_20250901_152235.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\叶利广_AI产品经理_20250901_152235.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\叶利广_AI产品经理_20250901_152235.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/姚博_AI产品经理_20250901_105002.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\姚博_AI产品经理_20250901_105002.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\姚博_AI产品经理_20250901_105002.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/林先生_AI产品经理_20250827_162700.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\林先生_AI产品经理_20250827_162700.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\林先生_AI产品经理_20250827_162700.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/张轩颖_AI产品经理_20250827_162556.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\张轩颖_AI产品经理_20250827_162556.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\张轩颖_AI产品经理_20250827_162556.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/叶利广_AI产品经理_20250827_162457.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\叶利广_AI产品经理_20250827_162457.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\叶利广_AI产品经理_20250827_162457.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/谭文杰_AI产品经理_20250827_162357.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\谭文杰_AI产品经理_20250827_162357.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\谭文杰_AI产品经理_20250827_162357.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/丘伟航_AI产品经理_20250827_162258.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\丘伟航_AI产品经理_20250827_162258.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\丘伟航_AI产品经理_20250827_162258.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/潘玉玲_AI产品经理_20250827_162151.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\潘玉玲_AI产品经理_20250827_162151.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\潘玉玲_AI产品经理_20250827_162151.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/丁嘉亮_AI产品经理_20250827_162012.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\丁嘉亮_AI产品经理_20250827_162012.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\丁嘉亮_AI产品经理_20250827_162012.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/杨志海_AI产品经理_20250827_161912.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\杨志海_AI产品经理_20250827_161912.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\杨志海_AI产品经理_20250827_161912.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/林丽丽_AI产品经理_20250827_161811.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\林丽丽_AI产品经理_20250827_161811.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\林丽丽_AI产品经理_20250827_161811.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/姚博_AI产品经理_20250827_161710.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\姚博_AI产品经理_20250827_161710.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\姚博_AI产品经理_20250827_161710.png
2025-09-01 15:22:56 - resume_collector - INFO - 原始路径: resumes/AI产品经理/刘俊锋_AI产品经理_20250825_203837.png
2025-09-01 15:22:56 - resume_collector - INFO - 解码后路径: resumes\AI产品经理\刘俊锋_AI产品经理_20250825_203837.png
2025-09-01 15:22:56 - resume_collector - INFO - 标准化路径: resumes\AI产品经理\刘俊锋_AI产品经理_20250825_203837.png
