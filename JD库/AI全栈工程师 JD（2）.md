# AI全栈工程师

## 岗位职责

1. 从零设计并实现完整的Agent框架架构，包括任务规划器、工具调用引擎、记忆系统、多轮对话管理等核心组件

2. 主导开发Agent的核心能力，包括RAG检索增强、function calling、代码执行、文件处理、联网搜索等工具集成，打造可扩展的能力生态

3. 负责核心技术选型决策，集成多种大模型API（OpenAI、Claude、国产模型），设计统一的模型管理层，实现模型切换、成本控制、性能监控等关键能力

4. 建立完整的开发、测试、部署流程，制定代码规范和技术文档标准

## 任职要求

### 必备技能

1. 深入理解ReAct、Planning、Tool Use等Agent核心算法，熟悉LangChain、CrewAI、AutoGPT等框架源码，能基于业务需求设计定制化Agent架构

2. 精通Python，熟悉FastAPI/Flask、异步编程、数据库设计，具备API设计、微服务架构、容器化部署等完整的后端开发能力

3. 深度使用过OpenAI、Anthropic、国产大模型API，熟悉prompt工程、token优化、并发控制、错误处理，有成本控制和性能优化经验

4. 有从零搭建复杂系统的完整经验，具备技术选型、架构设计、团队协作能力，能独立承担核心技术决策责任

### 优先技能

1. 熟悉prompt工程、token优化

2. 指导团队技术选型，推动代码质量和工程效率提升

3. 有大规模API调用和并发处理的工程经验

4. 了解模型部署、推理优化

### 加分项

1. 有过完整的Agent产品开发经验，具备端到端的项目实施能力

2. 参与过从0到1的产品开发，具备快速迭代和敏捷开发经验

4. 跟踪AI前沿技术趋势

