# 大模型算法工程师

## 岗位职责

1. 负责大规模语言模型的训练、微调与优化，提升模型性能和泛化能力，支撑业务场景落地。
2. 研究并应用模型压缩、量化、蒸馏等关键技术，降低模型部署成本，提升推理效率。
3. 实现模型推理加速与部署，提升服务性能，确保模型在生产环境的稳定运行。
4. 构建高质量训练数据集及制定数据增强策略，提升模型训练质量和效果。

## 任职要求

### 必备技能

1. 精通大规模语言模型的训练与调优技巧，具备扎实的算法工程能力。
2. 具备将大模型部署到生产环境的经验，熟悉模型部署流程和相关工具。
3. 熟悉模型压缩、量化、蒸馏等关键技术，能独立完成模型优化工作。
4. 具备良好的算法工程化能力，能高效实现模型训练与推理优化。

### 优先技能

1. 熟悉分布式训练与GPU集群优化，具备大规模模型训练经验。
2. 掌握模型服务化部署工具（如TensorRT、ONNX Runtime、Triton），能完成模型服务部署与调优。
3. 具备大模型推理优化经验（如KV Cache优化、并行生成等），有实际项目落地能力。

### 加分项

1. 曾主导或深度参与大规模模型训练与落地项目，具备完整项目经验。
2. 有知名开源项目贡献经历（如HuggingFace、Transformers），具有良好的技术影响力。
3. 具备良好的英文读写能力，能跟进英文技术文档与论文，持续学习最新研究成果。

## 我们提供

1. 有竞争力的薪资待遇（15-20K）和完善的福利体系
2. 良好的发展空间和学习成长机会，参与前沿大模型项目
3. 位于广东珠海的现代化办公环境，鼓励技术创新与团队协作
4. 专业发展支持，包括技术培训、学术交流、项目资源倾斜等

**基本信息**

- 学历要求：专科及以上
- 工作经验：1-3年相关经验
- 薪资范围：15-20K·月结
- 工作地点：广东珠海
- 工作方式：现场办公
- 年龄要求：35岁以下
- 专业要求：人工智能、计算机、数学等相关专业

---

✅ **质量检查清单确认**：

- ✅ 职责描述清晰、聚焦业务价值，每条职责不超过两句话
- ✅ 技能分类明确，必备技能控制在4项以内，优先技能和加分项合理区分
- ✅ 没有重复要求（如学历、经验等只出现在“基本信息”）
- ✅ 技能描述避免模糊词汇（如“了解”“熟悉”“掌握”明确区分优先级）
- ✅ 岗位通用技能与定制化技能平衡良好
- ✅ 未设置不合理的一票否决条件
- ✅ JD总字数控制在800字以内，结构清晰、便于HR使用

是否需要对JD进行微调？我可以根据您的反馈进一步优化。