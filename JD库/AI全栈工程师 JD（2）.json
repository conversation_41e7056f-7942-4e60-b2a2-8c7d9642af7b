{"job_name": "AI全栈工程师", "job_core_responsibility": ["// 从零设计并实现一个可扩展的AI Agent框架架构，涵盖任务规划、工具调用、记忆管理与多轮对话等核心能力模块。", "// 主导Agent核心功能的技术实现与系统集成，包括RAG、function calling、代码执行、文件处理与联网搜索，构建统一的能力生态。", "// 负责多大模型API的统一接入与管理层设计，实现模型灵活切换、成本控制与性能监控，支撑高可用、高性能的AI服务。"], "core_hard_skills": ["// Python（作为Agent框架与后端服务的实现语言，是整个系统的基础）", "// Agent架构设计能力（必须理解ReAct、Planning、Tool Use等机制，并能自主设计非依赖框架的定制化架构）", "// 大模型API集成经验（必须深度使用OpenAI、Claude、国产模型API，实现统一管理层，否则无法完成核心职责）", "// RAG与function calling实现能力（作为Agent核心能力，缺失则无法构建智能体的外部交互与知识扩展）", "// FastAPI/Flask与异步编程（支撑高并发API服务与工具调用引擎的实时响应，是系统性能的关键基石）"], "abstract_capabilities": ["// 从0到1复杂系统构建的架构思维与技术决策能力", "// 技术前瞻性与AI领域持续学习能力", "// 工程化思维：代码规范、文档标准、可维护性设计", "// 跨模块系统整合与抽象设计能力"], "job_bonus": ["1. 熟悉prompt工程、token优化", "2. 指导团队技术选型，推动代码质量和工程效率提升", "3. 有大规模API调用和并发处理的工程经验", "4. 了解模型部署、推理优化", "1. 有过完整的Agent产品开发经验，具备端到端的项目实施能力", "2. 参与过从0到1的产品开发，具备快速迭代和敏捷开发经验", "4. 跟踪AI前沿技术趋势"]}