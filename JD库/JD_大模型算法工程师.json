{"job_name": "大模型算法工程师", "salary_range": "15-20K·月结", "education_requirement": "专科及以上", "age_range": "35岁以下", "job_core_responsibility": ["负责大规模语言模型的训练、微调与优化，提升模型性能和泛化能力，支撑业务场景落地", "研究并应用模型压缩、量化、蒸馏等关键技术，降低模型部署成本，提升推理效率", "实现模型推理加速与部署，提升服务性能，确保模型在生产环境的稳定运行"], "core_hard_skills": ["大规模语言模型训练与调优", "模型部署与服务化工具", "模型压缩与量化技术", "算法工程化实现能力"], "abstract_capabilities": ["复杂问题拆解能力", "技术自驱力与研究落地能力", "持续学习与工程实践结合能力"], "job_bonus": ["熟悉分布式训练与GPU集群优化，具备大规模模型训练经验", "掌握模型服务化部署工具（如TensorRT、ONNX Runtime、Triton），能完成模型服务部署与调优", "具备大模型推理优化经验（如KV Cache优化、并行生成等），有实际项目落地能力", "曾主导或深度参与大规模模型训练与落地项目，具备完整项目经验", "有知名开源项目贡献经历（如HuggingFace、Transformers），具有良好的技术影响力", "具备良好的英文读写能力，能跟进英文技术文档与论文，持续学习最新研究成果"]}