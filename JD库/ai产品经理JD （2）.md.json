{"job_name": "AI产品经理", "job_core_responsibility": ["主导AI智能体产品的全周期规划、设计与落地，确保产品具备市场竞争力与技术可行性。", "在技术与业务之间架设桥梁，精准将算法能力转化为用户价值与产品功能。", "构建AI产品的评估体系，通过数据驱动方式持续优化模型表现与用户体验。"], "core_hard_skills": ["大型语言模型（LLM）的工作原理与能力边界", "Prompt工程与模型微调策略", "Agent架构理解与技术挑战识别能力（如幻觉、延迟、可靠性）", "AI产品交互设计能力（如对话流程、提示词模板、错误处理机制）"], "abstract_capabilities": ["技术与业务之间的翻译与沟通能力", "复杂AI问题的产品化抽象能力", "市场趋势洞察与竞品分析能力", "数据驱动的决策与优化思维"], "job_bonus": ["理解智能体核心概念与技术栈，以及主流Agent框架（如LangChain, AutoGen, LlamaIndex）的设计理念和应用场景", "有实际的AI相关项目管理或产品设计经验", "具备数据分析能力，能通过用户行为数据优化产品功能", "有大模型/AI Agent开发经验", "有0-1产品从无到有的完整经历，具备创业公司工作经验", "计算机科学、人工智能、机器学习等相关专业硕士或以上学历。有算法开发、软件工程或数据科学背景者优先"]}