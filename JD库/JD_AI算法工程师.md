# AI算法工程师

## 岗位职责

1. 构建推荐系统或自然语言处理模块，提升产品智能化水平，通过算法优化增强用户体验和产品竞争力。  
2. 设计并实现图像识别或计算机视觉相关算法，推动技术在实际业务场景中的落地与应用。  
3. 与产品和工程团队协作，完成AI模块的集成与优化，确保模型高效稳定运行。  
4. 编写相关技术文档，并进行算法模块的测试与验证，保障系统的可维护性与可扩展性。

## 任职要求

### 必备技能

1. 熟练掌握Python编程语言，具备扎实的算法实现能力。  
2. 熟悉PyTorch或TensorFlow等深度学习框架，能独立进行模型训练与调优。  
3. 有推荐系统或NLP相关开发经验，理解业务场景下的算法应用逻辑。  
4. 能够独立完成模型训练、调优与评估，具备完整的技术闭环能力。

### 优先技能

1. 掌握模型压缩与加速技术（如模型量化、剪枝、知识蒸馏），提升模型推理效率。  
2. 熟悉模型部署与推理优化工具（如TensorRT、ONNX、Triton），具备工程化落地经验。  
3. 熟悉分布式训练和大规模数据处理（如Spark、Hadoop），有大规模数据建模经验者优先。

### 加分项

1. 在AI顶会（如NeurIPS、ICML、CVPR）发表论文，体现深厚的研究能力。  
2. 顶尖高校硕士及以上学历（如985、QS前100院校），具备扎实的理论基础。  
3. 有大厂AI项目经验或开源项目贡献者优先，体现实际工程与协作能力。

## 我们提供

1. 有竞争力的薪资待遇：20-40K·15薪，根据能力面议。  
2. 良好的发展空间和学习成长机会，参与前沿AI项目研发。  
3. 弹性工作制度与开放的技术氛围，支持技术创新与探索。  
4. 完善的福利体系，包括五险一金、年度体检、节日福利等。

**基本信息**

- 学历要求：硕士及以上  
- 工作经验：3-5年相关经验  
- 薪资范围：20-40K·15薪  
- 工作地点：北京  
- 工作方式：现场办公（可弹性）