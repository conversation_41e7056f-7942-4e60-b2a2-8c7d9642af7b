{"job_name": "AI产品经理", "salary_range": "15-20K·13薪", "education_requirement": "专科及以上", "age_range": "35岁以下", "job_core_responsibility": ["设计和定义AI产品的功能与用户体验，确保产品具备高可用性和技术可行性", "制定产品路线图与优先级，推动AI产品持续迭代与优化", "协调跨职能团队推进AI产品的开发与落地", "制定AI产品的市场定位与竞争策略，打造差异化产品竞争力"], "core_hard_skills": ["AI/机器学习基本原理", "技术可行性判断能力", "用户需求调研能力", "产品需求文档（PRD）撰写能力", "产品路线图规划与迭代管理方法", "Axure/Figma原型设计工具"], "abstract_capabilities": ["跨职能团队协作能力", "需求分析与优先级判断能力", "技术理解与产品落地的平衡能力", "市场洞察与产品策略制定能力"], "job_bonus": ["熟悉TensorFlow/PyTorch等深度学习框架，理解AI模型训练与部署流程", "掌握Jira/TAPD等项目管理工具，具备良好的项目推进与进度管理能力", "具备基础代码能力（如Python、SQL），能与开发团队协同调试与验证功能", "在AI相关领域发表过论文或拥有专利，体现深度技术理解与创新能力", "拥有AI产品经理相关认证（如PMI-ACP、PMP、AI产品管理培训证书等）", "曾主导过从0到1的AI产品落地项目并取得商业化成果，具备完整产品生命周期管理经验"]}