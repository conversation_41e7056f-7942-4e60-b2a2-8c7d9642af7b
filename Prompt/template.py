system_round1 = """
评估准则:
{eval_prompt}

岗位JD：
{jd_content}
请严格按照评估标准进行评估，并给出详细的评估报告。
"""

user_round1 = """
简历内容：
{resume_text}

请严格按照评估标准进行评估，并给出详细的评估报告。
重要提示：严格遵守提示词中的输出格式要求，不要添加任何未在提示词中明确要求的内容（如评分、关键词匹配表格等）。"""

# ==========================================================================


system_round2 = """
评估准则:
{eval_prompt}

请根据上述简历内容，进行详细的专业身份识别和能力边界分析。
重要提示：
1. 严格遵守提示词中的输出格式要求
2. 重点关注工作内容分析和技能应用证据
3. 基于描述完整度、关键词密度、应用证据和职责核心度进行专业比重评估
4. 明确区分主要专业身份(80-100分)、重要辅助技能(60-79分)和边缘接触能力(40-59分)
5. 清晰识别候选人的专业能力边界和职业发展轨迹
6. 最终给出一句话专业身份定位和可信度评估"""

user_round2 = """
简历内容：
{resume_text}


"""
# ==========================================================================

system_round2_eval = """
评估准则:
{eval_prompt}

岗位JD：
{jd_content}

请根据上述简历内容，进行详细的专业身份识别和能力边界分析。
重要提示：
1. 严格遵守提示词中的输出格式要求
2. 重点关注工作内容分析和技能应用证据
3. 基于描述完整度、关键词密度、应用证据和职责核心度进行专业比重评估
4. 明确区分主要专业身份(80-100分)、重要辅助技能(60-79分)和边缘接触能力(40-59分)
5. 清晰识别候选人的专业能力边界和职业发展轨迹
6. 最终给出一句话专业身份定位和可信度评估"""

# ==========================================================================
system_round2_5 = """{matching_prompt}"""
user_round2_5 = """## 输入数据

### 候选人专业画像
{profile_report}

### 岗位名称
{jd_title}

请根据上述信息，按照提示词要求，评估候选人专业画像与岗位名称的匹配度。"""

# ==========================================================================

system_round3 = """
评估准则:
{eval_prompt}

岗位JD：
{jd_content}

请严格按照评估标准进行评估，并给出详细的评估报告。
"""

# 这是一份报告的示例，请参考其打分的思路以及严格程度:
# {example_resume_report}

user_round3 = """
简历内容：
{resume_text}


请根据上述简历内容，按照项目导向简历评估系统的要求，进行详细的项目经验匹配、核心能力应用和专业背景匹配评估。
重要提示：
1. 严格遵守提示词中的输出格式要求
2. 基于证据导向原则，评分必须基于简历中的具体描述
3. 项目经验是最重要的评估依据
4. 使用明确的分数对应标准，避免主观判断
5. 请确保评估报告格式符合标准化输出格式要求

"""

TEMPLATES = {
    "system_round1": system_round1,
    "user_round1": user_round1,
    "system_round2": system_round2,
    "user_round2": user_round2,
    "system_round3": system_round3,
    "user_round3": user_round3,
    "system_round2_5": system_round2_5,
    "user_round2_5": user_round2_5,
    "system_round2_eval": system_round2_eval,
    "user_round2_eval": user_round2
}
