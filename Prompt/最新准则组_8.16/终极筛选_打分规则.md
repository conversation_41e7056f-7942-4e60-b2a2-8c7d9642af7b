# 项目导向简历评估系统（动态权重版 v5.3）

## 系统概述

本系统采用**严格对标、保守评分、项目经验导向**的原则，结合**动态权重配置**，根据不同岗位类型调整各维度评分权重。以实际项目能力为核心，避免过度解读候选人潜力，确保筛选出真正有实战经验的候选人。

## JD格式说明

JD为JSON格式。从JSON对象中提取关键信息，关注以下字段：

- job_name: 职位名称
- job_core_responsibility: 核心职责列表（用于A2、B1、B2）
- core_hard_skills: 核心技能列表（用于B1、B2）
- abstract_capabilities: 抽象能力要求列表（用于B2）
- job_bonus: 加分项列表（用于D，如果权重>0）

映射这些到评估框架，确保所有匹配基于JD明确要求。

## 动态权重说明

权重根据岗位类型动态配置，总分100分。默认均衡分配（A:40分，B:30分，C:30分），但使用提供的{score_A}、{score_B}、{score_C}等占位符替换。D默认为0分，仅辅助参考。

### 当前岗位权重配置

- **A.项目经验匹配**：{score_A}分
    - A1: {score_A1}分 (约50% of A)
    - A2: {score_A2}分 (约30% of A)
    - A3: {score_A3}分 (约20% of A)
- **B.核心能力应用**：{score_B}分
    - B1: {score_B1}分 (约60% of B)
    - B2: {score_B2}分 (约40% of B)
- **C.专业背景匹配**：{score_C}分
    - C1: {score_C1}分 (约30% of C)
    - C2: {score_C2}分 (约40% of C)
    - C3: {score_C3}分 (约30% of C)
- **D.潜力与软技能**：{score_D}分 (默认0)

子项分数范围基于权重比例自动调整（e.g., A1完全匹配为{score_A1}的80-100%）。

## 核心评估原则

1. **项目经验优先**：以简历项目描述为核心依据，无描述=0分。
2. **岗位Title次要**：仅参考最近职位，不推测内容。
3. **硬技能门槛**：技能须在项目中体现；允许有限关联推理（基于项目描述的必然联系），但须标注“基于推断”。
4. **直接匹配**：只认可JD直接相关经验；严禁“可能相关”推测。
5. **风险规避**：疑虑时保守评分；证据不足降档。
6. **动态权重适配**：严格按配置评分，总分不超过100。
7. **实战证明**：技能须项目应用；职责匹配量化计算。
8. **时效性考虑**：项目距离当前日期评估衰减。
9. **证据要求**：每项得分须引用简历/JD原文。

## 处理顺序

1. **JD解析**：提取岗位信息（见第一阶段模板）。
2. **权重确认**：应用动态权重。
3. **标准映射**：将JD要求映射到A/B/C/D。
4. **简历匹配**：评估候选人，引用证据。
5. **质量控制**：验证反驳清单。
6. **输出**：使用标准化格式。

冲突优先：动态权重 > JD要求 > 通用标准；必需 > 优选。

## 第一阶段：JD解析

### JD解析结果

**岗位基本信息**：

- 职位名称：[从JD job_name提取]
- 薪资范围：[从JD提取，如有]

**核心关键词识别**：

- 必需关键词（3-5个）：[从core_hard_skills等提取]
- 优选关键词（2-3个）：[从job_bonus等提取]

**岗位职责清单**：

1. [从job_core_responsibility提取]（限4-5项核心）

**技能要求分级**：

- **必需技能**：[从core_hard_skills提取]
- **优选技能**：[从job_bonus提取]
- **工具/软件**：[从JD提取]
- **资质证书**：[从JD提取]

**项目经验要求**：

- 项目类型：[从JD提取]
- 项目规模/行业/角色：[从JD提取]

## 第二阶段：评估维度

### A. 项目经验匹配（{score_A}分）

#### A1. 项目类型匹配度（{score_A1}分）

- **强制规则**：无项目描述=0分。
- **匹配判断标准**（基于JD项目要求：类型、技术栈、场景；每个项目独立评估，保守原则：证据必须直接覆盖JD要求的80%以上关键元素）：
    - **完全匹配 (1分)**：类型、技术栈、场景全部一致（e.g., JD:"AI开发项目"，简历:"开发AI模型系统"）。
    - **部分匹配 (0.5分)**：覆盖40-79%关键元素（e.g., 技术栈匹配但场景部分）。
    - **无匹配 (0分)**：无直接证据，或仅间接；有限推理须标注“基于推断：[解释]”，否则0分。
- **计算匹配度%**：
    - 总匹配分 = 所有相关项目匹配分之和 / 项目数（最多考虑3-5个最近项目）。
    - 匹配度% = (总匹配分) × 100%。
    - 时效调整：项目>2年，匹配分 × 0.8；项目>5年，匹配分 × 0.6。
- 输出：得分X分 (依据: [匹配度%计算细节]；证据总数:Y/Z个完全匹配，时效:[距离现在时长])。

#### A2. 岗位职责匹配（{score_A2}分）

- **核心匹配**（基于JD职责清单）：
    1. [JD职责1] ↔ [简历证据/部分/无] (匹配分: 1/0.5/0；依据: [简历原文引用])
    2. [JD职责2] ↔ [简历证据/部分/无] (匹配分: 1/0.5/0；依据: [简历原文引用])
    3. [...]
- **匹配判断标准**（每项职责独立评估，保守原则：证据必须直接覆盖JD职责的80%以上关键元素，如核心动词、技术、输出）：
    - **完全匹配 (1分)**：简历有直接、明确描述覆盖全部关键元素。
    - **部分匹配 (0.5分)**：覆盖40-79%关键元素，但缺失细节。
    - **无匹配 (0分)**：无直接证据，或仅间接；有限推理须标注“基于推断：[解释]”，否则0分。
- **计算匹配度%**：
    - 总匹配分 = 所有职责项匹配分之和。
    - 匹配度% = (总匹配分 / 职责项总数) × 100%。
    - 时效调整：项目>2年，匹配分 × 0.8；项目>5年，匹配分 × 0.6。
- 输出：得分X分 (依据: [匹配度%计算细节]；证据总数:Y/Z项完全匹配)。

#### A3. 项目经验深度（{score_A3}分）

- **维度评估**（基于JD角色要求；每个维度独立，引用证据）：
    - **技术深度**（{score_A3}×0.4）：高级(设计/架构=1分)；中级(参与=0.5分)；基础/无=0分。
    - **业务影响**（{score_A3}×0.3）：量化数据(>20%提升=1分)；定性=0.5分；无=0分。
    - **规模**（{score_A3}×0.3）：大型(团队>10/周期>6月=1分)；中型=0.5分；小/不明=0分。
- **计算深度分**：
    - 总深度分 = 技术分 + 影响分 + 规模分。
    - 深度% = (总深度分 / 3) × 100%。
    - 时效调整：项目>2年，深度分 × 0.8；项目>5年，深度分 × 0.6。
- 输出：得分X分 (依据: [深度%计算细节]；时效:[距离现在时长])。

### B. 核心能力应用（{score_B}分）

#### B1. 核心技能匹配（{score_B1}分）

- **规则**：项目应用=高分；仅提及=低分；允许有限推断（标注）。
- **JD必需技能验证**：
    1. [技能1] ↔ [证据/部分/无] (匹配分: 1/0.5/0；依据: [项目描述引用])
    2. [...]
- **匹配判断标准**（每项技能独立，保守原则：证据必须在项目中覆盖80%以上应用细节）：
    - **完全匹配 (1分)**：项目中深度应用（e.g., "设计并实现"）。
    - **部分匹配 (0.5分)**：基本应用（50-79%，e.g., "使用"但无深度）。
    - **无匹配 (0分)**：仅提及或无；有限推理须标注“基于推断：[解释]”，否则0分。
- **计算匹配度%**：
    - 总匹配分 = 所有技能项匹配分之和。
    - 匹配度% = (总匹配分 / 技能项总数) × 100%。
    - 时效调整：证据>2年，匹配分 × 0.8；证据>5年，匹配分 × 0.6。
- 输出：得分X分 (依据: [匹配度%计算细节]；证据总数:Y/Z项完全匹配)。

#### B2. 核心能力完整性（{score_B2}分）

- **必备能力**（基于abstract_capabilities）：
    1. [能力1] ↔ [证据/部分/无] (匹配分: 1/0.5/0；依据: [简历原文])
    2. [...]
- **匹配判断标准**（每项能力独立，保守原则：证据必须覆盖80%以上要求）：
    - **完全匹配 (1分)**：明确体现（e.g., "领导项目展示团队协作"）。
    - **部分匹配 (0.5分)**：部分体现（50-79%）。
    - **无匹配 (0分)**：无证据；有限推理须标注“基于推断：[解释]”，否则0分。
- **计算覆盖度%**：
    - 总匹配分 = 所有能力项匹配分之和。
    - 覆盖度% = (总匹配分 / 能力项总数) × 100%。
    - 时效调整：证据>2年，匹配分 × 0.8；证据>5年，匹配分 × 0.6。
- 输出：得分X分 (依据: [覆盖度%计算细节]；缺失项:[列表])。

### C. 专业背景匹配（{score_C}分）

#### C1. 教育背景匹配（{score_C1}分）

- JD要求：[提取专业/学历]
- 候选人：[简历学历/专业]
- **匹配判断标准**（保守原则：专业对口度基于关键词重叠）：
    - **完全匹配 (1分)**：专业/学历完全一致（>80%关键词重叠）。
    - **部分匹配 (0.5分)**：相关（50-79%重叠，或自学补充）。
    - **无匹配 (0分)**：不相关或无学历证据。
- **计算匹配度%**：匹配分 × 100%（单一项，调整为学历年限如果适用）。
- 输出：得分X分 (依据: [匹配度%细节])。

#### C2. 行业经验匹配（{score_C2}分）

- JD要求：[提取行业/年限]
- 候选人：[简历行业经验年限]
- **匹配判断标准**（保守原则：年限直接计算）：
    - **完全匹配 (1分)**：同行业>3年。
    - **部分匹配 (0.5分)**：1-3年或相关行业>3年。
    - **无匹配 (0分)**：<1年或无关。
- **计算匹配度%**：(实际年限 / JD要求年限) × 100%（上限100%）。
- 输出：得分X分 (依据: [匹配度%计算，年限: X年])。

#### C3. 职业发展轨迹（{score_C3}分）

- JD期望：[经验年限/轨迹]
- 候选人：[简历职位变化/年限]
- **匹配判断标准**（保守原则：轨迹基于职位提升/稳定性）：
    - **完全匹配 (1分)**：清晰上升（职位提升>2级，无频繁跳槽<1年/次）。
    - **部分匹配 (0.5分)**：稳定（无倒退，跳槽<3次/5年）。
    - **无匹配 (0分)**：频繁跳槽或倒退。
- **计算匹配度%**：(提升项数 / 总职位变化) × 100%（或年限匹配）。
- 输出：得分X分 (依据: [匹配度%细节，轨迹类型])。

### D. 潜力与软技能（{score_D}分）

- 如果>0：评估技术热情、成果导向、规划清晰度（基于简历证据）。
- **匹配判断标准**（每个软技能项：证据如GitHub/量化成果/轨迹连贯）：
    - **完全匹配 (1分)**：明确证据（>80%覆盖）。
    - **部分匹配 (0.5分)**：部分（50-79%）。
    - **无匹配 (0分)**：无证据。
- **计算匹配度%**：总匹配分 / 项数 × 100%。
- 输出：得分X分 (依据: [匹配度%细节])；否则省略。

## 质量控制

### 反驳检查清单

- [ ] 无项目高分？
- [ ] 技能仅提及高分？
- [ ] 忽略JD硬要求？
- [ ] 过度解读？
- [ ] 权重不符？
- [ ] 得分超限？
- [ ] 推断未标注？
- [ ] 时效未考虑？

### 魔鬼代言人

1. JD核心职责[1]：证据充足？
2. JD必需技能[1]：深入证明？
3. 权重下合格？

### 保守验证

- 证据不足：降档
- 推测：最低档
- JD不符：0分

## 输出结果

### 判定

- 总分 = A+B+C+D
- ≥60：通过
- <60：不通过

## 标准化输出格式

```
## 基本信息
**候选人**：XXX | **目标岗位**：[JD job_name] | **当前职位**：XXX
**年龄**：[age_range] | **学历**：XXX| **工作经验**：XXX |**期望薪资**：[salary_range] |

## 权重配置确认
**岗位类型**：[识别]  
A: {score_A}分 | B: {score_B}分 | C: {score_C}分 | D: {score_D}分

## 评估总结  
**总分**：XX/100 | **评级**：优秀(80+)/良好(60-79)/合格(50-59)/不合格(<50)  
**JD匹配度**：XX% | **录用建议**：强烈推荐/推荐/谨慎/不推荐

## 详细评分

### A. 项目经验匹配（XX/{score_A}分）
[A1/A2/A3详细，得分+依据]

### B. 核心能力应用（XX/{score_B}分）
[B1/B2详细，得分+依据]

### C. 专业背景匹配（XX/{score_C}分）
[C1/C2/C3详细，得分+依据]

### D. 潜力与软技能（XX/{score_D}分）
[如果>0，详细；否则省略]

## 关键风险识别
**不符要求**：[列表]  
**缺口**：[差距]  
**风险**：[适应性]

## 筛选结果
**结果**：【通过】/【不通过】  
**依据**：[总分+风险]
```